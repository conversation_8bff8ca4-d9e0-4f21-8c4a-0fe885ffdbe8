# 演示数据配置文件

users:
  - username: admin
    email: <EMAIL>
    password: admin123
    role: admin
  - username: operator
    email: <EMAIL>
    password: operator123
    role: user
  - username: developer
    email: <EMAIL>
    password: developer123
    role: user

service_configs:
  - name: base-infra
    description: 基础设施服务配置模板
    docker_compose: |
      version: '3'
      services:
        nginx:
          image: nginx:latest
          ports:
            - "80:80"
          volumes:
            - ./nginx.conf:/etc/nginx/nginx.conf
            - ./html:/usr/share/nginx/html

        mysql:
          image: mysql:8.0
          ports:
            - "3306:3306"
          volumes:
            - mysql_data:/var/lib/mysql

        redis:
          image: redis:6.2
          ports:
            - "6379:6379"
          command: redis-server

      volumes:
        mysql_data:


  - name: base-bot
    description: 基础机器人服务配置模板
    docker_compose: |
      version: '3'
      services:
        bot:
          image: youngking/base-bot:latest
          volumes:
            - ./config:/app/config
          restart: unless-stopped
          logging:
            driver: json-file
            options:
              max-size: "10m"
              max-file: "3"

projects:
  - name: web-portal
    description: 公司门户网站
    service_config_id: 1
    service_compose:
      services:
        nginx:
          image: nginx:latest
          environment:
            DOMAIN: "{{ domain }}"
            SSL_ENABLED: "{{ ssl_enabled }}"
          volumes:
            - ./nginx.conf:/etc/nginx/nginx.conf
            - ./html:/usr/share/nginx/html
    form_schema:
      type: object
      properties:
        domain:
          type: string
          title: 域名
        ssl_enabled:
          type: boolean
          title: 启用SSL
    files:
      - name: nginx.conf
        content: |
          events {
              worker_connections 1024;
          }
          http {
              server {
                  listen 80;
                  server_name {{ domain }};
                  location / {
                      root /usr/share/nginx/html;
                      index index.html;
                  }
              }
          }
      - name: html/index.html
        content: |
          <!DOCTYPE html>
          <html>
          <head>
              <title>Welcome</title>
          </head>
          <body>
              <h1>Welcome to our website!</h1>
          </body>
          </html>

  - name: user-db
    description: 用户数据库服务
    service_config_id: 1
    service_compose:
      services:
        mysql:
          image: mysql:8.0
          environment:
            MYSQL_DATABASE: "{{ db_name }}"
            MYSQL_ROOT_PASSWORD: "{{ root_password }}"
          volumes:
            - mysql_data:/var/lib/mysql
    form_schema:
      type: object
      properties:
        db_name:
          type: string
          title: 数据库名称
        root_password:
          type: string
          title: Root密码
          format: password
    files:
      - name: init.sql
        content: |
          CREATE TABLE users (
            id INT PRIMARY KEY AUTO_INCREMENT,
            username VARCHAR(50) NOT NULL,
            email VARCHAR(100) NOT NULL
          );

  - name: session-cache
    description: 会话缓存服务
    service_config_id: 1
    service_compose:
      services:
        redis:
          image: redis:6.2
          command: redis-server --requirepass {{ redis_password }} --maxmemory {{ max_memory }}
          volumes:
            - redis_data:/data
    form_schema:
      type: object
      properties:
        redis_password:
          type: string
          title: Redis密码
          format: password
        max_memory:
          type: string
          title: 最大内存
          default: "1gb"

  - name: bless-network
    description: Bless Network 自动挂机项目
    service_config_id: 2
    service_compose:
      services:
        bot:
          image: youngking/bless-bot:latest
          environment:
            B7S_AUTH_TOKEN: "{{ auth_token }}"
          volumes:
            - ./config:/app/config
          restart: unless-stopped
    form_schema:
      type: object
      properties:
        auth_token:
          type: string
          title: B7S_AUTH_TOKEN
          format: password
        proxy_list:
          type: string
          title: 代理列表
          format: textarea
    files:
      - name: config/nodes.txt
        content: |
          {{ proxy_list }}

  - name: dawn-farm
    description: Dawn 自动化项目
    service_config_id: 2
    service_compose:
      services:
        bot:
          image: youngking/dawn-bot:latest
          environment:
            CAPTCHA_KEY: "{{ captcha_key }}"
          volumes:
            - ./config:/app/config
          restart: unless-stopped
    form_schema:
      type: object
      properties:
        accounts:
          type: string
          title: 账号列表
          format: textarea
          description: 每行一个账号，格式：email:password
        proxies:
          type: string
          title: 代理列表
          format: textarea
          description: 每行一个代理
        captcha_key:
          type: string
          title: 2Captcha API Key
          format: password
    files:
      - name: config/farm.txt
        content: |
          {{ accounts }}
      - name: config/proxies.txt
        content: |
          {{ proxies }}
      - name: config/2captcha_api_key.txt
        content: |
          {{ captcha_key }}

  - name: pipe-network
    description: Pipe Network 自动化项目
    service_config_id: 2
    service_compose:
      services:
        bot:
          image: youngking/pipe-bot:latest
          volumes:
            - ./config:/app/config
          restart: unless-stopped
    form_schema:
      type: object
      properties:
        tokens:
          type: string
          title: Token列表
          format: textarea
          description: 每行一个token,email
        proxies:
          type: string
          title: 代理列表
          format: textarea
          description: 每行一个代理
    files:
      - name: config/token.txt
        content: |
          {{ tokens }}
      - name: config/proxy.txt
        content: |
          {{ proxies }}

  - name: openloop-auto
    description: OpenLoop 自动化项目
    service_config_id: 2
    service_compose:
      services:
        openloop:
          image: youngking/openloop-bot
          volumes:
            - ./openloop/token.txt:/app/token.txt
            - ./openloop/proxy.txt:/app/proxy.txt
          restart: unless-stopped
          container_name: openloop
    form_schema:
      type: object
      properties:
        email:
          type: string
          title: 邮箱
          format: email
        password:
          type: string
          title: 密码
          format: password
    files:
      - name: openloop/token.txt
        content: |
          {{ email }}:{{ password }}
      - name: openloop/proxy.txt
        content: |
          ***********************************************

  - name: nodepay-auto
    description: NodePay 自动化项目
    service_config_id: 2
    service_compose:
      services:
        bot:
          image: youngking/nodepay-bot:latest
          volumes:
            - ./config:/app/config
          restart: unless-stopped
    form_schema:
      type: object
      properties:
        tokens:
          type: string
          title: Token列表
          format: textarea
          description: 每行一个token
        proxies:
          type: string
          title: 代理列表
          format: textarea
          description: 每行一个代理
    files:
      - name: config/token.txt
        content: |
          {{ tokens }}
      - name: config/proxy.txt
        content: |
          {{ proxies }}

devices:
  - name: web-server-1
    description: 生产环境 Web 服务器 1
    ip_address: *************
    mac_address: 00:1A:2B:3C:4D:5E
    owner: operator
    tags: prod,web,nginx
    
  - name: web-server-2
    description: 生产环境 Web 服务器 2
    ip_address: *************
    mac_address: 00:1A:2B:3C:4D:5F
    owner: operator
    tags: prod,web,nginx

  - name: db-server-prod
    description: 生产环境数据库服务器
    ip_address: *************
    mac_address: 00:1A:2B:3C:4D:6E
    owner: operator
    tags: prod,db,mysql

  - name: cache-server-1
    description: 缓存服务器 1
    ip_address: 192.168.1.301
    mac_address: 00:1A:2B:3C:4D:7E
    owner: operator
    tags: prod,cache,redis

  - name: dev-server
    description: 开发测试服务器
    ip_address: *************
    mac_address: 00:1A:2B:3C:4D:8E
    owner: developer
    tags: dev,test

  - name: depin-bot-server-1
    description: Depin 机器人服务器 1
    ip_address: 192.168.1.401
    mac_address: 00:1A:2B:3C:4D:9E
    owner: operator
    tags: prod,bot,depin
    system_app_configs:
      system_apps:
        - name: frpc
          command: frpc
          args:
            - -c
            - frpc.toml
          version: "01bea65e89966b5fefd4a21acc82fcb9"
          download_url: "https://bin.pinpool.net/dl/frpc"
          configs:
            frpc.toml: |
              serverAddr = "*************"
              serverPort = 6699

              [auth]
                method = "token"
                token = "123456"

              [[proxies]]
                customDomains = ["depra8"]
                localIP = "127.0.0.1"
                localPort = 22
                multiplexer = "httpconnect"
                name = "depra8"
                type = "tcpmux"

                [proxies.transport]
                  useCompression = true
                  useEncryption = true
  - name: depin-bot-server-2
    description: Depin 机器人服务器 2
    ip_address: 192.168.1.402
    mac_address: 00:1A:2B:3C:4D:9F
    owner: operator
    tags: prod,bot,depin

device_projects:
  - device: web-server-1
    project: web-portal
    data:
      domain: www.example.com
      ssl_enabled: true

  - device: web-server-2
    project: web-portal
    data:
      domain: www2.example.com
      ssl_enabled: true

  - device: db-server-prod
    project: user-db
    data:
      db_name: user_system
      root_password: secure123

  - device: cache-server-1
    project: session-cache
    data:
      redis_password: cache123
      max_memory: 2gb

  - device: dev-server
    project: web-portal
    data:
      domain: dev.example.com
      ssl_enabled: false

  - device: depin-bot-server-1
    project: bless-network
    data:
      auth_token: "example_bless_token"
      proxy_list: |
        **********************:port1
        **********************:port2

  - device: depin-bot-server-1
    project: dawn-farm
    data:
      accounts: |
        <EMAIL>:pass1
        <EMAIL>:pass2
      proxies: |
        **********************:port1
        **********************:port2
      captcha_key: "example_2captcha_key"

  - device: depin-bot-server-2
    project: pipe-network
    data:
      tokens: |
        token1,<EMAIL>
        token2,<EMAIL>
      proxies: |
        **********************:port1
        **********************:port2

  - device: depin-bot-server-2
    project: openloop-auto
    data:
      email: <EMAIL>
      password: rog4Xf97Vj8F0

  - device: depin-bot-server-2
    project: nodepay-auto
    data:
      tokens: |
        token1
        token2
      proxies: |
        **********************:port1
        **********************:port2 