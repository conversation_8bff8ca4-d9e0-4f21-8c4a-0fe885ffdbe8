#!/bin/bash

# 设置变量
DEPRA_PATH="/data/apps/depra"
DOWNLOAD_URL="https://bin.pinpool.net/dl/depra" # 请替换为实际下载链接
CHECK_INTERVAL=3600 # 检测间隔时间（秒）
MAX_RETRY=3 # 尝试重新启动的最大次数
SLEEP_TIME=10 # 等待时间（秒）以检查进程是否成功启动
DOWNLOAD_TIMEOUT=600 # 下载超时时间（秒）

function download_latest() {
    echo "Downloading latest depra..."
    if [ ! -d "$DEPRA_PATH" ]; then
        mkdir -p "$DEPRA_PATH"
    fi
    # 首次创建docker目录
    if [ ! -d "/data/apps/docker" ]; then
        mkdir -p "/data/apps/docker"
    fi
    curl -L --max-time $DOWNLOAD_TIMEOUT -o "$DEPRA_PATH/depra_tmp" "$DOWNLOAD_URL"
    if [ $? -eq 0 ]; then
        mv "$DEPRA_PATH/depra_tmp" "$DEPRA_PATH/depra"
        chmod +x "$DEPRA_PATH/depra"
        echo "Download and update completed."
    else
        echo "Failed to download the latest version of depra within $DOWNLOAD_TIMEOUT seconds."
        echo "等待${SLEEP_TIME}秒后退出..."
        sleep $SLEEP_TIME
        exit 1
    fi
}

function start_depra() {
    local retry_count=0
    while [ $retry_count -lt $MAX_RETRY ]; do
        echo "Trying to start depra... Attempt $((retry_count+1))"
        "$DEPRA_PATH/depra" &
        sleep $((SLEEP_TIME * 2 ** retry_count)) # 等待指定的时间以检查进程是否成功启动
        
        if pgrep -f "depra" > /dev/null; then
            echo "depra started successfully."
            return 0
        else
            echo "Failed to start depra. Retrying..."
            ((retry_count++))
        fi
    done
    
    echo "Max retries reached. Re-downloading the latest version of depra."
    download_latest
    return 1
}

while true; do
    if ! pgrep -f "depra" > /dev/null; then
        echo "depra is not running."
        if [ ! -f "$DEPRA_PATH/depra" ]; then
            echo "depra binary not found. Downloading..."
            download_latest
        fi
        if ! start_depra; then
            echo "Failed to start depra after downloading, will retry on next check."
        fi
    else
        echo "depra is running normally."
    fi
    sleep $CHECK_INTERVAL
done