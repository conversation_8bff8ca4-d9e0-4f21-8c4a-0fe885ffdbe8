import os
import sys

from datetime import datetime, timedelta, timezone, time

from sqlalchemy import func

from app import create_app
from app.models import PointRecord
from app.models.base import db
from app.models.device_project import DeviceProject
from app.models.metrics import ServiceMetricsSnapshot
from app.models.device import Device
from app.services.metrics_service import metrics_service


def generate_snapshot():
    """
    生成所有设备的服务指标快照
    
    此函数每30分钟运行一次，为每个设备的每个服务创建当日快照。
    它会计算与昨天最新快照相比的点数和运行时间增量。
    """
    # 获取所有设备
    devices = Device.query.all()

    # 获取当前日期
    current_date = datetime.now(timezone.utc).date()
    today_str = current_date.strftime('%Y-%m-%d')

    # 计算昨天的日期
    yesterday = current_date - timedelta(days=1)
    yesterday_str = yesterday.strftime('%Y-%m-%d')

    for device in devices:
        # 获取设备的所有服务指标
        services = metrics_service.get_device_services(device.id)

        for service_name in services:
            # 获取服务的当前指标
            metrics = metrics_service.get_service_metrics(device.id, service_name)

            if not metrics:
                continue

            # 获取今天之前创建的最新快照，用于获取昨天的增量值
            existing_snapshot = ServiceMetricsSnapshot.query.filter_by(
                device_id=device.id,
                service_name=service_name,
                day=today_str
            ).order_by(ServiceMetricsSnapshot.created_at.desc()).first()

            # 获取昨天的最新快照，用于计算今天的增量
            yesterday_snapshot = ServiceMetricsSnapshot.query.filter_by(
                device_id=device.id,
                service_name=service_name,
                day=yesterday_str
            ).order_by(ServiceMetricsSnapshot.created_at.desc()).first()

            # 计算增量
            points_increase = metrics.points - (yesterday_snapshot.points if yesterday_snapshot else 0)
            running_time_increase = metrics.running_time - (
                yesterday_snapshot.running_time if yesterday_snapshot else 0)

            # 设置昨天的单日增量值
            # 如果已经有今天的快照，尝试重用现有的 yesterday_* 值
            # 这样可以保持一致性，避免多个今天的快照有不同的昨天增量值
            yesterday_points = 0
            yesterday_running_time = 0

            if existing_snapshot and existing_snapshot.yesterday_points > 0:
                # 如果现有的今日快照已有昨日值，就重用它们
                yesterday_points = existing_snapshot.yesterday_points
                yesterday_running_time = existing_snapshot.yesterday_running_time
            elif yesterday_snapshot:
                # 如果没有今日值，但有昨日快照，尝试计算昨日增量
                # 计算方法由 update_yesterday_metrics 任务处理
                # 这里我们只做基本设置，让日常任务更新它们
                yesterday_points = 0
                yesterday_running_time = 0

            # 创建新快照
            snapshot = ServiceMetricsSnapshot(
                device_id=device.id,
                service_name=service_name,
                day=today_str,
                points=metrics.points,
                running_time=metrics.running_time,
                points_increase=points_increase,
                running_time_increase=running_time_increase,
                yesterday_points=yesterday_points,
                yesterday_running_time=yesterday_running_time
            )

            db.session.add(snapshot)

    db.session.commit()


def generate_all_yesterday_snapshot():
    # 分批处理设备
    page = 0
    per_page = 100

    while True:
        devices = Device.query.order_by(Device.id).offset(page * per_page).limit(per_page).all()
        if not devices:
            break
        for device in devices:
            generate_device_yesterday_snapshot(device)
        page += 1


def generate_device_yesterday_snapshot(device: Device):
    device_projects = DeviceProject.query.filter_by(device_id=device.id).all()
    if device_projects:
        for device_project in device_projects:
            generate_dp_yesterday_snapshot(device_project)


def generate_dp_yesterday_snapshot(device_project: DeviceProject):
    device_id = device_project.device.id
    project_name = device_project.project.name
    metrics = metrics_service.get_service_metrics(device_id, project_name)
    if not metrics:
        return
    today_time = datetime.combine(datetime.today(), time.min)

    yesterday_time = today_time - timedelta(days=1)
    yesterday_str = yesterday_time.strftime('%Y-%m-%d')

    day_before_yesterday = yesterday_time - timedelta(days=1)
    day_before_yesterday_str = day_before_yesterday.strftime('%Y-%m-%d')

    day_before_snapshot = ServiceMetricsSnapshot.query.filter_by(
        device_id=device_id,
        service_name=project_name,
        day=day_before_yesterday_str
    ).order_by(ServiceMetricsSnapshot.created_at.desc()).first()

    points_increase = metrics.points - (day_before_snapshot.points if day_before_snapshot else 0)
    running_time_increase = metrics.running_time - (day_before_snapshot.running_time if day_before_snapshot else 0)

    yesterday_user_ubi_points = PointRecord.query.with_entities(
        func.sum(PointRecord.points)
    ).filter(
        PointRecord.user_id == device_project.device.owner_id,
        PointRecord.record_type == 'project',
        PointRecord.related_id == device_project.project_id,
        PointRecord.created_at >= yesterday_time,
        PointRecord.created_at < today_time
    ).scalar() or 0

    yesterday_snapshot = ServiceMetricsSnapshot(
        device_id=device_id,
        service_name=project_name,
        project_id=device_project.project.id,
        day=yesterday_str,
        points=metrics.points,
        running_time=metrics.running_time,
        points_increase=points_increase,
        running_time_increase=running_time_increase,
        ubi_points=yesterday_user_ubi_points
    )
    db.session.add(yesterday_snapshot)
    db.session.commit()
