#!/usr/bin/env python

"""用于测试向 Pinpool 上报指标数据的脚本"""
import os
import random
import sys

# 添加项目根目录到 Python Path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import requests
from datetime import datetime, timezone
import json
import base64
from typing import Dict, Any
from app.utils.crypto import Crypto




class PinpoolClient:
    def __init__(self, base_url: str, token: str):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            "Authorization": f"{token}"
        })
        encryption_key = "UQ8yv92zcRxbVOuOnm7k58bJ9hndm94g"
        self.crypto = Crypto(encryption_key)

    def encrypt(self, data):
        """加密数据"""
        # 将数据转换为bytes
        if isinstance(data, str):
            data = data.encode()

        encrypted, nonce = self.crypto.encrypt(data)
        return encrypted, nonce

    def decrypt(self, encrypted_data, nonce):
        """解密数据"""
        try:
            plaintext = self.crypto.decrypt(encrypted_data, nonce)
            return plaintext.decode("utf-8")
        except Exception as e:
            print(f"解密失败: {e}")
            return None

    def get_statistics(self) -> dict:
        """获取统计数据"""
        url = f"{self.base_url}/api/dashboard/statistics"
        try:
            response = self.session.get(url)
            response.raise_for_status()
            return response.json().get("data", {})
        except requests.RequestException as e:
            print(f"获取统计数据失败: {str(e)}")
            return {}

    def report_metrics(self) -> bool:
        """上报指标数据"""
        url = f"{self.base_url}/api/remote/metrics"
        try:
            # 构造原始数据
            fixed_started_at = "2024-02-17T07:40:00Z"
            raw_data = {
                "services": [
                    {
                        "started_at": fixed_started_at,
                        "service_name": "bless-network",
                        "points": random.randint(0, 1000000),
                        "running_time": random.randint(0, 1000000),
                        "updated_at": datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")
                    },
                    {
                        "started_at": fixed_started_at,
                        "service_name": "openloop",
                        "points": random.randint(0, 1000000),
                        "running_time": random.randint(0, 1000000),
                        "updated_at": datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")
                    },
                    {
                        "started_at": fixed_started_at,
                        "service_name": "nodepay-auto",
                        "points": random.randint(0, 1000000),
                        "running_time": random.randint(0, 1000000),
                        "updated_at": datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")
                    },
                    {
                        "started_at": fixed_started_at,
                        "service_name": "pipe",
                        "points": random.randint(0, 1000000),
                        "running_time": random.randint(0, 1000000),
                        "updated_at": datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")
                    }
                ],
                "device": {
                    "started_at": fixed_started_at,
                    "running_time": random.randint(3600, 86400),  # 1小时到24小时
                    "lan_ip": "*************",
                    "public_ip": "*******",
                    "network": {
                        "download_speed": random.randint(1000000, 10000000),  # 1-10 Mbps
                        "upload_speed": random.randint(500000, 5000000)      # 0.5-5 Mbps
                    },
                    "disk": {
                        "total": 1000000000000,  # 1TB
                        "used": random.randint(100000000000, 800000000000),  # 100GB-800GB
                        "free": random.randint(100000000000, 200000000000)   # 100GB-200GB
                    },
                    "cpu": {
                        "usage": random.uniform(10.0, 90.0),  # 10%-90%
                        "cores": 8
                    },
                    "memory": {
                        "total": 16000000000,  # 16GB
                        "used": random.randint(4000000000, 12000000000),  # 4GB-12GB
                        "free": random.randint(2000000000, 4000000000)    # 2GB-4GB
                    },
                    "updated_at": datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")
                }
            }
            
            # 将数据转换为 JSON 字符串并使用项目的加密方法进行加密
            json_data = json.dumps(raw_data)
            encrypted_data, nonce = self.encrypt(json_data)
            
            # 构造请求数据
            data = {
                "encrypted": encrypted_data,
                "nonce": nonce
            }
            
            response = self.session.post(url, json=data)
            print(response.text)
            response.raise_for_status()
            return True

        except requests.RequestException as e:
            print(f"上报指标失败: {str(e)}")
            return False

def main():
    # 初始化客户端，使用固定的 token
    token = "3e460fb7269a507d6042cac859d7f16209b5533a4c74c9d3c95bde4b5f8e5381"
    client = PinpoolClient("https://pinpool.net", token)

    # 上报测试数据
    success = client.report_metrics()
    if success:
        print("成功上报测试数据")
    else:
        print("上报测试数据失败")

if __name__ == "__main__":
    main()
