# API 国际化指南

本文档介绍如何使用、扩展和管理本项目的国际化功能。

## 目录

1. [简介](#简介)
2. [技术架构](#技术架构)
3. [使用流程](#使用流程)
4. [添加新语言](#添加新语言)
5. [翻译管理](#翻译管理)
6. [开发注意事项](#开发注意事项)
7. [问题排查](#问题排查)

## 简介

国际化（i18n）功能使我们的 API 能够根据客户端的语言偏好返回相应语言的提示消息。我们采用了非侵入式设计，不需要修改现有的业务逻辑代码，而是通过响应拦截器自动翻译 JSON 响应中的 `message` 字段。

## 技术架构

- **框架**: Flask-Babel
- **实现方式**: 使用响应拦截器 (after_request)
- **提取工具**: 自定义脚本自动提取中文文案
- **配置文件**: babel.cfg 用于配置提取规则

## 使用流程

### 1. 安装依赖

```bash
uv add flask-babel
```

### 2. 设置客户端语言偏好

客户端可以通过以下两种方式指定语言偏好：

- 查询参数: `?locale=en`
- 请求头: `Accept-Language: en`

如果未指定，将使用默认语言（中文）。

### 3. 提取、更新和编译翻译

使用提供的脚本管理翻译：

```bash
# 提取需要翻译的消息
./i18n.sh extract

# 初始化新语言 (例如英语)
./i18n.sh init en

# 更新现有翻译
./i18n.sh update

# 编译翻译文件
./i18n.sh compile
```

## 添加新语言

要添加新语言支持，请按照以下步骤操作：

1. 初始化语言：
   ```bash
   ./i18n.sh init <语言代码>
   ```
   例如：`./i18n.sh init fr` (法语)

2. 编辑生成的 PO 文件，填写翻译：
   ```
   translations/<语言代码>/LC_MESSAGES/messages.po
   ```

3. 编译翻译：
   ```bash
   ./i18n.sh compile
   ```

## 翻译管理

### 翻译文件结构

- **POT 文件**: `messages.pot` - 翻译模板
- **PO 文件**: `translations/<语言代码>/LC_MESSAGES/messages.po` - 翻译文件
- **MO 文件**: `translations/<语言代码>/LC_MESSAGES/messages.mo` - 编译后的翻译文件

### 翻译过程

1. 修改代码或添加新消息
2. 运行 `./i18n.sh extract` 提取新消息
3. 运行 `./i18n.sh update` 更新现有翻译文件
4. 编辑 PO 文件，完成翻译
5. 运行 `./i18n.sh compile` 编译翻译
6. 重启应用，使翻译生效

## 开发注意事项

### 响应格式

系统会自动翻译符合以下格式的 JSON 响应：

```json
{
  "data": { ... },
  "code": "200",
  "message": "操作成功"
}
```

只有 `message` 字段会被翻译。

## 问题排查

### 常见问题

1. **翻译不生效**
   - 检查是否已编译翻译文件：`./i18n.sh compile`
   - 检查语言代码是否正确
   - 检查 PO 文件中是否有相应的翻译

2. **新添加的消息未被提取**
   - 确保消息在 `app/api` 或 `app/services` `app/utils` 目录下的 Python 文件中

3. **翻译后格式错乱**
   - 检查翻译中是否保留了原始字符串中的格式化参数
   - 检查 JSON 响应是否被正确解析和序列化

如有其他问题，请联系开发团队。 