#!/usr/bin/env python
import os
import re
import glob
import sys
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def extract_messages_from_file(file_path: str) -> list:
    """
    从Python文件中提取用于国际化的消息

    Args:
        file_path: Python文件路径

    Returns:
        提取的消息列表
    """
    messages = []
    chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 提取字符串中的中文
        # 1. 查找所有的字符串字面量
        string_pattern = re.compile(r'["\'](.+?)["\']')
        for match in string_pattern.finditer(content):
            string_value = match.group(1)

            # 检查是否包含中文
            if chinese_pattern.search(string_value):
                # 避免重复添加
                if string_value not in messages:
                    messages.append(string_value)
    except Exception as e:
        logger.error(f"从文件提取消息时出错 {file_path}: {str(e)}")

    return messages

def main():
    """提取API和服务层中的中文消息并生成POT文件"""
    messages = []
    
    # 检查目录是否存在
    required_dirs = ['../app/api', '../app/services', '../app/utils']
    missing_dirs = [d for d in required_dirs if not os.path.exists(d)]
    
    if missing_dirs:
        logger.warning(f"以下目录不存在: {', '.join(missing_dirs)}")
        logger.warning("部分消息可能无法提取")
    
    # 获取多个目录下的所有Python文件
    logger.info("搜索Python文件...")
    api_files = glob.glob('../app/api/**/*.py', recursive=True) if os.path.exists('../app/api') else []
    service_files = glob.glob('../app/services/**/*.py', recursive=True) if os.path.exists('../app/services') else []
    utils_files = glob.glob('../app/utils/**/*.py', recursive=True) if os.path.exists('../app/utils') else []
    
    all_files = api_files + service_files + utils_files
    
    logger.info(f"找到 {len(all_files)} 个Python文件.")
    
    # 从每个文件中提取消息
    for file_path in all_files:
        try:
            file_messages = extract_messages_from_file(file_path)
            if file_messages:
                logger.info(f"从 {file_path} 中提取了 {len(file_messages)} 条消息")
            messages.extend(file_messages)
        except Exception as e:
            logger.error(f"处理 {file_path} 时出错: {e}")
    
    # 去重
    unique_messages = list(set(messages))
    
    if not unique_messages:
        logger.warning("未找到任何中文消息")
        sys.exit(0)
    
    # 创建messages.pot文件
    try:
        with open('../app/messages.pot', 'w', encoding='utf-8') as f:
            f.write('# Translations template for your application.\n')
            f.write('# Copyright (C) 2023 ORGANIZATION\n')
            f.write('# This file is distributed under the same license as the project.\n')
            f.write('msgid ""\n')
            f.write('msgstr ""\n')
            f.write('"Project-Id-Version: 1.0\\n"\n')
            f.write('"Report-Msgid-Bugs-To: \\n"\n')
            f.write('"POT-Creation-Date: 2023-08-01 12:00+0000\\n"\n')
            f.write('"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\\n"\n')
            f.write('"Last-Translator: FULL NAME <EMAIL@ADDRESS>\\n"\n')
            f.write('"Language-Team: LANGUAGE <<EMAIL>>\\n"\n')
            f.write('"MIME-Version: 1.0\\n"\n')
            f.write('"Content-Type: text/plain; charset=utf-8\\n"\n')
            f.write('"Content-Transfer-Encoding: 8bit\\n"\n')
            f.write('"Generated-By: manual process\\n"\n\n')
            
            # 写入消息
            for message in unique_messages:
                escaped_message = message.replace('"', '\\"').replace('\n', '\\n')
                f.write(f'msgid "{escaped_message}"\n')
                f.write('msgstr ""\n\n')
        
        logger.info(f"提取了 {len(unique_messages)} 条唯一消息到 messages.pot")
        logger.info("现在可以运行: ./i18n.sh init 初始化默认语言翻译")
        logger.info("或者: ./i18n.sh init <语言代码> 初始化特定语言翻译")
    except Exception as e:
        logger.error(f"创建 messages.pot 文件时出错: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main() 