#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# 默认支持的语言数组
# 添加你需要支持的所有语言代码到这个数组
DEFAULT_LANGUAGES=('zh' 'es' 'de' 'fr' 'ru' 'ja' 'ko' 'pt' 'ar' 'hi' 'en')

# 错误处理函数
handle_error() {
    echo -e "${RED}错误: $1${NC}"
    exit 1
}

# 确保目录存在
mkdir -p ../app/translations || handle_error "无法创建 translations 目录"

# 检查依赖命令
check_dependencies() {
    for cmd in pybabel python; do
        if ! command -v $cmd &> /dev/null; then
            handle_error "$cmd 命令未找到，请确保已安装所需依赖"
        fi
    done
}

# 提取函数
extract() {
    echo -e "${YELLOW}正在提取消息...${NC}"
    python extract_messages.py || handle_error "提取消息失败"
    
    # 检查是否成功创建了POT文件
    if [ ! -f ../app/messages.pot ]; then
        handle_error "未能生成 messages.pot 文件"
    else
        echo -e "${GREEN}成功提取消息到 messages.pot${NC}"
    fi
}

# 初始化单个语言
init_single() {
    local lang=$1
    echo -e "${YELLOW}初始化 $lang 语言翻译...${NC}"
    
    if [ -d "../app/translations/$lang" ]; then
        echo -e "${YELLOW}$lang 语言翻译已存在，跳过初始化${NC}"
        return
    fi
    
    pybabel init -i ../app/messages.pot -d ../app/translations -l $lang
    if [ $? -ne 0 ]; then
        echo -e "${RED}初始化 $lang 语言翻译失败${NC}"
        return
    fi
    
    echo -e "${GREEN}成功创建 $lang 语言翻译文件${NC}"
}

# 批量初始化语言
init() {
    # 检查POT文件是否存在
    if [ ! -f ../app/messages.pot ]; then
        echo -e "${YELLOW}messages.pot 文件不存在，正在提取消息...${NC}"
        extract
    fi
    
    if [ $# -gt 0 ]; then
        # 使用命令行参数指定的语言
        for lang in "$@"; do
            init_single "$lang"
        done
    else
        # 使用默认语言数组
        echo -e "${YELLOW}未指定语言，使用默认语言: ${DEFAULT_LANGUAGES[*]}${NC}"
        for lang in "${DEFAULT_LANGUAGES[@]}"; do
            init_single "$lang"
        done
    fi
    
    echo -e "${YELLOW}请编辑 translations/*/LC_MESSAGES/messages.po 文件添加翻译${NC}"
}

# 更新翻译
update() {
    # 检查POT文件是否存在
    if [ ! -f ../app/messages.pot ]; then
        echo -e "${YELLOW}messages.pot 文件不存在，正在提取消息...${NC}"
        extract
    fi
    
    # 检查translations目录是否存在
    if [ ! -d ../app/translations ] || [ -z "$(ls -A ../app/translations 2>/dev/null)" ]; then
        handle_error "translations 目录不存在或为空，请先运行 ./i18n.sh init"
    fi
    
    # 如果指定了语言，只更新这些语言
    if [ $# -gt 0 ]; then
        for lang in "$@"; do
            if [ -d "../app/translations/$lang" ]; then
                echo -e "${YELLOW}更新 $lang 语言翻译...${NC}"
                pybabel update -i ../app/messages.pot -d ../app/translations -l $lang
                if [ $? -eq 0 ]; then
                    echo -e "${GREEN}成功更新 $lang 语言翻译${NC}"
                else
                    echo -e "${RED}更新 $lang 语言翻译失败${NC}"
                fi
            else
                echo -e "${RED}$lang 语言翻译不存在，请先运行 ./i18n.sh init $lang${NC}"
            fi
        done
    else
        # 更新所有现有语言翻译
        echo -e "${YELLOW}更新所有语言翻译...${NC}"
        pybabel update -i ../app/messages.pot -d ../app/translations
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}成功更新所有语言翻译${NC}"
        else
            echo -e "${RED}更新翻译失败${NC}"
        fi
    fi
    
    echo -e "${YELLOW}请编辑 translations/*/LC_MESSAGES/messages.po 文件更新翻译${NC}"
}

# 编译单个语言翻译
compile_single() {
    local lang=$1
    
    if [ ! -d "../app/translations/$lang" ]; then
        echo -e "${RED}$lang 语言翻译不存在，请先运行 ./i18n.sh init $lang${NC}"
        return
    fi
    
    echo -e "${YELLOW}编译 $lang 语言翻译...${NC}"
    pybabel compile -d ../app/translations -l $lang
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}成功编译 $lang 语言翻译${NC}"
    else
        echo -e "${RED}编译 $lang 语言翻译失败${NC}"
    fi
}

# 编译翻译
compile() {
    # 检查translations目录是否存在
    if [ ! -d ../app/translations ] || [ -z "$(ls -A ../app/translations 2>/dev/null)" ]; then
        handle_error "translations 目录不存在或为空，请先运行 ./i18n.sh init"
    fi
    
    if [ $# -gt 0 ]; then
        # 编译指定语言
        for lang in "$@"; do
            compile_single "$lang"
        done
    else
        # 编译所有语言或默认语言
        if [ ${#DEFAULT_LANGUAGES[@]} -gt 0 ]; then
            echo -e "${YELLOW}编译默认语言: ${DEFAULT_LANGUAGES[*]}${NC}"
            for lang in "${DEFAULT_LANGUAGES[@]}"; do
                compile_single "$lang"
            done
        else
            echo -e "${YELLOW}编译所有语言翻译...${NC}"
            pybabel compile -d translations
            if [ $? -eq 0 ]; then
                echo -e "${GREEN}成功编译所有语言翻译${NC}"
            else
                echo -e "${RED}编译翻译失败${NC}"
            fi
        fi
    fi
    
    echo -e "${YELLOW}重启应用后翻译将生效${NC}"
}

# 显示支持的语言
list_languages() {
    echo -e "${GREEN}已配置的默认语言:${NC}"
    for lang in "${DEFAULT_LANGUAGES[@]}"; do
        echo "  - $lang"
    done
    
    echo -e "\n${GREEN}已初始化的语言:${NC}"
    if [ -d ../app/translations ]; then
        langs=$(find ../app/translations -maxdepth 1 -type d -not -path "translations" | xargs -n1 basename 2>/dev/null)
        if [ -z "$langs" ]; then
            echo "  尚未初始化任何语言"
        else
            for lang in $langs; do
                echo "  - $lang"
            done
        fi
    else
        echo "  尚未初始化任何语言"
    fi
}

# 显示帮助
help() {
    echo -e "${GREEN}使用方法:${NC}"
    echo "  ./i18n.sh extract                   # 从源代码提取消息"
    echo "  ./i18n.sh init [lang1 lang2 ...]    # 初始化指定语言翻译，无参数则初始化默认语言"
    echo "  ./i18n.sh update [lang1 lang2 ...]  # 更新指定语言翻译，无参数则更新所有翻译"
    echo "  ./i18n.sh compile [lang1 lang2 ...] # 编译指定语言翻译，无参数则编译默认语言"
    echo "  ./i18n.sh list                      # 显示已配置和已初始化的语言"
    echo ""
    echo -e "${YELLOW}默认支持的语言: ${DEFAULT_LANGUAGES[*]}${NC}"
    echo ""
    echo -e "${YELLOW}示例:${NC}"
    echo "  ./i18n.sh init en                   # 仅初始化英语翻译"
    echo "  ./i18n.sh init en ja zh_TW          # 初始化英语、日语和繁体中文翻译"
    echo "  ./i18n.sh compile en                # 仅编译英语翻译"
}

# 主函数
check_dependencies

case "$1" in
    extract)
        extract
        ;;
    init)
        shift  # 移除第一个参数，剩余的作为语言代码
        init "$@"
        ;;
    update)
        shift
        update "$@"
        ;;
    compile)
        shift
        compile "$@"
        ;;
    list)
        list_languages
        ;;
    *)
        help
        ;;
esac 