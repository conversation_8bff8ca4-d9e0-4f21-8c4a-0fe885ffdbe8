#!/usr/bin/env python3
import os
import sys

# 添加项目根目录到 Python Path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import json

import requests

from app.utils.crypto import Crypto


class RemoteConfigClient:
    def __init__(self, base_url, mac_address, encryption_key):
        """
        初始化远程配置客户端

        Args:
            base_url: API基础URL
            mac_address: 设备MAC地址
            encryption_key: 32字节的加密密钥
        """
        self.base_url = base_url.rstrip("/")
        self.mac_address = mac_address
        self.crypto = Crypto(encryption_key)
        self.token = None

    def encrypt(self, data):
        """加密数据"""
        # 将数据转换为bytes
        if isinstance(data, str):
            data = data.encode()

        encrypted, nonce = self.crypto.encrypt(data)
        return {
            "encrypted": encrypted,
            "nonce": nonce
        }

    def decrypt(self, encrypted_data, nonce):
        """解密数据"""
        try:
            plaintext = self.crypto.decrypt(encrypted_data, nonce)
            return plaintext.decode("utf-8")
        except Exception as e:
            print(f"解密失败: {e}")
            return None

    def get_token(self):
        """获取设备令牌"""
        url = f"{self.base_url}/api/remote/devices/token"

        # 加密MAC地址
        encrypted_data = self.encrypt(self.mac_address)

        # 发送请求
        response = requests.post(url, json=encrypted_data)
        if response.status_code == 200:
            data = response.json()
            # 解密token
            self.token = self.decrypt(data["encrypted"], data["nonce"])
            return self.token
        else:
            print(f"获取token失败: {response.status_code}")
            return None

    def get_system_config(self):
        """获取系统配置"""
        if not self.token:
            print("未获取token，先获取token")
            if not self.get_token():
                return None

        url = f"{self.base_url}/api/remote/configs/system"
        headers = {"Authorization": self.token}

        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            data = response.json()
            # 解密配置
            config = self.decrypt(data["encrypted"], data["nonce"])
            return json.loads(config) if config else None
        else:
            print(f"获取系统配置失败: {response.status_code}")
            return None

    def get_service_config(self):
        """获取服务配置"""
        if not self.token:
            print("未获取token，先获取token")
            if not self.get_token():
                return None

        url = f"{self.base_url}/api/remote/configs/service"
        headers = {"Authorization": self.token}

        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            data = response.json()
            # 解密配置
            config = self.decrypt(data["encrypted"], data["nonce"])
            return json.loads(config) if config else None
        else:
            print(f"获取服务配置失败: {response.status_code}")
            return None

def main():
    # 配置参数
    if os.getenv("BOX_ENV") == "DEV":
        BASE_URL = "http://127.0.0.1:5000"  # 修改为实际的API地址
        MAC_ADDRESS = "00:1A:2B:3C:4D:9E"  # depin-bot-server-2的MAC地址
    else:
        BASE_URL = "https://api.pinpool.net"
        MAC_ADDRESS = "E0:76:D0:C4:A9:9D"
    ENCRYPTION_KEY = "UQ8yv92zcRxbVOuOnm7k58bJ9hndm94g"  # 32字节的加密密钥

    # 创建客户端
    client = RemoteConfigClient(BASE_URL, MAC_ADDRESS, ENCRYPTION_KEY)

    # 获取token
    print("正在获取设备token...")
    token = client.get_token()
    if token:
        print(f"获取token成功: {token}")

        # 获取系统配置
        print("\n正在获取系统配置...")
        system_config = client.get_system_config()
        if system_config:
            print("系统配置:")
            print(json.dumps(system_config, indent=2, ensure_ascii=False))

        # 获取服务配置
        print("\n正在获取服务配置...")
        service_config = client.get_service_config()
        if service_config:
            print("服务配置:")
            print(json.dumps(service_config, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    main()
