#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
迁移脚本：将现有的UBI积分数据迁移到用户资产系统

此脚本将读取所有现有的积分记录，并使用UbiPointsIntegrationService将它们添加到用户资产系统中。
执行此脚本前，请确保已经创建了UbiPointsIntegrationService并且已经配置好了UBI项目。

使用方法：
    python migrate_ubi_points_to_assets.py
"""

import time
from datetime import datetime
from decimal import Decimal

from app.models.asset import UserAsset, AssetTransaction, TransactionTypeEnum
from app.models.base import db
from app.models.points import PointRecord
from app.services.ubi_points_integration_service import UbiPointsIntegrationService


def migrate_ubi_points():
    """
    将现有的UBI积分数据迁移到用户资产系统
    """
    print("开始迁移UBI积分数据到用户资产系统...")

    # 确保UBI项目和资产类型存在
    asset_type = UbiPointsIntegrationService.get_ubi_asset_type()
    if not asset_type:
        print("错误：无法创建UBI资产类型")
        return False

    print(f"UBI资产类型ID: {asset_type.id}")

    # 获取所有用户的积分总和
    user_points = db.session.query(
        PointRecord.user_id,
        db.func.sum(PointRecord.points).label('total_points')
    ).group_by(PointRecord.user_id).all()

    total_users = len(user_points)
    print(f"找到 {total_users} 个用户的积分记录")

    # 检查是否已经有用户资产记录
    existing_assets = db.session.query(UserAsset.user_id).filter_by(
        asset_type_id=asset_type.id
    ).all()
    existing_user_ids = [asset[0] for asset in existing_assets]

    if existing_user_ids:
        print(f"警告：已有 {len(existing_user_ids)} 个用户的UBI资产记录")
        proceed = input("是否继续迁移？这可能会导致积分重复。(y/n): ")
        if proceed.lower() != 'y':
            print("迁移已取消")
            return False

    # 开始迁移
    success_count = 0
    error_count = 0

    for user_id, total_points in user_points:
        # 跳过已有资产记录的用户

        print(f"正在迁移用户 {user_id} 的积分: {total_points}")

        # 创建用户资产记录
        try:
            # 检查用户是否已有资产记录
            existing_asset = UserAsset.query.filter_by(
                user_id=user_id,
                asset_type_id=asset_type.id
            ).first()

            balance_before = Decimal(0)
            amount = Decimal(total_points)
            if existing_asset:
                print(f"用户 {user_id} 已有资产记录，更新余额")
                balance_before = existing_asset.available_balance
                existing_asset.available_balance += amount
                db.session.add(existing_asset)
            else:
                # 创建新的用户资产记录
                user_asset = UserAsset(
                    user_id=user_id,
                    asset_type_id=asset_type.id,
                    available_balance=amount,
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )
                db.session.add(user_asset)

            # 创建资产交易记录
            transaction = AssetTransaction(
                user_id=user_id,
                asset_type_id=asset_type.id,
                amount=amount,
                balance_before=balance_before,
                balance_after=balance_before + amount,
                transaction_type=TransactionTypeEnum.SYSTEM_ADJUST,
                extend_field={"remark": "从UBI积分系统迁移"},
                created_at=datetime.now()
            )
            db.session.add(transaction)

            db.session.commit()
            success_count += 1

        except Exception as e:
            db.session.rollback()
            print(f"迁移用户 {user_id} 的积分失败: {str(e)}")
            error_count += 1

        # 每处理10个用户暂停一下，避免数据库负载过高
        if (success_count + error_count) % 10 == 0:
            time.sleep(0.5)

    print("\n迁移完成!")
    print(f"成功: {success_count} 个用户")
    print(f"失败: {error_count} 个用户")

    return True


if __name__ == "__main__":
    migrate_ubi_points()
