from unittest.mock import Mock, patch

import fakeredis
import pytest
from flask import json

from app import create_app
from app.models import db
from app.utils.redis_client import RedisClient


class TestEmailVerificationAPI:
    @pytest.fixture(autouse=True)
    def setup(self):
        self.app = create_app("testing")
        self.client = self.app.test_client()
        # 使用 fakeredis 替代真实 redis
        self.fake_redis = fakeredis.FakeStrictRedis(decode_responses=True)
        RedisClient.set_redis(self.fake_redis)
        self.redis = RedisClient.get_instance()

        with self.app.app_context():
            db.create_all()
            self.redis.flushdb()
            yield
            db.session.remove()
            db.drop_all()
            self.redis.flushdb()
            # 测试结束后清理 redis 实例
            RedisClient.set_redis(None)

    def test_send_code_success(self):
        """测试成功发送验证码"""
        with patch("requests.post") as mock_post:
            mock_post.return_value = Mock(status_code=200)
            response = self.client.post("/api/auth/send-code", json={
                "email": "<EMAIL>"
            })
            data = json.loads(response.data)
            assert response.status_code == 200
            assert data["code"] == 200
            assert data["message"] == "验证码发送成功"
            # 验证Redis中存储了验证码
            assert self.redis.exists("verifycodeguest") == 0

    def test_send_code_invalid_email(self):
        """测试发送验证码时邮箱格式错误"""
        response = self.client.post("/api/auth/send-code", json={
            "email": "invalid-email"
        })
        data = json.loads(response.data)
        assert response.status_code == 400
        assert data["code"] == 400
        assert "邮箱格式错误" in data["message"]

    def test_send_code_missing_email(self):
        """测试发送验证码时缺少邮箱参数"""
        response = self.client.post("/api/auth/send-code", json={})
        data = json.loads(response.data)
        assert response.status_code == 400
        assert data["code"] == 400
        assert "缺少必要参数" in data["message"]

    def test_verify_code_success(self):
        """测试验证码验证成功"""
        test_code = "123456"
        self.redis.setex("verifycodeguest", 600, test_code)

        response = self.client.post("/api/auth/verify-code", json={
            "code": test_code
        })
        data = json.loads(response.data)
        assert response.status_code == 200
        assert data["code"] == 200
        assert data["message"] == "验证成功"
        # 验证验证码被删除
        assert not self.redis.exists("verifycodeguest")

    def test_verify_code_invalid(self):
        """测试验证码错误"""
        test_code = "123456"
        self.redis.setex("verifycodeguest", 600, test_code)

        response = self.client.post("/api/auth/verify-code", json={
            "code": "wrong_code"
        })
        data = json.loads(response.data)
        assert response.status_code == 400
        assert data["code"] == 400
        assert "验证码错误或已过期" in data["message"]

    def test_verify_code_expired(self):
        """测试验证码过期"""
        test_code = "123456"
        self.redis.setex("verifycodeguest", 1, test_code)
        import time
        time.sleep(1.1)

        response = self.client.post("/api/auth/verify-code", json={
            "code": test_code
        })
        data = json.loads(response.data)
        assert response.status_code == 400
        assert data["code"] == 400
        assert "验证码错误或已过期" in data["message"]

    @patch("requests.post")
    def test_send_code_service_error(self, mock_post):
        """测试邮件服务异常"""
        mock_post.side_effect = Exception("Mailgun API error")
        response = self.client.post("/api/auth/send-code", json={
            "email": "<EMAIL>"
        })
        data = json.loads(response.data)
        assert response.status_code == 400
        assert data["code"] == 400
        assert "发送失败" in data["message"]

    def test_verify_code_missing_parameter(self):
        """测试验证时缺少验证码参数"""
        response = self.client.post("/api/auth/verify-code", json={})
        data = json.loads(response.data)
        assert response.status_code == 400
        assert data["code"] == 400
        assert "缺少必要参数" in data["message"]
