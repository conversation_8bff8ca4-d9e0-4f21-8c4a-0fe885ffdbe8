"""项目 API 测试模块"""

import json
from datetime import datetime, timezone, timedelta

import pytest

from app.models.base import db
from app.models.device import Device
from app.models.device_project import DeviceProject
from app.models.metrics import ServiceMetricsSnapshot, ServiceMetricsDetail
from app.models.project import Project
from app.models.project_file import ProjectFile
from app.models.service_config import ServiceConfig
from app.models.user import User
from app.services.project_service import ProjectService


@pytest.fixture
def client(app):
    return app.test_client()


@pytest.fixture
def admin_user(app):
    """创建管理员用户"""
    with app.app_context():
        user = User(username="admin", email="<EMAIL>", role="admin")
        user.set_password("admin")
        db.session.add(user)
        db.session.commit()

        yield user

        User.query.filter_by(username="admin").delete()
        db.session.commit()


@pytest.fixture
def admin_token(app, client, admin_user):
    """获取管理员token"""
    response = client.post(
        "/api/auth/login", json={"username": "admin", "password": "admin"}
    )
    return json.loads(response.data)["data"]["token"]


@pytest.fixture
def normal_user(app):
    """创建普通用户"""
    with app.app_context():
        user = User(username="test_user", email="<EMAIL>", role="user")
        user.set_password("password")
        db.session.add(user)
        db.session.commit()

        yield user

        User.query.filter_by(username="test_user").delete()
        db.session.commit()


@pytest.fixture
def normal_user_token(app, client, normal_user):
    """获取普通用户token"""
    response = client.post(
        "/api/auth/login", json={"username": "test_user", "password": "password"}
    )
    return json.loads(response.data)["data"]["token"]


@pytest.fixture
def normal_user_headers(normal_user_token):
    """获取普通用户认证头"""
    return {"Authorization": f"Bearer {normal_user_token}"}


@pytest.fixture
def test_device(app, normal_user):
    """创建测试设备"""
    with app.app_context():
        device = Device(
            name="test-device",
            description="Test Device",
            status="active",
            ip_address="***********",
            mac_address="00:11:22:33:44:55",
            owner_id=normal_user.id
        )
        db.session.add(device)
        db.session.commit()

        yield device

        Device.query.filter_by(name="test-device").delete()
        db.session.commit()


@pytest.fixture
def service_config(app):
    """创建服务配置"""
    with app.app_context():
        # 清理可能存在的配置
        ServiceConfig.query.filter_by(name="test-service").delete()
        db.session.commit()

        # 创建新配置
        config = ServiceConfig(
            name="test-service",
            description="Test Service Config",
            docker_compose={
                "version": "3",
                "services": {
                    "app": {
                        "image": "test-image:latest",
                        "environment": {
                            "CONFIG_FILE": "/app/config/app.conf"
                        }
                    }
                }
            }
        )
        db.session.add(config)
        db.session.commit()

        yield config

        # 清理
        ServiceConfig.query.filter_by(name="test-service").delete()
        db.session.commit()


@pytest.fixture
def auth_headers(client, admin_token):
    """获取认证头"""
    return {"Authorization": f"Bearer {admin_token}"}


@pytest.fixture
def test_project(app, service_config):
    """创建测试项目"""
    with app.app_context():
        project_name = "test-project-files"
        # 清理测试数据
        Project.query.filter_by(name=project_name).delete()
        db.session.commit()

        # 创建项目
        project = Project(
            name=project_name,
            description="Test Project",
            service_config_id=service_config.id,
        )
        db.session.add(project)
        db.session.commit()

        yield project

        # 清理
        Project.query.filter_by(name=project_name).delete()
        db.session.commit()


@pytest.fixture
def project_with_metrics(app, test_project):
    """创建带有指标数据的测试项目"""
    from app.models.metrics import ServiceMetrics
    from app.models.device import Device
    from app.models.device_project import DeviceProject
    from datetime import datetime

    with app.app_context():
        # 创建第二个项目
        project2 = Project(
            name="test-project-2",
            description="Test Project 2",
            service_config_id=test_project.service_config_id
        )
        db.session.add(project2)
        db.session.flush()

        # 创建两个测试设备
        devices = []
        for i in range(2):
            device = Device(
                name=f"test-device-{i}",
                description=f"Test Device {i}",
                ip_address=f"192.168.1.{i+10}",
                mac_address=f"00:11:22:33:44:{i+50}",
                owner_id=1  # 假设是管理员ID
            )
            db.session.add(device)
            db.session.flush()
            devices.append(device)

            # 创建服务指标数据
            metrics = ServiceMetrics(
                device_id=device.id,
                service_name=test_project.name,  # 使用项目名称作为服务名称
                points=(i + 1) * 100,
                ubi_points=10,
                running_time=(i + 1) * 3600,
                updated_at=datetime.utcnow()
            )
            db.session.add(metrics)
            yesterday = (datetime.now(timezone.utc) - timedelta(days=1))

            metrics_snapshot_yesterday = ServiceMetricsSnapshot(
                device_id=device.id,
                service_name=test_project.name,  # 使用项目名称作为服务名称
                points=(i + 1) * 100,
                ubi_points=10,
                running_time=(i + 1) * 3600,
                day=yesterday.strftime('%Y-%m-%d'),
            )
            metrics_snapshot_yesterday.created_at = yesterday
            db.session.add(metrics_snapshot_yesterday)

            # 为第二个设备添加第二个项目的指标数据
            if i == 1:
                metrics2 = ServiceMetrics(
                    device_id=device.id,
                    service_name=project2.name,  # 使用第二个项目的名称
                    points=200,
                    ubi_points=5,
                    running_time=7200,
                    updated_at=datetime.utcnow()
                )
                metrics2_snapshot_yesterday = ServiceMetricsSnapshot(
                        device_id=device.id,
                        service_name=project2.name,
                        day=yesterday.strftime('%Y-%m-%d'),
                        ubi_points=5,
                        running_time=1,
                    )
                metrics2_snapshot_yesterday.created_at = yesterday
                db.session.add(metrics2)
                db.session.add(metrics2_snapshot_yesterday)

        # 第一个设备只关联第一个项目
        device_project1 = DeviceProject(
            device_id=devices[0].id,
            project_id=test_project.id,
            data={"test": "config"}
        )
        db.session.add(device_project1)

        # 第二个设备关联两个项目
        device_project2 = DeviceProject(
            device_id=devices[1].id,
            project_id=test_project.id,
            data={"test": "config"}
        )
        device_project3 = DeviceProject(
            device_id=devices[1].id,
            project_id=project2.id,
            data={"test": "config"}
        )
        db.session.add(device_project2)
        db.session.add(device_project3)

        db.session.commit()

        # 清理
        yield test_project

        # 清理数据
        DeviceProject.query.filter(DeviceProject.project_id.in_([test_project.id, project2.id])).delete()
        ServiceMetrics.query.filter(ServiceMetrics.device_id.in_([d.id for d in devices])).delete()
        Device.query.filter(Device.id.in_([d.id for d in devices])).delete()
        Project.query.filter_by(id=project2.id).delete()
        db.session.commit()


def test_create_project(app, client, service_config, auth_headers):
    """测试创建项目"""
    with app.app_context():
        project_name = "test-project-create"
        # 清理测试数据
        Project.query.filter_by(name=project_name).delete()
        db.session.commit()

        # 创建项目
        response = client.post(
            "/api/projects",
            headers=auth_headers,
            json={
                "name": project_name,
                "description": "Test Project",
                "service_config_id": service_config.id,
                "service_compose": {
                    "services": {
                        "app": {
                            "image": "test-image:latest",
                            "environment": {
                                "API_KEY": "${api_key}"
                            }
                        }
                    }
                },
                "form_schema": {
                    "type": "object",
                    "properties": {
                        "api_key": {
                            "type": "string",
                            "title": "API Key"
                        }
                    }
                }
            },
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["code"] == 200
        assert data["message"] == "项目创建成功"
        assert data["data"]["name"] == project_name
        assert data["data"]["description"] == "Test Project"
        assert data["data"]["service_config_id"] == service_config.id
        assert "service_compose" in data["data"]
        assert "services" in data["data"]["service_compose"]
        assert "app" in data["data"]["service_compose"]["services"]


def test_get_projects(app, client, service_config, auth_headers):
    """测试获取项目列表"""
    with app.app_context():
        # 清理测试数据
        Project.query.filter(Project.name.startswith("test-project-list")).delete()
        db.session.commit()

        # 创建测试项目
        for i in range(3):
            project = Project(
                name=f"test-project-list-{i}",
                description=f"Test Project {i}",
                service_config_id=service_config.id,
            )
            db.session.add(project)
        db.session.commit()

        # 获取项目列表
        response = client.get("/api/projects", headers=auth_headers)
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["code"] == 200
        assert len(data["data"]["items"]) >= 3

def test_update_device_projects_proxy_not_required(app, client, test_project, test_device, auth_headers):
    """测试获取项目列表"""
    with app.app_context():
        # 创建设备项目关联

        test_project.form_schema = {"not":{
            "required":["proxy"]
        }}

        device_project = DeviceProject(
            device_id=test_device.id,
            project_id=test_project.id,
            state="created",
            data={},
        )
        db.session.add(device_project)
        db.session.commit()

        # 获取项目列表
        response = client.put("/api/projects/devices/1/1", headers=auth_headers, json={"proxy_enable":True, "proxy_scope": "Device proxy"})
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data["message"] == "该项目不允许配置代理"

def test_update_device_projects_proxy_scope_device(app, client, test_project, test_device, auth_headers):
    """测试获取项目列表"""
    with app.app_context():
        # 创建设备项目关联
        device_project = DeviceProject(
            device_id=test_device.id,
            project_id=test_project.id,
            state="created",
            data={},
        )
        db.session.add(device_project)
        db.session.commit()

        # 获取项目列表
        response = client.put("/api/projects/devices/1/1", headers=auth_headers,
                              json={"proxy_enable": True, "proxy_scope": "Device proxy"})
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data["message"] == "设备代理未配置"

def test_update_device_projects_proxy_scope_project(app, client, test_project, test_device, auth_headers):
    """测试获取项目列表"""
    with app.app_context():
        # 创建设备项目关联
        device_project = DeviceProject(
            device_id=test_device.id,
            project_id=test_project.id,
            state="created"
        )
        db.session.add(device_project)
        db.session.commit()

        # 获取项目列表
        response = client.put("/api/projects/devices/1/1", headers=auth_headers,
                              json={"email": "123", "password": "456"})
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["code"] == 200

def test_update_device_projects(app, client, test_project, test_device, auth_headers):
    """测试获取项目列表"""
    with app.app_context():
        # 创建设备项目关联
        device_project = DeviceProject(
            device_id=test_device.id,
            project_id=test_project.id,
            state="created",
            data={"proxy":""},
        )
        db.session.add(device_project)
        db.session.commit()

        # 获取项目列表
        response = client.put("/api/projects/devices/1/1", headers=auth_headers,
                              json={"proxy_enable": True, "proxy_scope": "Project proxy"})
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data["message"] == "项目代理未配置"

def test_get_project_detail(app, client, service_config, auth_headers):
    """测试获取项目详情"""
    with app.app_context():
        project_name = "test-project-detail"
        # 清理测试数据
        Project.query.filter_by(name=project_name).delete()
        db.session.commit()

        # 创建测试项目
        project = Project(
            name=project_name,
            description="Test Project",
            service_config_id=service_config.id,
            status=Project.STATUS_ENABLED
        )
        db.session.add(project)
        db.session.commit()

        # 添加项目文件
        project_file = ProjectFile(
            name="test.txt",
            content="test content",
            project=project
        )
        db.session.add(project_file)
        db.session.commit()

        # 获取项目详情
        response = client.get(f"/api/projects/{project.id}", headers=auth_headers)
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["code"] == 200
        assert data["data"]["name"] == project_name
        assert data["data"]["description"] == "Test Project"
        assert data["data"]["status"] == Project.STATUS_ENABLED
        assert len(data["data"]["files"]) == 1


def test_update_project(app, client, service_config, auth_headers):
    """测试更新项目"""
    with app.app_context():
        project_name = "test-project-update"
        # 清理测试数据
        Project.query.filter(
            Project.name.in_([project_name, "updated-project"])
        ).delete()
        db.session.commit()

        # 创建测试项目
        project = Project(
            name=project_name,
            description="Test Project",
            service_config_id=service_config.id,
            service_compose={
                "services": {
                    "app": {
                        "image": "test-image:latest"
                    }
                }
            }
        )
        db.session.add(project)
        db.session.commit()

        # 更新项目
        response = client.put(
            f"/api/projects/{project.id}",
            headers=auth_headers,
            json={
                "name": "updated-project",
                "description": "Updated Project",
                "service_compose": {
                    "services": {
                        "app": {
                            "image": "test-image:v2",
                            "environment": {
                                "DEBUG": "true"
                            }
                        }
                    }
                }
            },
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["code"] == 200
        assert data["message"] == "项目更新成功"
        assert data["data"]["name"] == "updated-project"
        assert data["data"]["description"] == "Updated Project"
        assert data["data"]["service_compose"]["services"]["app"]["image"] == "test-image:v2"
        assert "DEBUG" in data["data"]["service_compose"]["services"]["app"]["environment"]

        # 清理
        db.session.delete(project)
        db.session.commit()


def test_delete_project(app, client, service_config, auth_headers):
    """测试删除项目"""
    with app.app_context():
        # 创建测试项目
        project = Project(
            name="test-project",
            description="Test Project",
            service_config_id=service_config.id,
        )
        db.session.add(project)
        db.session.commit()
        project_id = project.id

        # 删除项目
        response = client.delete(f"/api/projects/{project_id}", headers=auth_headers)
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["code"] == 200
        assert data["message"] == "项目删除成功"

        # 验证项目已被软删除
        project = db.session.get(Project, project_id)
        assert project is not None
        assert project.deleted_at is not None

        # 验证项目不会出现在查询结果中
        assert Project.not_deleted().filter_by(id=project_id).first() is None


def test_project_name_unique(app, client, service_config, auth_headers):
    """测试项目名称唯一性"""
    with app.app_context():
        project_name = "test-project-unique"
        # 清理测试数据
        Project.query.filter_by(name=project_name).delete()
        db.session.commit()

        # 创建测试项目
        project = Project(
            name=project_name,
            description="Test Project",
            service_config_id=service_config.id,
            status=Project.STATUS_ENABLED
        )
        db.session.add(project)
        db.session.commit()

        # 尝试创建同名项目
        response = client.post(
            "/api/projects",
            headers=auth_headers,
            json={
                "name": project_name,
                "description": "Another Project",
                "service_config_id": service_config.id,
            },
        )
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data["code"] == 400
        assert "项目名称已存在" in data["message"]

        # 清理
        db.session.delete(project)
        db.session.commit()


def test_project_file_operations(app, test_project):
    """测试项目文件操作"""
    with app.app_context():
        service = ProjectService()
        # 测试添加文件
        file, error = service.add_project_file(
            test_project.id, "test.txt", "test content"
        )
        assert error is None
        assert file.name == "test.txt"
        assert file.content == "test content"

        # 测试更新文件
        file, error = service.update_project_file(
            test_project.id, file.id, {"content": "new content"}
        )
        assert error is None
        assert file.content == "new content"

        # 测试删除文件
        success, error = service.delete_project_file(test_project.id, file.id)
        assert error is None
        assert success is True


def test_create_project_with_status(app, client, service_config, auth_headers):
    """测试创建带状态的项目"""
    with app.app_context():
        project_name = "test-project-status"
        # 清理测试数据
        Project.query.filter_by(name=project_name).delete()
        db.session.commit()

        # 创建禁用状态的项目
        response = client.post(
            "/api/projects",
            headers=auth_headers,
            json={
                "name": project_name,
                "description": "Test Project",
                "service_config_id": service_config.id,
                "status": Project.STATUS_DISABLED,
            },
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["code"] == 200
        assert data["data"]["status"] == Project.STATUS_DISABLED

        # 清理测试数据
        Project.query.filter_by(name=project_name).delete()
        db.session.commit()


def test_update_project_status(app, client, service_config, auth_headers):
    """测试更新项目状态"""
    with app.app_context():
        project_name = "test-project-status-update"
        # 清理测试数据
        Project.query.filter_by(name=project_name).delete()
        db.session.commit()

        # 创建启用状态的项目
        project = Project(
            name=project_name,
            description="Test Project",
            service_config_id=service_config.id,
            status=Project.STATUS_ENABLED,
        )
        db.session.add(project)
        db.session.commit()

        # 更新为禁用状态
        response = client.put(
            f"/api/projects/{project.id}",
            headers=auth_headers,
            json={"status": Project.STATUS_DISABLED},
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["code"] == 200
        assert data["data"]["status"] == Project.STATUS_DISABLED
        assert data["data"]["status_text"] == "disabled"

        # 清理测试数据
        Project.query.filter_by(name=project_name).delete()
        db.session.commit()


def test_filter_projects_by_status(app, client, service_config, auth_headers):
    """测试按状态筛选项目"""
    with app.app_context():
        # 清理测试数据
        Project.query.filter(Project.name.startswith("test-project-filter")).delete()
        db.session.commit()

        # 创建不同状态的测试项目
        enabled_project = Project(
            name="test-project-filter-enabled",
            description="Enabled Project",
            service_config_id=service_config.id,
            status=Project.STATUS_ENABLED,
        )
        disabled_project = Project(
            name="test-project-filter-disabled",
            description="Disabled Project",
            service_config_id=service_config.id,
            status=Project.STATUS_DISABLED,
        )
        db.session.add(enabled_project)
        db.session.add(disabled_project)
        db.session.commit()

        # 获取启用状态的项目
        response = client.get(
            "/api/projects",
            headers=auth_headers,
            query_string={"status": Project.STATUS_ENABLED},
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["code"] == 200
        enabled_items = [
            item
            for item in data["data"]["items"]
            if item["name"].startswith("test-project-filter")
        ]
        assert len(enabled_items) == 1
        assert enabled_items[0]["status"] == Project.STATUS_ENABLED

        # 获取禁用状态的项目
        response = client.get(
            "/api/projects",
            headers=auth_headers,
            query_string={"status": Project.STATUS_DISABLED},
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["code"] == 200
        disabled_items = [
            item
            for item in data["data"]["items"]
            if item["name"].startswith("test-project-filter")
        ]
        assert len(disabled_items) == 1
        assert disabled_items[0]["status"] == Project.STATUS_DISABLED

        # 清理测试数据
        Project.query.filter(Project.name.startswith("test-project-filter")).delete()
        db.session.commit()


def test_cannot_disable_used_project(app, client, service_config, auth_headers, normal_user):
    """测试无法禁用正在使用的项目"""
    with app.app_context():
        project_name = "test-project-in-use"
        # 清理测试数据
        Project.query.filter_by(name=project_name).delete()
        db.session.commit()

        # 创建启用状态的项目
        project = Project(
            name=project_name,
            description="Test Project",
            service_config_id=service_config.id,
            status=Project.STATUS_ENABLED,
        )
        db.session.add(project)
        db.session.commit()

        # 创建设备
        device = Device(
            name="test-device-in-use",
            description="Test Device",
            status="active",
            ip_address="***********",
            mac_address="00:11:22:33:44:55",
            owner_id=normal_user.id
        )
        db.session.add(device)
        db.session.commit()

        # 创建设备项目关联
        device_project = DeviceProject(
            device_id=device.id,
            project_id=project.id,
            state="created",
            data={},
        )
        db.session.add(device_project)
        db.session.commit()

        # 尝试禁用项目
        response = client.put(
            f"/api/projects/{project.id}",
            headers=auth_headers,
            json={"status": Project.STATUS_DISABLED},
        )
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data["code"] == 400
        assert "项目正在被设备使用，无法禁用" in data["message"]

        # 清理
        db.session.delete(device_project)
        db.session.delete(device)
        db.session.delete(project)
        db.session.commit()


def test_normal_user_cannot_create_project(
    app, client, service_config, normal_user_headers
):
    """测试普通用户无法创建项目"""
    response = client.post(
        "/api/projects",
        headers=normal_user_headers,
        json={
            "name": "test-project",
            "description": "Test Project",
            "service_config_id": service_config.id,
        },
    )
    assert response.status_code == 403
    assert response.json["message"] == "权限不足"


def test_normal_user_can_view_authorized_project(
    app, client, test_project, normal_user, normal_user_headers
):
    """测试普通用户可以查看被授权的项目"""
    with app.app_context():
        # 创建设备并设置所有者为普通用户
        device = Device(
            name="auth-test-device",
            description="Auth Test Device",
            status="active",
            ip_address="***********00",
            mac_address="00:11:22:33:44:66",
            owner_id=normal_user.id
        )
        db.session.add(device)
        db.session.commit()

        # 创建设备项目关联
        device_project = DeviceProject(
            device_id=device.id,
            project_id=test_project.id,
            state="created"
        )
        db.session.add(device_project)
        db.session.commit()

        # 测试访问项目
        response = client.get(
            f"/api/projects/{test_project.id}",
            headers=normal_user_headers
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["code"] == 200
        assert data["data"]["id"] == test_project.id

        # 清理
        DeviceProject.query.filter_by(device_id=device.id).delete()
        Device.query.filter_by(id=device.id).delete()
        db.session.commit()


def test_normal_user_cannot_view_unauthorized_project(
    app, client, test_project, normal_user_headers
):
    """测试普通用户无法查看未授权的项目"""
    response = client.get(
        f"/api/projects/{test_project.id}",
        headers=normal_user_headers
    )
    assert response.status_code == 403
    data = json.loads(response.data)
    assert data["code"] == 403
    assert "没有权限访问该项目" in data["message"]


def test_device_project_permission(
    app, client, test_project, test_device, normal_user, normal_user_headers
):
    """测试设备项目权限"""
    with app.app_context():
        # 设置设备所有者为普通用户（只读权限）
        test_device.owner_id = normal_user.id
        db.session.commit()

        # 测试创建设备项目配置(应该成功，因为是设备所有者)
        response = client.post(
            f"/api/projects/devices/{test_device.id}/{test_project.id}",
            headers=normal_user_headers,
            json={"test": "data"},
        )
        assert response.status_code == 200
        assert response.json["code"] == 200

        # 清理
        DeviceProject.query.filter_by(
            device_id=test_device.id,
            project_id=test_project.id
        ).delete()
        db.session.commit()


def test_merge_service_configs(app, client, service_config, auth_headers,admin_user):
    """测试服务配置合并功能"""
    with app.app_context():
        project_name = "test-project-merge"
        # 清理测试数据
        Project.query.filter_by(name=project_name).delete()
        db.session.commit()

        # 创建基础服务配置
        base_config = ServiceConfig(
            name="base-service",
            description="Base Service Config",
            docker_compose={
                "version": "3",
                "services": {
                    "app": {
                        "image": "base-image:latest",
                        "restart": "always",
                        "volumes": ["./data:/app/data"]
                    }
                }
            }
        )
        db.session.add(base_config)
        db.session.commit()


        # 创建项目
        project = Project(
            name=project_name,
            description="Test Project",
            service_config_id=base_config.id,
            service_compose={
                "services": {
                    "app": {
                        "image": "custom-image:latest",
                        "environment": {
                            "API_KEY": "{{ api_key }}"
                        }
                    }
                }
            },
            form_schema={
                "type": "object",
                "properties": {
                    "api_key": {
                        "type": "string",
                        "title": "API Key"
                    }
                }
            }
        )
        db.session.add(project)
        db.session.commit()

        # 添加代理配置文件
        project_file = ProjectFile(
            project_id=project.id,
            name="config/proxy.conf",
            content="{{ proxy }}"
        )
        db.session.add(project_file)
        db.session.commit()

        # 添加钱包配置文件
        wallet_file = ProjectFile(
            project_id=project.id,
            name="config/wallet.txt",
            content="{{ wallet_data }}"
        )
        db.session.add(wallet_file)
        db.session.commit()

        # 创建测试设备
        device = Device(
            name="test-device",
            description="Test Device",
            ip_address="***********00",
            owner_id=admin_user.id,
            mac_address="00:11:22:33:44:55",
            service_configs={}
        )
        db.session.add(device)
        db.session.commit()

        # 创建设备项目配置
        device_project = DeviceProject(
            device_id=device.id,
            project_id=project.id,
            data={"api_key": "test-key-123", 'proxy_enable': True, 'proxy_scope': 'Device proxy', 'proxy': '***********'}
        )
        db.session.add(device_project)
        db.session.commit()

        # 生成服务配置
        project_service = ProjectService()
        config = project_service.generate_device_project_config(device_project)

        # 验证配置
        assert config is not None
        assert "docker_compose" in config
        assert "services" in config["docker_compose"]
        assert "app" in config["docker_compose"]["services"]
        assert config["docker_compose"]["services"]["app"]["image"] == "custom-image:latest"
        assert config["docker_compose"]["services"]["app"]["environment"]["API_KEY"] == "test-key-123"
        assert config["files"]["config/proxy.conf"] == ''
        # 检查钱包数据
        wallet_content = config["files"]["config/wallet.txt"]
        assert isinstance(wallet_content, str), "钱包数据应该是字符串类型"
        wallet_info = json.loads(wallet_content)
        assert wallet_info['items'] is not None, "钱包数据应该包含items字段"
        assert wallet_info['total'] is not None, "钱包数据应该包含total字段"

        device_project.data = {"api_key": "test-key-123", 'proxy_enable': True, 'proxy_scope': 'Project proxy', 'proxy': '***********'}
        db.session.commit()
        config = project_service.generate_device_project_config(device_project)
        assert config["files"]["config/proxy.conf"] == "***********"
        # 检查钱包数据
        wallet_content = config["files"]["config/wallet.txt"]
        assert isinstance(wallet_content, str), "钱包数据应该是字符串类型"
        wallet_info = json.loads(wallet_content)
        assert wallet_info['items'] is not None, "钱包数据应该包含items字段"
        assert wallet_info['total'] is not None, "钱包数据应该包含total字段"

        device_project.data = {"api_key": "test-key-123", 'proxy_enable': False, 'proxy_scope': 'Project proxy', 'proxy': '***********'}
        db.session.commit()
        config = project_service.generate_device_project_config(device_project)
        assert config["files"].get("config/proxy.conf") == ''
        # 检查钱包数据
        wallet_content = config["files"]["config/wallet.txt"]
        assert isinstance(wallet_content, str), "钱包数据应该是字符串类型"
        wallet_info = json.loads(wallet_content)
        assert wallet_info['items'] is not None, "钱包数据应该包含items字段"
        assert wallet_info['total'] is not None, "钱包数据应该包含total字段"

        # 清理
        db.session.delete(device_project)
        db.session.delete(device)
        db.session.delete(project)
        db.session.delete(base_config)
        db.session.commit()


def test_merge_service_configs_with_base(app, client, auth_headers):
    """测试基础服务配置和项目服务配置的合并"""
    with app.app_context():
        # 创建基础服务配置
        base_config = ServiceConfig(
            name="base-service",
            description="Base Service Config",
            docker_compose={
                "version": "3",
                "services": {
                    "nginx": {
                        "image": "nginx:latest"
                    },
                    "redis": {
                        "image": "redis:latest"
                    }
                }
            }
        )
        db.session.add(base_config)
        db.session.commit()

        # 创建项目
        project = Project(
            name="test-project-merge",
            description="Test Project",
            service_config_id=base_config.id,
            service_compose={
                "services": {
                    "app": {
                        "image": "test-app:latest"
                    },
                    "db": {
                        "image": "mysql:latest"
                    }
                }
            }
        )
        db.session.add(project)
        db.session.commit()

        # 创建测试设备
        device = Device(
            name="test-device",
            description="Test Device",
            ip_address="***********00",
            mac_address="00:11:22:33:44:55",
            owner_id=1,
            service_configs={}
        )
        db.session.add(device)
        db.session.commit()

        # 创建设备项目配置
        device_project = DeviceProject(
            device_id=device.id,
            project_id=project.id,
            data={}
        )
        db.session.add(device_project)
        db.session.commit()

        # 生成服务配置
        project_service = ProjectService()
        config = project_service.generate_device_project_config(device_project)

        # 验证配置
        assert config is not None
        assert "docker_compose" in config
        compose = config["docker_compose"]

        # 验证基础服务存在
        assert "nginx" in compose["services"]
        assert compose["services"]["nginx"]["image"] == "nginx:latest"
        assert "redis" in compose["services"]
        assert compose["services"]["redis"]["image"] == "redis:latest"

        # 验证项目服务存在
        assert "app" in compose["services"]
        assert compose["services"]["app"]["image"] == "test-app:latest"
        assert "db" in compose["services"]
        assert compose["services"]["db"]["image"] == "mysql:latest"

        # 清理
        db.session.delete(device_project)
        db.session.delete(device)
        db.session.delete(project)
        db.session.delete(base_config)
        db.session.commit()


def test_project_file_rendering(app, client, service_config, auth_headers):
    """测试项目文件渲染功能"""
    with app.app_context():
        project_name = "test-project-render"
        # 清理测试数据
        Project.query.filter_by(name=project_name).delete()
        db.session.commit()

        # 创建项目
        project = Project(
            name=project_name,
            description="Test Project",
            service_config_id=service_config.id,
            service_compose={
                "services": {
                    "app": {
                        "image": "test-image:latest",
                        "environment": {
                            "CONFIG_FILE": "/app/config/app.conf"
                        }
                    }
                }
            },
            form_schema={
                "type": "object",
                "properties": {
                    "api_url": {
                        "type": "string",
                        "title": "API URL"
                    },
                    "api_key": {
                        "type": "string",
                        "title": "API Key"
                    }
                }
            }
        )
        db.session.add(project)
        db.session.commit()

        # 添加配置文件
        project_file = ProjectFile(
            project_id=project.id,
            name="config/app.conf",
            content="""
api_url={{ api_url }}
api_key={{ api_key }}
"""
        )
        db.session.add(project_file)
        db.session.commit()

        # 创建测试设备
        device = Device(
            name="test-device-2",
            description="Test Device 2",
            ip_address="***********01",
            mac_address="00:11:22:33:44:66",
            service_configs={},
            owner_id=1
        )
        db.session.add(device)
        db.session.commit()

        # 创建设备项目配置
        device_project = DeviceProject(
            device_id=device.id,
            project_id=project.id,
            data={
                "api_url": "https://api.example.com",
                "api_key": "test-key-456"
            }
        )
        db.session.add(device_project)
        db.session.commit()

        # 生成服务配置
        project_service = ProjectService()
        config = project_service.generate_device_project_config(device_project)

        # 验证配置
        assert config is not None
        assert "docker_compose" in config
        assert "files" in config
        assert "config/app.conf" in config["files"]
        assert "https://api.example.com" in config["files"]["config/app.conf"]
        assert "test-key-456" in config["files"]["config/app.conf"]

        # 清理
        db.session.delete(device_project)
        db.session.delete(device)
        db.session.delete(project_file)
        db.session.delete(project)
        db.session.commit()


def test_project_detail_metrics(app, client, auth_headers, project_with_metrics):
    """测试项目详情接口中的指标数据
    
    验证项目详情 API 返回的 metrics 数据是否正确，包括：
    1. 设备数量统计
    2. 累积运行时长计算
    3. 累积收益计算
    """
    response = client.get(
        f"/api/projects/{project_with_metrics.id}",
        headers=auth_headers
    )
    assert response.status_code == 200
    
    data = json.loads(response.data)["data"]
    metrics = data["metrics"]

    print(metrics)
    
    # 验证指标数据
 #   assert metrics["device_count"] == 2, "项目关联设备数量应该为2"
    assert metrics["total_running_time"] == 10800.0, "总运行时长应该为10800秒(3600+7200)"
    assert metrics["total_points"] == 300.0, "总收益应该为300点(100+200)"


def test_project_list_metrics(app, client, normal_user_headers, project_with_metrics):
    """测试项目列表接口中的指标数据
    
    验证项目列表 API 返回的每个项目是否都包含正确的 metrics 数据
    """
    response = client.get("/api/projects", headers=normal_user_headers)
    assert response.status_code == 200
    
    data = json.loads(response.data)["data"]
    
    # 获取两个项目的数据
    project1_data = next(
        (item for item in data["items"] if item["id"] == project_with_metrics.id),
        None
    )
    project2_data = next(
        (item for item in data["items"] if item["name"] == "test-project-2"),
        None
    )
    
    # 验证第一个项目的指标数据
    assert project1_data is not None, "未找到第一个测试项目"
    metrics1 = project1_data["metrics"]
    print("Project 1 metrics:", metrics1)
    
    assert metrics1["device_count"] == 2, "项目1关联设备数量应该为2"
    assert metrics1["total_running_time"] == 10800, "项目1总运行时长应该为10800秒(3600+7200)"
    assert metrics1["total_points"] == 20, "项目1总收益应该为20点(10+10)"
    assert metrics1["yesterday_points"] == 10, "昨日收益应为 10 点"
    
    # 验证第二个项目的指标数据
    assert project2_data is not None, "未找到第二个测试项目"
    metrics2 = project2_data["metrics"]
    print("Project 2 metrics:", metrics2)
    
    assert metrics2["device_count"] == 1, "项目2关联设备数量应该为1"
    assert metrics2["total_running_time"] == 7200, "项目2总运行时长应该为7200秒"
    assert metrics2["total_points"] == 5, "项目2总收益应该为5点"
    assert metrics2["yesterday_points"] == 5, "昨日收益应为 5 点"


def test_project_metrics_without_devices(app, client, auth_headers, test_project):
    """测试无关联设备时的项目指标数据
    
    验证当项目没有关联设备时，API 返回的 metrics 数据是否正确
    """
    response = client.get(f"/api/projects/{test_project.id}", headers=auth_headers)
    assert response.status_code == 200
    
    data = json.loads(response.data)["data"]
    metrics = data["metrics"]
    
    # 验证空指标数据
    # assert metrics["device_count"] == 0, "无关联设备时，设备数量应为0"
    assert metrics["total_running_time"] == 0, "无指标数据时，总运行时长应为0"
    assert metrics["total_points"] == 0, "无指标数据时，总收益应为0"


def test_device_projects_runtime(
    app, client, test_project, test_device, normal_user, normal_user_headers
):
    """测试设备项目列表接口中的 runtime 结构

    验证 /api/projects/devices/<device_id> 接口返回的每个设备项目是否都包含正确的 runtime 数据
    """
    with app.app_context():
        # 创建设备项目关联
        device_project = DeviceProject(
            device_id=test_device.id,
            project_id=test_project.id,
            data={"test": "config"}
        )
        db.session.add(device_project)

        # 创建设备运行时数据
        from app.models.metrics import ServiceMetrics
        metrics = ServiceMetrics(
            device_id=test_device.id,
            service_name=test_project.name,
            points=100,
            running_time=3600,  # 1小时
            updated_at=datetime.utcnow()
        )
        db.session.add(metrics)
        db.session.commit()

        # 调用接口获取设备项目列表
        response = client.get(
            f"/api/projects/devices/{test_device.id}",
            headers=normal_user_headers
        )
        assert response.status_code == 200
        data = json.loads(response.data)["data"]

        # 验证返回数据中包含 runtime 结构
        assert len(data) > 0
        for device_project in data:
            metrics = device_project["metrics"]
            assert "running_time" in metrics
            assert metrics["running_time"] == 3600  # 验证运行时间是否正确

        # 清理测试数据
        DeviceProject.query.filter_by(device_id=test_device.id).delete()
        ServiceMetrics.query.filter_by(device_id=test_device.id).delete()
        db.session.commit()


def test_list_device_projects_metrics(app, client, test_project, test_device, normal_user_headers):
    """Test device projects list API with metrics data"""
    with app.app_context():
        # Create device project association
        device_project = DeviceProject(
            device_id=test_device.id,
            project_id=test_project.id,
            state="stopped",
            data={"config": "test"}
        )
        db.session.add(device_project)

        # Create metrics data
        from app.models.metrics import ServiceMetrics
        metrics = ServiceMetrics(
            device_id=test_device.id,
            service_name=test_project.name,
            points=100,
            running_time=3600,  # 1 hour
            updated_at=datetime.utcnow()
        )

        db.session.add(metrics)

        metrics_detail = ServiceMetricsDetail(device_id=test_device.id, updated_at=datetime.now(timezone.utc) - timedelta(hours=3),
                             service_name=test_project.name, points=100, running_time=3600, status_code=400,status_msg='test_msg')
        db.session.add(metrics_detail)
        db.session.commit()

        normal_user_headers["Accept-Language"] = "en_US"
        # Test API response
        response = client.get(
            f"/api/projects/devices/{test_device.id}",
            headers=normal_user_headers
        )
        assert response.status_code == 200
        data = response.json["data"]

        # Verify response structure
        assert len(data) == 1
        device_project_data = data[0]
        assert "metrics" in device_project_data
        assert device_project_data["metrics"]["points"] == 100.0
        assert device_project_data["metrics"]["running_time"] == 3600.0
        assert device_project_data["status"]["code"] == 400
        assert device_project_data["status"]["status_message"]=="Service config error"

        # Test when metrics don't exist
        db.session.delete(metrics)
        db.session.commit()

        response = client.get(
            f"/api/projects/devices/{test_device.id}",
            headers=normal_user_headers
        )
        assert response.status_code == 200
        data = response.json["data"]
        device_project_data = data[0]
        assert device_project_data["metrics"]["points"] == 0
        assert device_project_data["metrics"]["running_time"] == 0

        # Cleanup
        db.session.delete(device_project)
        db.session.commit()
