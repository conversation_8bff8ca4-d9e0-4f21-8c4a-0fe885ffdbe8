import pytest
from unittest.mock import patch
from app.models.base import db
from app.models.user import User
from app.models.device import Device
from app.models.device_project import DeviceProject
from app.models.points import PointRecord
from app.models.project import Project  # 假设你的 Project 模型已存在
from app.models.service_config import ServiceConfig
from app.tasks.check_first_project_running import process_invite_records

@pytest.fixture
def setup_invite_data(app):
    """设置邀请记录及相关数据，使用较大的ID，防止冲突"""
    with app.app_context():
        # 创建测试用户，邀请者和被邀请者
        inviter = User(
            id=1001,
            username="inviter",
            email="<EMAIL>"
        )
        invitee = User(
            id=1002,
            username="invitee",
            email="<EMAIL>"
        )
        db.session.add_all([inviter, invitee])
        db.session.commit()

        # 创建邀请积分记录
        inviter_record = PointRecord(
            user_id=1001,      # 邀请者ID
            invitee_id=1002,   # 被邀请者ID
            points=0,
            record_type='invite'
        )
        invitee_record = PointRecord(
            user_id=1002,      # 被邀请者的积分记录
            invitee_id=None,
            points=0,
            record_type='invite'
        )
        db.session.add_all([inviter_record, invitee_record])
        db.session.commit()

        # 创建测试设备，并关联给被邀请者，设备ID设置较大
        device1 = Device(
            id=2001,
            owner_id=1002,
            name="test-device-1",
            ip_address="************",
            mac_address="AA:BB:CC:DD:EE:FF"
        )
        db.session.add(device1)
        db.session.commit()

        # 创建 ServiceConfig 测试实例
        service_config = ServiceConfig(
            id=4001,
            name="test-service-config",
            description="Test service config",
            docker_compose={"version": "3", "services": {"app": {"image": "test:latest"}}},
            default_env={"ENV_VAR": "value"}
        )
        db.session.add(service_config)
        db.session.commit()

        # 创建测试项目，并传入 service_config_id
        project = Project(
            id=3001,
            name="test-project",
            description="Test project",
            service_config_id=service_config.id,
            status="enabled"
        )
        db.session.add(project)
        db.session.commit()

        # 将设备和项目关联，状态设置为running，以触发邀请积分更新
        device_project = DeviceProject(
            device_id=device1.id,
            project_id=project.id,
            state="running"  # 运行状态
        )
        db.session.add(device_project)
        db.session.commit()

        yield {
            "inviter": inviter,
            "invitee": invitee,
            "inviter_record": inviter_record,
            "invitee_record": invitee_record,
            "device": device1,
            "project": project,
            "service_config": service_config
        }

        # 测试完成后清理数据
        db.session.query(DeviceProject).delete()
        db.session.query(Project).delete()
        db.session.query(ServiceConfig).delete()
        db.session.query(Device).delete()
        db.session.query(PointRecord).delete()
        db.session.query(User).delete()
        db.session.commit()


@pytest.fixture(autouse=True)
def mock_celery_task():
    """Mock Celery任务，使任务同步执行，便于测试"""
    with patch('celery.shared_task', lambda *args, **kwargs: lambda f: f):
        yield


def test_process_invite_records_success(app, setup_invite_data):
    """
    测试存在running状态的设备项目时，邀请积分正确更新：
    邀请者获得100分，被邀请者获得50分。
    """
    with app.app_context():
        inviter_record = setup_invite_data["inviter_record"]
        invitee_record = setup_invite_data["invitee_record"]

        # 验证初始积分均为0
        assert inviter_record.points == 0, "邀请者积分初始应为0"
        assert invitee_record.points == 0, "被邀请者积分初始应为0"

        # 运行Celery任务
        process_invite_records()

        # 从数据库中重新获取记录，检查积分更新结果
        updated_inviter_record = db.session.get(PointRecord, inviter_record.id)
        updated_invitee_record = db.session.get(PointRecord, invitee_record.id)

        assert updated_inviter_record.points == 2, "邀请者积分应更新为100"
        assert updated_invitee_record.points == 1, "被邀请者积分应更新为50"


def test_process_invite_records_no_running_project(app, setup_invite_data):
    """
    测试当设备项目状态不是running时，不更新邀请积分。
    """
    with app.app_context():
        # 修改设备项目状态为非running状态
        device_project = DeviceProject.query.filter_by(device_id=setup_invite_data["device"].id).first()
        device_project.state = "created"  # 非running状态
        db.session.commit()

        inviter_record = setup_invite_data["inviter_record"]
        invitee_record = setup_invite_data["invitee_record"]

        # 验证初始积分均为0
        assert inviter_record.points == 0, "邀请者积分初始应为0"
        assert invitee_record.points == 0, "被邀请者积分初始应为0"

        # 运行Celery任务
        process_invite_records()

        # 再次获取记录，积分应保持不变
        updated_inviter_record = db.session.get(PointRecord, inviter_record.id)
        updated_invitee_record = db.session.get(PointRecord, invitee_record.id)

        assert updated_inviter_record.points == 0, "邀请者积分应保持为0"
        assert updated_invitee_record.points == 0, "被邀请者积分应保持为0"
