"""项目服务测试模块"""

import pytest

from app.models.base import db
from app.models.device import Device
from app.models.device_project import DeviceProject
from app.models.project import Project
from app.models.service_config import ServiceConfig
from app.models.user import User
from app.services.project_service import ProjectService
from app.utils.exceptions import PermissionError


@pytest.fixture
def service_config(app):
    """创建服务配置"""
    with app.app_context():
        config = ServiceConfig(
            name="test-service",
            description="Test Service Config",
            docker_compose={
                "version": "3",
                "services": {
                    "test": {
                        "image": "test:latest"
                    }
                }
            }
        )
        db.session.add(config)
        db.session.commit()

        yield config

        ServiceConfig.query.filter_by(name="test-service").delete()
        db.session.commit()


@pytest.fixture
def admin_user(app):
    """创建管理员用户"""
    with app.app_context():
        user = User(
            username="admin",
            email="<EMAIL>",
            role="admin"
        )
        user.set_password("password")
        db.session.add(user)
        db.session.commit()

        yield user

        User.query.filter_by(username="admin").delete()
        db.session.commit()


@pytest.fixture
def normal_user(app):
    """创建普通用户"""
    with app.app_context():
        user = User(
            username="user",
            email="<EMAIL>",
            role="user"
        )
        user.set_password("password")
        db.session.add(user)
        db.session.commit()

        yield user

        User.query.filter_by(username="user").delete()
        db.session.commit()


@pytest.fixture
def test_device(app, admin_user):
    """创建测试设备"""
    with app.app_context():
        device = Device(
            name="test-device",
            description="Test Device",
            status=2,  # online
            ip_address="***********",
            mac_address="00:11:22:33:44:55",
            owner_id=admin_user.id
        )
        db.session.add(device)
        db.session.commit()

        yield device

        Device.query.filter_by(name="test-device").delete()
        db.session.commit()


def test_create_project(app, service_config, admin_user):
    """测试创建项目"""
    with app.app_context():
        service = ProjectService()
        project_data = {
            "name": "test-project",
            "description": "Test Project",
            "service_config_id": service_config.id,
            "form_schema": {
                "email": {"type": "email", "label": "邮箱"},
                "password": {"type": "password", "label": "密码"}
            }
        }

        # 测试管理员创建项目
        project, error = service.create_project(project_data, admin_user)
        assert error is None
        assert project.name == "test-project"
        assert project.description == "Test Project"
        assert project.service_config_id == service_config.id
        assert project.form_schema == project_data["form_schema"]

        # 清理
        Project.query.filter_by(name="test-project").delete()
        db.session.commit()


def test_normal_user_create_project(app, service_config, normal_user):
    """测试普通用户创建项目（应该失败）"""
    with app.app_context():
        service = ProjectService()
        project_data = {
            "name": "test-project",
            "description": "Test Project",
            "service_config_id": service_config.id
        }

        # 测试普通用户创建项目
        with pytest.raises(PermissionError) as exc_info:
            service.create_project(project_data, normal_user)
        assert str(exc_info.value) == "权限不足"


def test_get_project_list(app, service_config, admin_user, normal_user):
    """测试获取项目列表"""
    with app.app_context():
        service = ProjectService()

        # 创建测试项目
        projects = []
        for i in range(3):
            project = Project(
                name=f"test-project-{i}",
                description=f"Test Project {i}",
                service_config_id=service_config.id,
                status=Project.STATUS_ENABLED if i < 2 else Project.STATUS_DISABLED  # Use constants instead of hardcoded values
            )
            db.session.add(project)
            projects.append(project)
        db.session.commit()

        # 测试管理员获取所有项目
        data, error = service.get_projects(user=admin_user)
        assert error is None
        assert data["total"] >= 3
        assert len(data["items"]) >= 3
        # 验证管理员可以看到所有字段
        admin_project = next(p for p in data["items"] if p["name"].startswith("test-project"))
        assert "service_compose" in admin_project
        assert "files" in admin_project

        # 测试普通用户只能看到启用状态的项目
        data, error = service.get_projects(user=normal_user)
        assert error is None
        enabled_projects = [p for p in data["items"] if p["name"].startswith("test-project")]
        assert len(enabled_projects) == 2  # 只能看到两个启用状态的项目
        # 验证普通用户看不到敏感字段
        normal_project = enabled_projects[0]
        assert "service_compose" not in normal_project
        assert "files" not in normal_project

        # 测试按状态筛选
        data, error = service.get_projects(user=admin_user, status=Project.STATUS_ENABLED)
        assert error is None
        enabled_projects = [p for p in data["items"] if p["name"].startswith("test-project")]
        assert len(enabled_projects) == 2

        data, error = service.get_projects(user=admin_user, status=Project.STATUS_DISABLED)
        assert error is None
        disabled_projects = [p for p in data["items"] if p["name"].startswith("test-project")]
        assert len(disabled_projects) == 1

        # 清理测试数据
        for project in projects:
            db.session.delete(project)
        db.session.commit()


def test_update_project(app, service_config, admin_user, normal_user):
    """测试更新项目"""
    with app.app_context():
        service = ProjectService()

        # 创建测试项目
        project = Project(
            name="test-project",
            description="Test Project",
            service_config_id=service_config.id
        )
        db.session.add(project)
        db.session.commit()

        # 测试管理员更新项目
        update_data = {
            "name": "updated-project",
            "description": "Updated Project",
            "status": 0
        }
        updated_project, error = service.update_project(project.id, update_data, admin_user)
        assert error is None
        assert updated_project.name == "updated-project"
        assert updated_project.description == "Updated Project"
        assert updated_project.status == 0

        # 测试普通用户更新项目（应该失败）
        update_data = {"name": "user-update"}
        with pytest.raises(PermissionError) as exc_info:
            service.update_project(project.id, update_data, normal_user)
        assert str(exc_info.value) == "权限不足"

        # 清理
        Project.query.filter_by(id=project.id).delete()
        db.session.commit()


def test_delete_project(app, service_config, admin_user, normal_user):
    """测试删除项目"""
    with app.app_context():
        service = ProjectService()

        # 创建测试项目
        project = Project(
            name="test-project",
            description="Test Project",
            service_config_id=service_config.id
        )
        db.session.add(project)
        db.session.commit()
        project_id = project.id

        # 创建设备和设备项目关联
        device = Device(
            name="test-device",
            description="Test Device",
            status=2,  # online
            ip_address="***********",
            mac_address="00:11:22:33:44:55",
            owner_id=normal_user.id
        )
        db.session.add(device)
        db.session.commit()

        device_project = DeviceProject(
            device_id=device.id,
            project_id=project_id,
            state="created"
        )
        db.session.add(device_project)
        db.session.commit()

        # 测试删除正在使用的项目（应该失败）
        success, error = service.delete_project(project_id)
        assert not success
        assert "项目正在被设备使用" in error

        # 删除设备项目关联后再次尝试删除
        db.session.delete(device_project)
        db.session.commit()

        success, error = service.delete_project(project_id)
        assert success
        assert error is None

        # 验证项目已被软删除
        project = db.session.get(Project, project_id)
        assert project is not None
        assert project.deleted_at is not None

        # 验证项目不会出现在查询结果中
        assert Project.not_deleted().filter_by(id=project_id).first() is None

        # 清理测试数据
        db.session.delete(device)
        db.session.commit()
