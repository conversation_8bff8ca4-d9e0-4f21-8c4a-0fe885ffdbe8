"""测试指标服务"""
from venv import create

import pytest
from datetime import datetime, timedelta, timezone

from app.models import PointRecord
from app.models.base import db
from app.models.device_project import DeviceProject
from app.models.metrics import ServiceMetrics, ServiceMetricsSnapshot, ServiceMetricsDetail
from app.models.device import Device
from app.models.device_runtime import DeviceRuntime
from app.models.project import Project
from app.models.user import User
from app.services.metrics_service import metrics_service
from app.tasks.metrics_tasks import update_yesterday_metrics


def test_get_daily_statistics(app):
    """测试获取每日统计数据"""
    with app.app_context():
        # 创建测试用户
        user = User(
            username="test_user",
            email="<EMAIL>"
        )
        user.set_password("password")
        db.session.add(user)
        db.session.commit()

        # 创建测试设备
        device = Device(
            name="test_device",
            ip_address="***********",
            mac_address="00:11:22:33:44:55",
            owner_id=user.id
        )
        db.session.add(device)
        db.session.commit()

        try:
            # 创建测试数据
            base_date = datetime.now(timezone.utc).date()
            for days_ago in range(3):
                day = base_date - timedelta(days=days_ago)
                day_str = day.strftime('%Y-%m-%d')
                
                # 每天创建3个服务的快照
                for i, service_name in enumerate(['service_a', 'service_b', 'service_c']):
                    points = 100 * (i + 1)  # service_a: 100, service_b: 200, service_c: 300
                    snapshot = ServiceMetricsSnapshot(
                        device_id=device.id,
                        service_name=service_name,
                        day=day_str,
                        points=points,
                        running_time=points * 36,  # 简单地将运行时间设为积分的36倍
                        points_increase=points * 0.1,  # 增量为积分的10%
                        running_time_increase=points * 3.6,  # 增量为运行时间的10%
                        yesterday_points=points * 0.9,  # 昨日积分为当前积分的90%
                        yesterday_running_time=points * 32.4  # 昨日运行时间为当前运行时间的90%
                    )
                    db.session.add(snapshot)
            db.session.commit()

            # 测试获取统计数据
            start_date = (base_date - timedelta(days=2)).strftime('%Y-%m-%d')
            end_date = base_date.strftime('%Y-%m-%d')
            statistics = metrics_service.get_daily_statistics(user.id, start_date, end_date)

            # 验证返回的数据结构和内容
            assert len(statistics) == 3  # 应该有3天的数据
            for day_str in statistics:
                day_data = statistics[day_str]
                assert len(day_data['service_name']) == 3  # 每天应该有3个服务
                assert day_data['service_name'] == ['service_a', 'service_b', 'service_c']
                assert day_data['points'] == [100, 200, 300]  # 验证积分数据

        finally:
            # 清理测试数据
            ServiceMetricsSnapshot.query.delete()
            db.session.delete(device)
            db.session.delete(user)
            db.session.commit()


def test_update_metrics(app):
    """测试更新指标数据"""
    with app.app_context():
        # 创建测试用户
        user = User(
            username="test_user",
            email="<EMAIL>"
        )
        user.set_password("password")
        db.session.add(user)
        db.session.commit()

        # 创建测试设备
        device = Device(
            name="test_device",
            ip_address="***********",
            mac_address="00:11:22:33:44:55",
            owner_id=user.id
        )
        db.session.add(device)
        db.session.commit()

        project = Project(
            name="test_service",
            description="Test Project",
            service_config_id=123,
            status=Project.STATUS_ENABLED
        )
        db.session.add(project)
        db.session.commit()

        # 创建设备项目关联
        device_project = DeviceProject(
            device_id=device.id,
            project_id=project.id,
            state="created",
            data={"name": "test"}
        )
        db.session.add(device_project)
        db.session.commit()

        try:
            # 准备测试数据
            current_time = datetime.now(timezone.utc)
            time_str = current_time.strftime('%Y-%m-%dT%H:%M:%SZ')
            metrics_data = {
                'services': [
                    {
                        'service_name': 'test_service',
                        'started_at': time_str,
                        'running_time': 3600,
                        'total_points': 100,
                        'daily_points': 10,
                        'updated_at': time_str,
                        'status':{
                            'code': 400,
                            'message': '配置错误'
                        }
                    }
                ],
                'device': {
                    'started_at': time_str,
                    'running_time': 7200,
                    'lan_ip': '***********00',
                    'public_ip': '*******',
                    'network': {
                        'download_speed': 1000000,  # 1Mbps
                        'upload_speed': 500000      # 500Kbps
                    },
                    'disk': {
                        'total': 1000000000,  # 1GB
                        'used': 500000000,    # 500MB
                        'free': 500000000     # 500MB
                    },
                    'cpu': {
                        'usage': 50.5,        # 50.5%
                        'cores': 4
                    },
                    'memory': {
                        'total': 8000000000,  # 8GB
                        'used': 4000000000,   # 4GB
                        'free': 4000000000    # 4GB
                    },
                    'updated_at': time_str
                }
            }

            # 更新指标数据
            metrics_service.update_metrics(device.id, metrics_data)

            # 验证服务指标数据
            started_at = datetime.strptime(time_str, '%Y-%m-%dT%H:%M:%SZ').replace(tzinfo=timezone.utc)
            service_metrics = ServiceMetricsDetail.query.filter_by(
                device_id=device.id,
                service_name='test_service',
                started_at=started_at.replace(tzinfo=None)
            ).first()
            assert service_metrics is not None
            assert service_metrics.points == 100
            assert service_metrics.running_time == 3600
            assert service_metrics.started_at.replace(tzinfo=None) == started_at.replace(tzinfo=None)
            assert service_metrics.status_code == 400
            assert service_metrics.status_msg == '配置错误'

            # 验证设备运行时数据
            device_runtime = DeviceRuntime.query.filter_by(device_id=device.id).first()
            assert device_runtime is not None
            assert device_runtime.running_time == 7200
            assert device_runtime.lan_ip == '***********00'
            assert device_runtime.public_ip == '*******'

            # 验证网络信息
            assert device_runtime.network == {
                'download_speed': 1000000,
                'upload_speed': 500000
            }

            # 验证磁盘信息
            assert device_runtime.disk == {
                'total': 1000000000,
                'used': 500000000,
                'free': 500000000
            }

            # 验证CPU信息
            assert device_runtime.cpu == {
                'usage': 50.5,
                'cores': 4
            }

            # 验证内存信息
            assert device_runtime.memory == {
                'total': 8000000000,
                'used': 4000000000,
                'free': 4000000000
            }

        finally:
            # 清理测试数据
            ServiceMetricsDetail.query.filter_by(device_id=device.id).delete()
            DeviceRuntime.query.filter_by(device_id=device.id).delete()
            db.session.delete(device)
            db.session.delete(user)
            db.session.commit()


def test_update_yesterday_metrics(app):
    """测试更新昨日指标任务"""
    with app.app_context():
        # 创建测试用户
        user = User(
            username="test_yesterday_user",
            email="<EMAIL>"
        )
        user.set_password("password")
        db.session.add(user)
        db.session.commit()

        device = Device(
            name="test-project",
            description="Test Device",
            status="active",
            tags="test,device",
            ip_address="***********00",
            mac_address="00:11:22:33:44:55",
            owner_id=user.id
        )
        db.session.add(device)
        db.session.commit()

        project = Project(
            name="test-project",
            description="Test Project",
            service_config_id=123,
            status=Project.STATUS_ENABLED
        )
        db.session.add(project)
        db.session.commit()

        # 创建设备项目关联
        device_project = DeviceProject(
            device_id=device.id,
            project_id=project.id,
            state="created",
            data={"name": "test"}
        )
        db.session.add(device_project)
        db.session.commit()

        point_records = [PointRecord(
            user_id=user.id,
            points=0.01,
            record_type='project',
            related_id=project.id,
            created_at=(datetime.now(timezone.utc) - timedelta(days=1))
        ),
        PointRecord(
            user_id=user.id,
            points=0.02,
            record_type='project',
            related_id=project.id,
            created_at=(datetime.now(timezone.utc) - timedelta(days=1))
        ),
            PointRecord(
            user_id=user.id,
            points=0.02,
            record_type='project',
            related_id=project.id,
            created_at=(datetime.now(timezone.utc))
        ),PointRecord(
            user_id=user.id,
            points=0.01,
            record_type='project',
            related_id=999,
            created_at=(datetime.now(timezone.utc) - timedelta(days=1))
        )]

        db.session.add_all(point_records)
        db.session.commit()

        try:
            # 创建昨天的快照
            yesterday = (datetime.now(timezone.utc) - timedelta(days=1)).strftime("%Y-%m-%d")

            
            # 创建当前服务指标
            service_metrics = ServiceMetrics(
                device_id=project.id,
                service_name="test-project",
                points=160,
                running_time=5400
            )
            db.session.add(service_metrics)
            db.session.commit()
            
            # 运行任务
            result = update_yesterday_metrics()
            
            # 验证结果
            assert result["status"] == "success"
            
            # 验证今天的快照已更新
            updated_snapshot = ServiceMetricsSnapshot.query.filter_by(
                device_id=device.id,
                service_name="test-project",
                day=yesterday
            ).first()
            
            assert updated_snapshot is not None
            assert updated_snapshot.points == 160
            assert updated_snapshot.running_time == 5400
            assert updated_snapshot.ubi_points == 0.03
            
        finally:
            # 清理测试数据
            ServiceMetricsSnapshot.query.filter_by(device_id=device.id).delete()
            ServiceMetrics.query.filter_by(device_id=device.id).delete()
            for point_record in point_records:
                db.session.delete(point_record)
            db.session.delete(device_project)
            db.session.delete(project)
            db.session.delete(device)
            db.session.delete(user)
            db.session.commit()
