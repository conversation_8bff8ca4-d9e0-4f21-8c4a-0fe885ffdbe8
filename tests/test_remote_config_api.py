"""远程配置 API 测试模块"""
import json
from unittest.mock import patch

import pytest

from app.models.base import db
from app.models.device import Device
from app.models.device_project import DeviceProject
from app.models.project import Project
from app.models.service_config import ServiceConfig
from app.services.project_service import ProjectService
from app.utils.crypto import Crypto


@pytest.fixture
def test_device(app, admin_user):
    """创建测试设备"""
    with app.app_context():
        # 将 admin_user 添加到会话中
        db.session.add(admin_user)
        db.session.commit()

        # 清理已存在的设备
        Device.query.filter_by(name="test-device").delete()
        db.session.commit()

        # 创建新设备
        device = Device(
            name="test-device",
            mac_address="00:11:22:33:44:55",
            ip_address="*************",
            status=2,  # online
            tags="test",
            owner_id=admin_user.id,
            token="test-device-token"  # 添加设备 token
        )
        db.session.add(device)
        db.session.commit()

        return device


@pytest.fixture
def test_project(app, service_config):
    """创建测试项目"""
    with app.app_context():
        # 清理已存在的项目
        Project.query.filter_by(name="test-project").delete()
        db.session.commit()

        # 创建新项目
        project = Project(
            name="test-project",
            description="Test Project",
            service_config_id=service_config.id,
            status="enabled"
        )
        db.session.add(project)
        db.session.commit()
        return project


@pytest.fixture
def test_device_project(app, test_device, test_project):
    """创建设备项目关联"""
    with app.app_context():
        # 清理已存在的关联
        DeviceProject.query.filter_by(
            device_id=test_device.id,
            project_id=test_project.id
        ).delete()
        db.session.commit()

        # 创建新关联
        device_project = DeviceProject(
            device_id=test_device.id,
            project_id=test_project.id,
            state="updated",
            data={"test-service": {"key": "value"}}
        )
        db.session.add(device_project)
        db.session.commit()
        return device_project


def test_system_config_api(client, test_device):
    """测试系统配置 API"""
    # 将 test_device 添加到会话中
    db.session.add(test_device)

    # 设置系统配置
    test_device.system_app_configs = {"test": "value"}
    db.session.commit()

    # 发送请求
    response = client.get(
        "/api/remote/configs/system",
        headers={
            "Authorization": test_device.token
        }
    )

    # 验证响应
    assert response.status_code == 200
    data = json.loads(response.data)
    assert "encrypted" in data
    assert "nonce" in data


def test_services_config_api_no_config(client, test_device):
    """测试服务配置 API - 无配置"""
    # 将 test_device 添加到会话中
    db.session.add(test_device)
    db.session.commit()

    # 发送请求
    response = client.get(
        "/api/remote/configs/service",
        headers={
            "Authorization": test_device.token
        }
    )

    # 验证响应
    assert response.status_code == 404
    data = json.loads(response.data)
    assert "error" in data


def test_services_config_api_basic(client, test_device, test_project):
    """测试服务配置 API - 基本功能测试"""
    # 将 test_device 添加到会话中
    db.session.add(test_device)
    db.session.commit()

    # 将 test_project 添加到会话中
    db.session.add(test_project)
    db.session.commit()

    # 创建设备项目关联
    device_project = DeviceProject(
        device_id=test_device.id,
        project_id=test_project.id,
        state="updated",
        data={"test": "value"}
    )
    db.session.add(device_project)

    # 设置设备的服务配置
    test_device.service_configs = {
        "test-service": {
            "config": "value"
        }
    }
    db.session.commit()

    # 发送请求
    response = client.get(
        "/api/remote/configs/service",
        headers={
            "Authorization": test_device.token
        }
    )

    # 验证响应
    assert response.status_code == 200
    data = json.loads(response.data)
    assert "encrypted" in data
    assert "nonce" in data


@pytest.fixture
def service_config(app):
    """创建服务配置"""
    with app.app_context():
        config = ServiceConfig(
            name="test-service",
            description="Test Service",
            docker_compose="test-service",
        )
        db.session.add(config)
        db.session.commit()

        yield config

        ServiceConfig.query.filter_by(docker_compose="test-service").delete()
        db.session.commit()


@pytest.fixture
def project(app, service_config):
    """创建测试项目"""
    with app.app_context():
        project = Project(
            name="test-project",
            description="Test Project",
            service_config_id=service_config.id,
            status=Project.STATUS_ENABLED
        )
        db.session.add(project)
        db.session.commit()

        yield project

        Project.query.filter_by(name="test-project").delete()
        db.session.commit()


def test_services_config_api_with_files(app, client, test_device, project, project_files):
    """测试服务配置 API - 有配置"""
    with app.app_context():
        # 确保所有对象都在当前会话中
        test_device = db.session.merge(test_device)
        project = db.session.merge(project)
        project_files = [db.session.merge(pf) for pf in project_files]
        db.session.flush()

        # 创建设备项目关联
        device_project = DeviceProject(
            device_id=test_device.id,
            project_id=project.id,
            state="created",
            data={"name": "test"}
        )
        db.session.add(device_project)
        db.session.commit()

        # 更新设备服务配置
        project_service = ProjectService()
        project_service.update_device_project_config(project.id, device_project.id)
        db.session.commit()

        # 获取服务配置
        response = client.get(
            "/api/remote/configs/service",
            headers={"Authorization": test_device.token}
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert "encrypted" in data
        assert "nonce" in data


def test_get_device_token_success(app, client, test_device):
    """测试成功获取设备 token"""
    with app.app_context():
        # 将测试设备添加到会话中
        db.session.add(test_device)
        db.session.commit()

        # 加密 MAC 地址
        crypto = Crypto(app.config["ENCRYPTION_KEY"])
        mac_data = test_device.mac_address.encode("utf-8")
        encrypted, nonce = crypto.encrypt(mac_data)

        # 发送请求
        response = client.post("/api/remote/devices/token", json={
            "encrypted": encrypted,
            "nonce": nonce
        })

        # 验证响应
        assert response.status_code == 200
        data = json.loads(response.data)
        assert "encrypted" in data
        assert "nonce" in data

        # 验证返回的 token 是否正确
        decrypted_token = crypto.decrypt(data["encrypted"], data["nonce"]).decode("utf-8")
        assert decrypted_token == test_device.token


def test_get_device_token_invalid_request(client):
    """测试无效的请求数据"""
    # 测试缺少必要字段
    response = client.post("/api/remote/devices/token", json={})
    assert response.status_code == 400
    data = json.loads(response.data)
    assert data["error"] == "无效的请求数据"

    # 测试缺少 nonce
    response = client.post("/api/remote/devices/token", json={"encrypted": "test"})
    assert response.status_code == 400
    data = json.loads(response.data)
    assert data["error"] == "无效的请求数据"


def test_get_device_token_invalid_mac(app, client):
    """测试无效的 MAC 地址格式"""
    with app.app_context():
        # 加密一个无效的 MAC 地址
        crypto = Crypto(app.config["ENCRYPTION_KEY"])
        mac_data = "invalid-mac".encode("utf-8")
        encrypted, nonce = crypto.encrypt(mac_data)

        response = client.post("/api/remote/devices/token", json={
            "encrypted": encrypted,
            "nonce": nonce
        })

        assert response.status_code == 400
        data = json.loads(response.data)
        assert data["error"] == "无效的 MAC 地址"


def test_get_device_token_unregistered(app, client):
    """测试未注册的设备"""
    with app.app_context():
        # 加密一个未注册的 MAC 地址
        crypto = Crypto(app.config["ENCRYPTION_KEY"])
        mac_data = "AA:BB:CC:DD:EE:FF".encode("utf-8")
        encrypted, nonce = crypto.encrypt(mac_data)

        response = client.post("/api/remote/devices/token", json={
            "encrypted": encrypted,
            "nonce": nonce
        })

        assert response.status_code == 404
        data = json.loads(response.data)
        assert data["error"] == "设备未注册"


def test_get_device_token_decrypt_fail(client):
    """测试解密失败"""
    response = client.post("/api/remote/devices/token", json={
        "encrypted": "invalid-encrypted-data",
        "nonce": "invalid-nonce"
    })

    assert response.status_code == 400
    data = json.loads(response.data)
    assert data["error"] == "解密失败"


def test_get_device_token_case_insensitive(app, client, test_device):
    """测试 MAC 地址大小写不敏感"""
    with app.app_context():
        # 将测试设备添加到会话中
        db.session.add(test_device)
        db.session.commit()

        # 使用小写 MAC 地址
        crypto = Crypto(app.config["ENCRYPTION_KEY"])
        mac_data = test_device.mac_address.lower().encode("utf-8")
        encrypted, nonce = crypto.encrypt(mac_data)

        # 发送请求
        response = client.post("/api/remote/devices/token", json={
            "encrypted": encrypted,
            "nonce": nonce
        })

        # 验证响应
        assert response.status_code == 200
        data = json.loads(response.data)
        assert "encrypted" in data
        assert "nonce" in data

        # 验证返回的 token 是否正确
        decrypted_token = crypto.decrypt(data["encrypted"], data["nonce"]).decode("utf-8")
        assert decrypted_token == test_device.token


def test_jsonrpc_api_encrypted_echo(app, client, test_device):
    """测试 /api/remote/jsonrpc 加密请求体和响应体的回显功能"""
    with app.app_context():
        db.session.add(test_device)
        db.session.commit()

        # 构造明文 JSON-RPC 请求体
        jsonrpc_body = {
            "jsonrpc": "2.0",
            "method": "test_method",
            "params": [1, 2, 3],
            "id": 123
        }
        # 加密请求体
        crypto = Crypto(app.config["ENCRYPTION_KEY"])
        encrypted, nonce = crypto.encrypt(json.dumps(jsonrpc_body).encode("utf-8"))

        # 发送加密请求
        response = client.post(
            "/api/remote/jsonrpc",
            headers={"Authorization": test_device.token},
            json={"encrypted": encrypted, "nonce": nonce}
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert "encrypted" in data
        assert "nonce" in data

        # 解密响应体
        decrypted = crypto.decrypt(data["encrypted"], data["nonce"])
        resp_json = json.loads(decrypted)
        assert resp_json["jsonrpc"] == "2.0"
        assert resp_json["id"] == 123
        assert resp_json["result"]["echo_method"] == "test_method"
        assert resp_json["result"]["echo_params"] == [1, 2, 3]


def test_jsonrpc_api_encrypted_sign(app, client, test_device):
    """测试 /api/remote/jsonrpc 加密请求体和响应体的回显功能"""
    with app.app_context():
        db.session.add(test_device)
        db.session.commit()

        # 构造明文 JSON-RPC 请求体
        jsonrpc_body = {
            "jsonrpc": "2.0",
            "method": "sign_message",
            "params": ["SOL", "HMKZJDVAsLMWXxLSxg27h7ceHCe22yYMrk7NWjhYJPu7", "test message"],
            "id": 123
        }
        # 加密请求体
        crypto = Crypto(app.config["ENCRYPTION_KEY"])
        encrypted, nonce = crypto.encrypt(json.dumps(jsonrpc_body).encode("utf-8"))

        with patch('app.services.wallet_service.WalletService.sign_message') as mock_sign_message_method:
            mock_sign_message_method.return_value='mock_sign', None
            # 发送加密请求
            response = client.post(
                "/api/remote/jsonrpc",
                headers={"Authorization": test_device.token},
                json={"encrypted": encrypted, "nonce": nonce}
            )
            assert response.status_code == 200
            data = json.loads(response.data)
            assert "encrypted" in data
            assert "nonce" in data

            # 解密响应体
            decrypted = crypto.decrypt(data["encrypted"], data["nonce"])
            resp_json = json.loads(decrypted)
            assert resp_json["jsonrpc"] == "2.0"
            assert resp_json["id"] == 123
            assert resp_json["method"] == 'sign_message'
            assert resp_json["result"]["signed_message"] == 'mock_sign'

def test_services_config_api_with_created_dp(app, client, test_device, project, project_files):
    """测试服务配置 API - 有配置"""
    with app.app_context():
        # 确保所有对象都在当前会话中
        test_device = db.session.merge(test_device)
        project = db.session.merge(project)
        db.session.flush()

        # 创建设备项目关联
        device_project = DeviceProject(
            device_id=test_device.id,
            project_id=project.id,
            state="created",

        )
        db.session.add(device_project)
        db.session.commit()

        # 获取服务配置
        response = client.get(
            "/api/remote/configs/service",
            headers={"Authorization": test_device.token}
        )
        assert response.status_code == 404
        data = json.loads(response.data)
        assert "设备未分配服务配置" == data['error']

def test_services_config_api_with_files_data_validation(app, client, test_device, project, project_files):
    """测试服务配置 API - 有配置"""
    with app.app_context():
        # 确保所有对象都在当前会话中
        test_device = db.session.merge(test_device)
        project = db.session.merge(project)
        project.form_schema={
                "port": {"label": "端口", "type": "number"},
                "required": ["port"],
            }
        project_files = [db.session.merge(pf) for pf in project_files]

        test_project = Project(
            name="test-project-2",
            description="Test Project",
            service_config_id=project.service_config_id,
            status="enabled",
            files=project_files
        )
        db.session.add(test_project)
        db.session.flush()

        # 创建设备项目关联
        device_project = DeviceProject(
            device_id=test_device.id,
            project_id=project.id,
            state="updated",
            data={"name": "test"},
        )
        db.session.add(device_project)

        device_project_2 = DeviceProject(
            device_id=test_device.id,
            project_id=test_project.id,
            state="updated",
            data={"port": "9999"}
        )
        db.session.add(device_project_2)
        db.session.commit()

        # 更新设备服务配置
        project_service = ProjectService()
        project_service.update_device_project_config(project.id, device_project.id)
        project_service.update_device_project_config(project.id, device_project_2.id)
        db.session.commit()

        # 获取服务配置
        response = client.get(
            "/api/remote/configs/service",
            headers={"Authorization": test_device.token}
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert "encrypted" in data
        assert "nonce" in data
        crypto = Crypto(app.config["ENCRYPTION_KEY"])
        decrypted = crypto.decrypt(data["encrypted"], data["nonce"])
        resp_json = json.loads(decrypted)
        assert len(resp_json['projects'])==1
        assert resp_json['configs']['test-project-2/test/proxy.txt']=='port: 9999'

