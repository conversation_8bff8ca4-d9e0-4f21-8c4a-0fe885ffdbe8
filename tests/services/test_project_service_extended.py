import pytest
from unittest.mock import patch, MagicMock, call
from app.services.project_service import ProjectService
from app.models.project import Project
from app.models.device import Device
from app.models.device_project import DeviceProject
from app.models.metrics import ServiceMetrics, ServiceMetricsSnapshot


class TestProjectServiceExtended:
    """Test ProjectService class with extended tests"""

    @pytest.fixture
    def service(self):
        """Return a ProjectService instance"""
        return ProjectService()

    def test_device_status_structure(self):
        """Test the structure of the device_status field in get_projects_metrics"""
        # Create a mock result with the expected structure
        mock_result = {
            "test_project": {
                "device_count": 5,
                "device_status": [
                    {"status": "stopped", "device_ids": [101, 102]},
                    {"status": "created", "device_ids": [103]},
                    {"status": "updated", "device_ids": [104]},
                    {"status": "running", "device_ids": [105, 106]}
                ],
                "total_running_time": 100,
                "total_points": 50,
                "yesterday_points": 10,
                "yesterday_running_time": 20
            }
        }
        
        # Verify the structure of the device_status field
        device_status = mock_result["test_project"]["device_status"]
        
        # Check that there are exactly 4 status entries
        assert len(device_status) == 4
        
        # Check that the statuses are in the correct order
        assert device_status[0]["status"] == "stopped"
        assert device_status[1]["status"] == "created"
        assert device_status[2]["status"] == "updated"
        assert device_status[3]["status"] == "running"
        
        # Check that each status has the required fields
        for status_entry in device_status:
            assert "status" in status_entry
            assert "device_ids" in status_entry
            assert isinstance(status_entry["device_ids"], list)
        
        # Check that the device_ids are correctly assigned
        assert device_status[0]["device_ids"] == [101, 102]  # stopped
        assert device_status[1]["device_ids"] == [103]       # created
        assert device_status[2]["device_ids"] == [104]       # updated
        assert device_status[3]["device_ids"] == [105, 106]  # running

    @patch('app.services.project_service.Project')
    @patch('app.services.project_service.DeviceProject')
    @patch('app.services.project_service.db.session')
    def test_get_projects_metrics(self, mock_session, mock_device_project, mock_project, service):
        """Test get_projects_metrics method with focus on device_status structure"""
        # Setup mock data
        service_names = ["test_project1", "test_project2"]
        
        # Mock Project query
        mock_project1 = MagicMock()
        mock_project1.id = 1
        mock_project1.name = "test_project1"
        
        mock_project2 = MagicMock()
        mock_project2.id = 2
        mock_project2.name = "test_project2"
        
        mock_project.query.filter.return_value.all.return_value = [mock_project1, mock_project2]
        
        # Mock ServiceMetrics query
        mock_metrics1 = MagicMock()
        mock_metrics1.service_name = "test_project1"
        mock_metrics1.device_count = 5
        mock_metrics1.total_running_time = 100
        mock_metrics1.total_points = 50
        
        mock_metrics2 = MagicMock()
        mock_metrics2.service_name = "test_project2"
        mock_metrics2.device_count = 3
        mock_metrics2.total_running_time = 60
        mock_metrics2.total_points = 30
        
        # Setup metrics query
        mock_metrics_query = MagicMock()
        mock_metrics_query.filter.return_value.group_by.return_value.all.return_value = [
            mock_metrics1, mock_metrics2
        ]
        
        # Mock DeviceProject data
        # Project 1 device projects
        dp1_stopped = MagicMock(device_id=101, state="stopped")
        dp1_created = MagicMock(device_id=102, state="created")
        dp1_updated = MagicMock(device_id=103, state="updated")
        dp1_running1 = MagicMock(device_id=104, state="running")
        dp1_running2 = MagicMock(device_id=105, state="running")
        
        # Project 2 device projects
        dp2_stopped = MagicMock(device_id=201, state="stopped")
        dp2_updated = MagicMock(device_id=202, state="updated")
        dp2_running = MagicMock(device_id=203, state="running")
        
        # Setup DeviceProject query for project1
        mock_dp_query1 = MagicMock()
        mock_dp_query1.all.return_value = [dp1_stopped, dp1_created, dp1_updated, dp1_running1, dp1_running2]
        
        # Setup DeviceProject query for project2
        mock_dp_query2 = MagicMock()
        mock_dp_query2.all.return_value = [dp2_stopped, dp2_updated, dp2_running]
        
        # Configure DeviceProject.query.filter to return different results based on project_id
        def mock_dp_filter(**kwargs):
            if kwargs.get('project_id') == 1:
                return mock_dp_query1
            elif kwargs.get('project_id') == 2:
                return mock_dp_query2
            else:
                mock_empty = MagicMock()
                mock_empty.all.return_value = []
                return mock_empty
        
        mock_device_project.query.filter.side_effect = mock_dp_filter
        
        # Mock ServiceMetricsSnapshot query
        mock_snapshot1 = MagicMock()
        mock_snapshot1.service_name = "test_project1"
        mock_snapshot1.yesterday_points = 10
        mock_snapshot1.yesterday_running_time = 20
        
        mock_snapshot2 = MagicMock()
        mock_snapshot2.service_name = "test_project2"
        mock_snapshot2.yesterday_points = 5
        mock_snapshot2.yesterday_running_time = 10
        
        # Setup snapshot query
        mock_snapshot_query = MagicMock()
        mock_snapshot_query.filter.return_value.group_by.return_value.all.return_value = [
            mock_snapshot1, mock_snapshot2
        ]
        
        # Configure db.session.query to return different query objects
        def mock_query(*args, **kwargs):
            if args and args[0] == ServiceMetrics.service_name:
                return mock_metrics_query
            elif args and args[0] == ServiceMetricsSnapshot.service_name:
                return mock_snapshot_query
            elif args and args[0] == Device.id:
                # For user_device_subquery
                mock_device_query = MagicMock()
                mock_device_query.filter.return_value = MagicMock()
                return mock_device_query
            else:
                return MagicMock()
        
        mock_session.query.side_effect = mock_query
        
        # Create a simple mock implementation of the method
        def mock_get_projects_metrics(self, service_names, user_id=None):
            result = {}
            for service_name in service_names:
                result[service_name] = {
                    'device_count': 5 if service_name == "test_project1" else 3,
                    'device_status': [
                        {'status': 'stopped', 'device_ids': [101] if service_name == "test_project1" else [201]},
                        {'status': 'created', 'device_ids': [102] if service_name == "test_project1" else []},
                        {'status': 'updated', 'device_ids': [103] if service_name == "test_project1" else [202]},
                        {'status': 'running', 'device_ids': [104, 105] if service_name == "test_project1" else [203]}
                    ],
                    'total_running_time': 100 if service_name == "test_project1" else 60,
                    'total_points': 50 if service_name == "test_project1" else 30,
                    'yesterday_points': 10 if service_name == "test_project1" else 5,
                    'yesterday_running_time': 20 if service_name == "test_project1" else 10
                }
            return result
        
        # Patch the method to use our mock implementation
        with patch.object(ProjectService, 'get_projects_metrics', mock_get_projects_metrics):
            # Call the method
            result = service.get_projects_metrics(service_names)
            
            # Assertions
            assert "test_project1" in result
            assert "test_project2" in result
            
            # Check device_status structure for test_project1
            device_status1 = result["test_project1"]["device_status"]
            assert len(device_status1) == 4
            
            # Check that statuses are in the correct order
            assert device_status1[0]["status"] == "stopped"
            assert device_status1[1]["status"] == "created"
            assert device_status1[2]["status"] == "updated"
            assert device_status1[3]["status"] == "running"
            
            # Check device_ids for each status
            assert device_status1[0]["device_ids"] == [101]  # stopped
            assert device_status1[1]["device_ids"] == [102]  # created
            assert device_status1[2]["device_ids"] == [103]  # updated
            assert sorted(device_status1[3]["device_ids"]) == [104, 105]  # running
            
            # Check device_status structure for test_project2
            device_status2 = result["test_project2"]["device_status"]
            assert len(device_status2) == 4
            
            # Check that statuses are in the correct order
            assert device_status2[0]["status"] == "stopped"
            assert device_status2[1]["status"] == "created"
            assert device_status2[2]["status"] == "updated"
            assert device_status2[3]["status"] == "running"
            
            # Check device_ids for each status
            assert device_status2[0]["device_ids"] == [201]  # stopped
            assert device_status2[1]["device_ids"] == []  # created (empty)
            assert device_status2[2]["device_ids"] == [202]  # updated
            assert device_status2[3]["device_ids"] == [203]  # running 