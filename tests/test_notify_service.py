import pytest

from app.enums.biz_enums import NotifyType, NotifyStatus, NotifyPriority
from app.models import User
from app.models.base import db
from app.models.notify import Notify, UserNotify
from app.services.notify_service import NotifyService


def test_create_notification(app):
    """测试创建通知"""
    with app.app_context():
        # 创建测试用户
        from app.models.user import User
        user = User(username="testuser", email="<EMAIL>")
        db.session.add(user)
        db.session.commit()

        # 创建通知
        notification = NotifyService.create_notification(
            creator=user.id,
            notify_type=NotifyType.ANNOUNCEMENT,
            title={"zh_CN":"Test Notification"},
            content={"zh_CN":"This is a test notification"},
            notify_priority=NotifyPriority.NORMAL,
            user_ids=[user.id]
        )

        # 验证通知数据
        assert notification['notify_type'] == NotifyType.ANNOUNCEMENT
        assert notification['title']['zh_CN'] == "Test Notification"
        assert notification['notify_status'] == NotifyStatus.DRAFT


def test_get_user_notifications(app):
    """测试获取用户通知列表"""
    with app.app_context():
        # 创建测试数据
        user_id = 1
        for i in range(5):
            notify = Notify(
                notify_type=NotifyType.ANNOUNCEMENT,
                title={"zh_CN": f"Test {i}"},
                content={"zh_CN": f"Content {i}"},
                notify_status=NotifyStatus.PUBLISHED
            )
            db.session.add(notify)
            db.session.flush()

            user_notify = UserNotify(
                user_id=user_id,
                notify_id=notify.id,
                is_read=False
            )
            db.session.add(user_notify)

        db.session.commit()

        # 测试基本查询
        notifications, total = NotifyService.get_user_notifications('zh_CN', user_id)
        assert len(notifications) == 5
        assert total == 5

        # 测试分页
        notifications, total = NotifyService.get_user_notifications('zh_CN',
            user_id, page=1, per_page=2
        )
        assert len(notifications) == 2
        assert total == 5

        # 测试类型过滤
        notifications, total = NotifyService.get_user_notifications('zh_CN',
            user_id, notify_type_list=[NotifyType.ANNOUNCEMENT]
        )
        assert len(notifications) == 5

        # 测试未读过滤
        a = NotifyService.get_and_read_user_notification('zh_CN', notifications[0]['id'], user_id)
        notifications, total = NotifyService.get_user_notifications('zh_CN',
            user_id, unread_only=True
        )
        assert len(notifications) == 4


def test_mark_as_read(app):
    """测试标记通知为已读"""
    with app.app_context():
        # 创建测试数据
        notify = Notify(
            notify_type=NotifyType.ANNOUNCEMENT,
            title={"zh_CN":"Test"},
            content={"zh_CN":"Content"},
            notify_status=NotifyStatus.PUBLISHED
        )
        db.session.add(notify)
        db.session.flush()

        user_notify = UserNotify(
            user_id=1,
            notify_id=notify.id,
            is_read=False
        )
        db.session.add(user_notify)
        db.session.commit()

        # 测试标记已读
        result = NotifyService.get_and_read_user_notification('zh_CN', notify.id, 1)
        assert result['is_read'] == True
        assert result['read_timestamp'] is not None

        # 测试不存在的通知
        with pytest.raises(ValueError):
            NotifyService.get_and_read_user_notification('zh_CN',999, 1)


def test_delete_notification(app):
    """测试删除通知"""
    with app.app_context():
        normal_user = User(username="test_user", email="<EMAIL>", role="user")
        db.session.add(normal_user)
        admin_user = User(username="admin", email="<EMAIL>", role="admin")
        db.session.add(admin_user)
        db.session.commit()

        # 创建测试数据
        notify = Notify(
            notify_type=NotifyType.ANNOUNCEMENT,
            title={"zh":"Test"},
            content={"zh":"Content"},
            notify_status=NotifyStatus.PUBLISHED
        )
        db.session.add(notify)

        user_notify = UserNotify(
            user_id=normal_user.id,
            notify_id=notify.id,
            is_read=False
        )
        db.session.add(user_notify)
        db.session.commit()

        # 测试普通用户删除
        NotifyService.delete_user_notify(user_notify.id, normal_user.id)
        deleted_notify = db.session.get(UserNotify, user_notify.id)
        assert deleted_notify is None

        user_notify_2 = UserNotify(
            user_id=normal_user.id,
            notify_id=notify.id,
            is_read=False
        )
        db.session.commit()

        # 测试管理员删除
        result = NotifyService.delete_notification(notify.id, admin_user.id)
        assert result['is_deleted'] == True
