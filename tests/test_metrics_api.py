"""指标上报 API 测试模块"""
import json
import datetime
from datetime import timezone

import pytest

from app.models.base import db
from app.models.device import Device
from app.models.metrics import ServiceMetricsDetail
from app.models.device_runtime import DeviceRuntime
from app.models.project import Project
from app.utils.crypto import encrypt_data, decrypt_data


@pytest.fixture
def test_device(app):
    """创建测试设备"""
    with app.app_context():
        # 清理可能存在的设备
        Device.query.filter_by(name="test-device").delete()
        db.session.commit()

        # 创建新设备
        device = Device(
            name="test-device",
            description="Test Device",
            status="active",
            ip_address="***********",
            mac_address="00:11:22:33:44:55"
        )
        db.session.add(device)
        db.session.commit()

        device_data = {
            "id": device.id,
            "token": device.token
        }

        yield device_data

        # 清理设备
        Device.query.filter_by(name="test-device").delete()
        db.session.commit()

@pytest.fixture
def test_project(app):
    # 创建测试项目
    project = Project(
        name='test_service',
        description="Test Project",
        service_config_id='123',
        service_compose='',
        status=Project.STATUS_ENABLED
    )
    db.session.add(project)
    db.session.commit()

    yield project

    # 清理数据
    Device.query.filter_by(name="test-device").delete()
    db.session.commit()



def test_metrics_upload_services_only(client, test_device, test_project, app):
    """测试仅上报服务指标数据"""
    with app.app_context():
        # 准备测试数据
        current_time = datetime.datetime.now(timezone.utc)
        time_str = current_time.strftime("%Y-%m-%dT%H:%M:%SZ")
        services_data = {
            "services": [
                {
                    "service_name": "test_service",
                    "started_at": time_str,
                    "total_points": 100,
                    "running_time": 3600,
                    "updated_at": time_str
                }
            ]
        }

        # 加密数据
        encrypted_data, nonce = encrypt_data(json.dumps(services_data))

        # 发送请求
        response = client.post(
            "/api/remote/metrics",
            headers={
                "Authorization": test_device["token"],
                "Content-Type": "application/json"
            },
            json={
                "encrypted": encrypted_data,
                "nonce": nonce
            }
        )

        # 验证响应
        assert response.status_code == 200
        response_data = json.loads(response.data)
        decrypted_data = json.loads(decrypt_data(response_data["encrypted"], response_data["nonce"]))
        assert "ok" in decrypted_data["message"].lower()

        # 验证服务指标数据
        started_at = datetime.datetime.strptime(time_str, '%Y-%m-%dT%H:%M:%SZ').replace(tzinfo=timezone.utc)
        metrics = ServiceMetricsDetail.query.filter_by(
            device_id=test_device["id"],
            service_name="test_service",
            started_at=started_at.replace(tzinfo=None)
        ).first()
        assert metrics is not None
        assert metrics.points == 100
        assert metrics.running_time == 3600
        assert metrics.started_at.replace(tzinfo=None) == started_at.replace(tzinfo=None)

        # 验证没有创建设备运行时数据
        runtime = DeviceRuntime.query.filter_by(device_id=test_device["id"]).first()
        assert runtime is None


def test_metrics_upload_device_only(client, test_device, test_project, app):
    """测试仅上报设备运行时数据"""
    with app.app_context():
        # 准备测试数据
        current_time = datetime.datetime.now(timezone.utc)
        time_str = current_time.strftime("%Y-%m-%dT%H:%M:%SZ")
        device_data = {
            "device": {
                "started_at": time_str,
                "running_time": 3600,
                "lan_ip": "***********00",
                "public_ip": "***********",
                "network": {
                    "download_speed": 100000000,
                    "upload_speed": 50000000
                },
                "disk": {
                    "total": 1000000000000,
                    "used": 500000000000,
                    "free": 500000000000
                },
                "cpu": {
                    "usage": 50.0,
                    "cores": 8
                },
                "memory": {
                    "total": 16000000000,
                    "used": 8000000000,
                    "free": 8000000000
                },
                "updated_at": time_str
            }
        }

        # 加密数据
        encrypted_data, nonce = encrypt_data(json.dumps(device_data))

        # 发送请求
        response = client.post(
            "/api/remote/metrics",
            headers={
                "Authorization": test_device["token"],
                "Content-Type": "application/json"
            },
            json={
                "encrypted": encrypted_data,
                "nonce": nonce
            }
        )

        # 验证响应
        assert response.status_code == 200
        response_data = json.loads(response.data)
        decrypted_data = json.loads(decrypt_data(response_data["encrypted"], response_data["nonce"]))
        assert "ok" in decrypted_data["message"].lower()

        # 验证没有服务指标数据
        metrics = ServiceMetricsDetail.query.filter_by(
            device_id=test_device["id"]
        ).first()
        assert metrics is None

        # 验证设备运行时数据
        runtime = DeviceRuntime.query.filter_by(device_id=test_device["id"]).first()
        assert runtime is not None
        assert runtime.running_time == 3600
        assert runtime.lan_ip == "***********00"
        assert runtime.public_ip == "***********"
        assert runtime.network == {
            "download_speed": 100000000,
            "upload_speed": 50000000
        }
        assert runtime.disk == {
            "total": 1000000000000,
            "used": 500000000000,
            "free": 500000000000
        }
        assert runtime.cpu == {
            "usage": 50.0,
            "cores": 8
        }
        assert runtime.memory == {
            "total": 16000000000,
            "used": 8000000000,
            "free": 8000000000
        }


def test_metrics_upload_both(client, test_device, test_project, app):
    """测试同时上报服务指标和设备运行时数据"""
    with app.app_context():
        # 准备测试数据
        current_time = datetime.datetime.now(timezone.utc)
        time_str = current_time.strftime("%Y-%m-%dT%H:%M:%SZ")
        services_data = {
            "services": [
                {
                    "service_name": "test_service",
                    "started_at": time_str,
                    "total_points": 100,
                    "running_time": 3600,
                    "updated_at": time_str
                }
            ],
            "device": {
                "started_at": time_str,
                "running_time": 3600,
                "lan_ip": "***********00",
                "public_ip": "***********",
                "network": {
                    "download_speed": 100000000,
                    "upload_speed": 50000000
                },
                "disk": {
                    "total": 1000000000000,
                    "used": 500000000000,
                    "free": 500000000000
                },
                "cpu": {
                    "usage": 50.0,
                    "cores": 8
                },
                "memory": {
                    "total": 16000000000,
                    "used": 8000000000,
                    "free": 8000000000
                },
                "updated_at": time_str
            }
        }

        # 加密数据
        encrypted_data, nonce = encrypt_data(json.dumps(services_data))

        # 发送请求
        response = client.post(
            "/api/remote/metrics",
            headers={
                "Authorization": test_device["token"],
                "Content-Type": "application/json"
            },
            json={
                "encrypted": encrypted_data,
                "nonce": nonce
            }
        )

        # 验证响应
        assert response.status_code == 200
        response_data = json.loads(response.data)
        decrypted_data = json.loads(decrypt_data(response_data["encrypted"], response_data["nonce"]))
        assert "ok" in decrypted_data["message"].lower()

        # 验证服务指标数据
        started_at = datetime.datetime.strptime(time_str, '%Y-%m-%dT%H:%M:%SZ').replace(tzinfo=timezone.utc)
        metrics = ServiceMetricsDetail.query.filter_by(
            device_id=test_device["id"],
            service_name="test_service",
            started_at=started_at.replace(tzinfo=None)
        ).first()
        assert metrics is not None
        assert metrics.points == 100
        assert metrics.running_time == 3600
        assert metrics.started_at.replace(tzinfo=None) == started_at.replace(tzinfo=None)

        # 验证设备运行时数据
        runtime = DeviceRuntime.query.filter_by(device_id=test_device["id"]).first()
        assert runtime is not None
        assert runtime.running_time == 3600
        assert runtime.lan_ip == "***********00"
        assert runtime.public_ip == "***********"
        assert runtime.network == {
            "download_speed": 100000000,
            "upload_speed": 50000000
        }
        assert runtime.disk == {
            "total": 1000000000000,
            "used": 500000000000,
            "free": 500000000000
        }
        assert runtime.cpu == {
            "usage": 50.0,
            "cores": 8
        }
        assert runtime.memory == {
            "total": 16000000000,
            "used": 8000000000,
            "free": 8000000000
        }


def test_metrics_upload_invalid_token(client):
    """测试使用无效token上报指标"""
    # 准备测试数据
    current_time = datetime.datetime.now(timezone.utc)
    time_str = current_time.strftime("%Y-%m-%dT%H:%M:%SZ")
    services_data = {
        "services": [
            {
                "service_name": "test_service",
                "started_at": time_str,
                "total_points": 100,
                "running_time": 3600,
                "updated_at": time_str
            }
        ]
    }

    # 加密数据
    encrypted_data, nonce = encrypt_data(json.dumps(services_data))

    # 发送请求
    response = client.post(
        "/api/remote/metrics",
        headers={
            "Authorization": "invalid_token",
            "Content-Type": "application/json"
        },
        json={
            "encrypted": encrypted_data,
            "nonce": nonce
        }
    )

    assert response.status_code == 401


def test_metrics_upload_invalid_data(client, test_device, test_project):
    """测试上报无效的指标数据"""
    # 缺少必需字段的数据
    current_time = datetime.datetime.now(timezone.utc)
    time_str = current_time.strftime("%Y-%m-%dT%H:%M:%SZ")
    services_data = {
        "services": [
            {
                "total_points": 100,  # 缺少 service_name
                "running_time": 3600,
                "updated_at": time_str,
                "started_at": time_str
            }
        ]
    }

    # 加密数据
    encrypted_data, nonce = encrypt_data(json.dumps(services_data))

    response = client.post(
        "/api/remote/metrics",
        headers={
            "Authorization": test_device["token"],
            "Content-Type": "application/json"
        },
        json={
            "encrypted": encrypted_data,
            "nonce": nonce
        }
    )

    assert response.status_code == 400


def test_metrics_upload_update_existing(client, test_device, test_project, app):
    """测试更新已存在的指标数据"""
    with app.app_context():
        # 首先创建一个初始记录
        initial_time = datetime.datetime(2024, 1, 1, tzinfo=timezone.utc)
        time_str = initial_time.strftime("%Y-%m-%dT%H:%M:%SZ")
        services_data = {
            "services": [
                {
                    "service_name": "test_service",
                    "started_at": time_str,
                    "total_points": 100,
                    "running_time": 3600,
                    "updated_at": time_str
                }
            ],
            "device": {
                "started_at": time_str,
                "running_time": 3600,
                "lan_ip": "***********00",
                "public_ip": "*******",
                "updated_at": time_str
            }
        }

        encrypted_data, nonce = encrypt_data(json.dumps(services_data))

        client.post(
            "/api/remote/metrics",
            headers={
                "Authorization": test_device["token"],
                "Content-Type": "application/json"
            },
            json={
                "encrypted": encrypted_data,
                "nonce": nonce
            }
        )

        # 验证初始记录已创建
        initial_started_at = datetime.datetime.strptime(time_str, '%Y-%m-%dT%H:%M:%SZ').replace(tzinfo=timezone.utc)
        metrics = ServiceMetricsDetail.query.filter_by(
            device_id=test_device["id"],
            service_name="test_service",
            started_at=initial_started_at.replace(tzinfo=None)
        ).first()
        assert metrics is not None
        assert metrics.points == 100
        assert metrics.running_time == 3600
        assert metrics.started_at.replace(tzinfo=None) == initial_started_at.replace(tzinfo=None)

        # 然后尝试只更新服务指标数据，使用新的 started_at
        updated_time = datetime.datetime(2024, 1, 2, tzinfo=timezone.utc)
        time_str = updated_time.strftime("%Y-%m-%dT%H:%M:%SZ")
        updated_data = {
            "services": [
                {
                    "service_name": "test_service",
                    "started_at": time_str,
                    "total_points": 200,
                    "running_time": 7200,
                    "updated_at": time_str
                }
            ]
        }

        encrypted_data, nonce = encrypt_data(json.dumps(updated_data))

        response = client.post(
            "/api/remote/metrics",
            headers={
                "Authorization": test_device["token"],
                "Content-Type": "application/json"
            },
            json={
                "encrypted": encrypted_data,
                "nonce": nonce
            }
        )

        assert response.status_code == 200

        # 验证新的服务指标记录已创建
        updated_started_at = datetime.datetime.strptime(time_str, '%Y-%m-%dT%H:%M:%SZ').replace(tzinfo=timezone.utc)
        metrics = ServiceMetricsDetail.query.filter_by(
            device_id=test_device["id"],
            service_name="test_service",
            started_at=updated_started_at.replace(tzinfo=None)
        ).first()
        assert metrics is not None
        assert metrics.points == 200
        assert metrics.running_time == 7200
        assert metrics.started_at.replace(tzinfo=None) == updated_started_at.replace(tzinfo=None)

        # 验证原始记录仍然存在且未改变
        metrics = ServiceMetricsDetail.query.filter_by(
            device_id=test_device["id"],
            service_name="test_service",
            started_at=initial_started_at.replace(tzinfo=None)
        ).first()
        assert metrics is not None
        assert metrics.points == 100
        assert metrics.running_time == 3600
        assert metrics.started_at.replace(tzinfo=None) == initial_started_at.replace(tzinfo=None)

        # 验证设备运行时数据保持不变
        runtime = DeviceRuntime.query.filter_by(device_id=test_device["id"]).first()
        assert runtime is not None
        assert runtime.running_time == 3600
        assert runtime.lan_ip == "***********00"
        assert runtime.public_ip == "*******"

        # 然后尝试只更新设备运行时数据
        time_str = datetime.datetime(2024, 1, 3, tzinfo=timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")
        device_update = {
            "device": {
                "started_at": time_str,
                "running_time": 10800,
                "lan_ip": "***********01",
                "public_ip": "*******",
                "updated_at": time_str
            }
        }

        encrypted_data, nonce = encrypt_data(json.dumps(device_update))

        response = client.post(
            "/api/remote/metrics",
            headers={
                "Authorization": test_device["token"],
                "Content-Type": "application/json"
            },
            json={
                "encrypted": encrypted_data,
                "nonce": nonce
            }
        )

        assert response.status_code == 200

        # 验证所有服务指标记录保持不变
        metrics = ServiceMetricsDetail.query.filter_by(
            device_id=test_device["id"],
            service_name="test_service",
            started_at=initial_started_at.replace(tzinfo=None)
        ).first()
        assert metrics is not None
        assert metrics.points == 100
        assert metrics.running_time == 3600
        assert metrics.started_at.replace(tzinfo=None) == initial_started_at.replace(tzinfo=None)

        metrics = ServiceMetricsDetail.query.filter_by(
            device_id=test_device["id"],
            service_name="test_service",
            started_at=updated_started_at.replace(tzinfo=None)
        ).first()
        assert metrics is not None
        assert metrics.points == 200
        assert metrics.running_time == 7200
        assert metrics.started_at.replace(tzinfo=None) == updated_started_at.replace(tzinfo=None)

        # 验证设备运行时数据已更新
        runtime = DeviceRuntime.query.filter_by(device_id=test_device["id"]).first()
        assert runtime is not None
        assert runtime.running_time == 10800
        assert runtime.lan_ip == "***********01"
        assert runtime.public_ip == "*******"
