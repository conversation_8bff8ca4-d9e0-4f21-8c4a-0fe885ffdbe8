from decimal import Decimal

import pytest
from unittest.mock import patch, MagicMock

from app.middlewares.web3.price_service import PriceService


@pytest.fixture
def mock_requests():
    with patch('requests.get') as mock:
        response = MagicMock()
        response.json.return_value = {
            'ethereum': {'usd': '2000.0'},
            'binancecoin': {'usd': '300.0'},
            'solana': {'usd': '100.0'},
            'tether': {'usd': '1.0'},
            'arbitrum': {'usd': '0.34'},
            'nodecoin': {'usd': '0.34'},
        }
        response.raise_for_status.return_value = None
        mock.return_value = response
        yield mock


@pytest.fixture
def mock_token():
    """创建模拟的 Token 对象"""
    token = MagicMock()
    token.token_code = 'eth_native'
    return token


def test_price_caching(app, mock_requests, mock_token):
    """Test price caching mechanism"""
    # First call should make a request
    PriceService.get_token_price(mock_token)
    assert mock_requests.call_count == 1

    # Second call within cache duration should use cached value
    PriceService.get_token_price(mock_token)
    assert mock_requests.call_count == 1

    # Clear cache
    PriceService._get_cached_price.cache_clear()

    # Call after clearing cache should make a new request
    PriceService.get_token_price(mock_token)
    assert mock_requests.call_count == 2


def test_get_token_price_by_code(mock_requests):
    """Test getting token prices by code"""
    # Test ETH price
    eth_price = PriceService.get_token_price_by_code('eth_native')
    assert eth_price == Decimal('2000.0')
    print(f'eth_price: {eth_price}')

    # Test BSC price
    bsc_price = PriceService.get_token_price_by_code('bnb_native')
    assert bsc_price == Decimal('300.0')
    print(f'bsc_price: {bsc_price}')

    # Test ARB price
    arb_price = PriceService.get_token_price_by_code('arb_native')
    assert arb_price == Decimal('0.34')
    print(f'arb_price: {arb_price}')

    # Test SOL price
    sol_price = PriceService.get_token_price_by_code('sol_native')
    assert sol_price == Decimal('100.0')
    print(f'sol_price: {sol_price}')


def test_get_token_price_with_token_object(mock_requests):
    """Test getting token price with Token object"""
    # Create mock token
    mock_token = MagicMock()
    mock_token.token_code = 'eth_native'

    eth_price = PriceService.get_token_price(mock_token)
    assert eth_price == Decimal('2000.0')


def test_get_all_prices(mock_requests):
    """Test getting all token prices"""
    all_prices = PriceService.get_all_prices()

    assert isinstance(all_prices, dict)
    assert 'eth_native' in all_prices
    assert 'bnb_native' in all_prices
    assert 'sol_native' in all_prices
    assert 'arb_native' in all_prices


def test_get_supported_tokens():
    """Test getting supported token list"""
    supported_tokens = PriceService.get_supported_tokens()

    assert isinstance(supported_tokens, list)
    assert 'eth_native' in supported_tokens
    assert 'bnb_native' in supported_tokens
    assert 'sol_native' in supported_tokens
    assert 'nodecoin' in supported_tokens


def test_clear_cache():
    """Test clearing price cache"""
    # This should not raise any exception
    PriceService.clear_cache()


def test_price_service_error_handling(app):
    """Test error handling in price service"""
    with patch('requests.get') as mock_get:
        # Simulate request failure
        mock_get.side_effect = Exception("Network error")

        # Should return 0.0 for all tokens on error
        price = PriceService.get_token_price_by_code('eth_native')
        assert price == Decimal('0.0')



