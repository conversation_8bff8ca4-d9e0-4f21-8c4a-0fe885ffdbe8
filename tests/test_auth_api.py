"""认证 API 测试模块"""
import json
from datetime import datetime, timedelta, timezone

import pytest
from flask_jwt_extended import create_access_token

from app.enums.biz_enums import DeviceStatus
from app.models.base import db
from app.models.device import Device
from app.models.metrics import ServiceMetricsSnapshot
from app.models.points import PointRecord
from app.models.user import User


@pytest.fixture
def client(app):
    return app.test_client()


@pytest.fixture
def admin_user(app):
    """创建管理员用户，如果不存在的话"""
    with app.app_context():
        admin = User.query.filter_by(username="admin").first()
        if not admin:
            admin = User(username="admin", email="<EMAIL>", role="admin")
            admin.set_password("admin123")
            db.session.add(admin)
            db.session.commit()
        return admin


@pytest.fixture
def admin_token(client, admin_user):
    """获取管理员的 JWT token"""
    response = client.post("/api/auth/login", json={
        "username": "admin",
        "password": "admin123"
    })
    data = json.loads(response.data)
    return data["data"]["token"]


def test_register(client):
    """测试用户注册"""
    # 清理测试数据
    with client.application.app_context():
        User.query.filter(User.username.in_(["testuser", "testuser2"])).delete()
        User.query.filter(User.email.in_(["<EMAIL>", "<EMAIL>"])).delete()
        db.session.commit()

    # 测试正常注册（在测试环境中不需要验证码）
    response = client.post("/api/auth/register", json={
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "password123"
    })
    assert response.status_code == 200
    data = json.loads(response.data)
    assert data["code"] == 200
    assert data["message"] == "注册成功"
    assert data["data"]["username"] == "testuser"
    assert data["data"]["email"] == "<EMAIL>"

    # 测试重复用户名
    response = client.post("/api/auth/register", json={
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "password123"
    })
    assert response.status_code == 400
    data = json.loads(response.data)
    assert data["code"] == 400
    assert "用户名已存在" in data["message"]

    # 测试重复邮箱
    response = client.post("/api/auth/register", json={
        "username": "testuser2",
        "email": "<EMAIL>",
        "password": "password123"
    })
    assert response.status_code == 400
    data = json.loads(response.data)
    assert data["code"] == 400
    assert "邮箱已存在" in data["message"]

    # 测试缺少必要字段
    response = client.post("/api/auth/register", json={
        "username": "testuser"
    })
    assert response.status_code == 422
    data = json.loads(response.data)
    assert data["code"] == 422
    assert "缺少必要字段" in data["message"]


def test_login(client):
    """测试用户登录"""
    # 清理测试数据
    with client.application.app_context():
        User.query.filter_by(username="testuser").delete()
        db.session.commit()

    # 创建测试用户
    with client.application.app_context():
        user = User(username="testuser", email="<EMAIL>")
        user.set_password("password123")
        db.session.add(user)
        db.session.commit()

    # 测试正常登录
    response = client.post("/api/auth/login", json={
        "username": "testuser",
        "password": "password123"
    })
    assert response.status_code == 200
    data = json.loads(response.data)
    assert data["code"] == 200
    assert data["message"] == "登录成功"
    assert "token" in data["data"]
    assert data["data"]["user"]["username"] == "testuser"

    # 测试错误密码
    response = client.post("/api/auth/login", json={
        "username": "testuser",
        "password": "wrongpassword"
    })
    assert response.status_code == 401
    data = json.loads(response.data)
    assert data["code"] == 401
    assert "密码错误" in data["message"]

    # 测试不存在的用户
    response = client.post("/api/auth/login", json={
        "username": "nonexistent",
        "password": "password123"
    })
    assert response.status_code == 401
    data = json.loads(response.data)
    assert data["code"] == 401
    assert "用户不存在" in data["message"]

    # 测试缺少必要字段
    response = client.post("/api/auth/login", json={
        "username": "testuser"
    })
    assert response.status_code == 422
    data = json.loads(response.data)
    assert data["code"] == 422
    assert "缺少必要字段" in data["message"]


def test_get_users(client, admin_token):
    """测试获取用户列表"""
    # 清理测试数据
    with client.application.app_context():
        User.query.filter(User.username.startswith("testuser")).delete()
        db.session.commit()

    # 创建一些测试用户
    with client.application.app_context():
        for i in range(3):
            user = User(
                username=f"testuser{i}",
                email=f"test{i}@example.com",
                role="user"
            )
            user.set_password("password123")
            db.session.add(user)
            db.session.commit()

    # 测试管理员获取用户列表
    response = client.get("/api/auth/users",
                         headers={"Authorization": f"Bearer {admin_token}"})
    assert response.status_code == 200
    data = json.loads(response.data)
    assert data["code"] == 200
    assert "items" in data["data"]
    assert "total" in data["data"]
    assert "page" in data["data"]
    assert "per_page" in data["data"]
    assert len(data["data"]["items"]) == 4  # 3个测试用户 + 1个管理员

    # 测试非管理员获取用户列表
    with client.application.app_context():
        user = User.query.filter_by(username="testuser0").first()
        token = create_access_token(identity=str(user.id))

    response = client.get("/api/auth/users",
                         headers={"Authorization": f"Bearer {token}"})
    assert response.status_code == 403
    data = json.loads(response.data)
    assert data["code"] == 403
    assert "权限不足" in data["message"]


def test_update_user(client, admin_token):
    """测试更新用户信息"""
    # 清理测试数据
    with client.application.app_context():
        User.query.filter(User.username.in_(["testuser", "updated_user"])).delete()
        User.query.filter(User.email.in_(["<EMAIL>", "<EMAIL>"])).delete()
        db.session.commit()

    # 创建测试用户
    with client.application.app_context():
        user = User(username="testuser", email="<EMAIL>", role="user")
        user.set_password("password123")
        db.session.add(user)
        db.session.commit()
        user_id = user.id

    # 测试管理员更新用户信息
    headers = {"Authorization": f"Bearer {admin_token}"}
    update_data = {
        "username": "updated_user",
        "email": "<EMAIL>",
        "role": "admin"
    }

    response = client.put(f"/api/auth/users/{user_id}",
                         headers=headers,
                         json=update_data)
    assert response.status_code == 200
    data = json.loads(response.data)
    assert data["code"] == 200
    assert data["message"] == "用户信息更新成功"
    assert data["data"]["username"] == "updated_user"
    assert data["data"]["email"] == "<EMAIL>"
    assert data["data"]["role"] == "admin"

    # 测试非管理员更新用户信息
    with client.application.app_context():
        normal_user = User(username="normaluser", email="<EMAIL>", role="user")
        normal_user.set_password("password123")
        db.session.add(normal_user)
        db.session.commit()
        normal_token = create_access_token(identity=str(normal_user.id))

    response = client.put(f"/api/auth/users/{user_id}",
                         headers={"Authorization": f"Bearer {normal_token}"},
                         json=update_data)
    assert response.status_code == 403
    data = json.loads(response.data)
    assert data["code"] == 403
    assert "权限不足" in data["message"]

    # 测试更新不存在的用户
    response = client.put("/api/auth/users/99999",
                         headers=headers,
                         json=update_data)
    assert response.status_code == 400
    data = json.loads(response.data)
    assert data["code"] == 400
    assert "用户不存在" in data["message"]


def test_get_user_statistics(client, app):
    """测试获取用户统计汇总信息"""
    with app.app_context():
        # 创建测试用户
        user = User(
            username="test_user",
            email="<EMAIL>"
        )
        user.set_password("password")
        db.session.add(user)
        db.session.commit()

        # 创建测试设备
        device1 = Device(
            name="test-device-1",
            ip_address="***********",
            mac_address="00:11:22:33:44:55",
            owner_id=user.id,
            status=DeviceStatus.RUNNING.code  # online
        )
        db.session.add(device1)
        db.session.commit()

        # 创建积分记录
        current_time = datetime.now(timezone.utc)
        point_record = PointRecord(
            user_id=user.id,
            points=100,
            record_type="project",
            created_at=current_time
        )
        db.session.add(point_record)
        db.session.commit()

        # 创建访问令牌
        token = create_access_token(identity=str(user.id))

        try:
            # 测试获取统计信息
            response = client.get(
                "/api/auth/statistics",
                headers={"Authorization": f"Bearer {token}"}
            )
            assert response.status_code == 200
            data = json.loads(response.data)
            assert data["code"] == 200
            assert data["data"]["total_devices"] == 1
            assert data["data"]["online_devices"] == 1
            assert data["data"]["total_running_time"] == 0
            assert data["data"]["total_points"] == 100

        finally:
            # 清理测试数据
            PointRecord.query.filter_by(user_id=user.id).delete()
            Device.query.filter_by(id=device1.id).delete()
            User.query.filter_by(id=user.id).delete()
            db.session.commit()


def test_get_daily_statistics(client, app):
    """测试获取用户每日统计数据"""
    with app.app_context():
        # 创建测试用户
        user = User(username="testuser", email="<EMAIL>")
        user.set_password("password123")
        db.session.add(user)
        db.session.commit()

        # 创建测试设备
        device = Device(
            name="device1",
            ip_address="***********",
            mac_address="00:11:22:33:44:55",
            owner_id=user.id
        )
        db.session.add(device)
        db.session.commit()

        # 创建测试积分记录
        base_date = datetime.now(timezone.utc)
        for days_ago in range(3):  # 创建3天的数据
            day = base_date - timedelta(days=days_ago)
            day_str = day.strftime("%Y-%m-%d")

            # 每天创建一个积分记录
            point_record = PointRecord(
                user_id=user.id,
                points=100,
                record_type="project",
                created_at=day.replace(hour=12, minute=0, second=0, microsecond=0, tzinfo=timezone.utc)  # 使用当天中午12点，UTC时区
            )
            db.session.add(point_record)

            # 每天创建一个运行时间快照记录
            snapshot = ServiceMetricsSnapshot(
                device_id=device.id,
                service_name="test_service",
                day=day_str,
                running_time=3600,  # 1小时
                points=0,  # 设置为0，因为积分通过 PointRecord 记录
                yesterday_points=100,  # 添加昨日积分
                yesterday_running_time=3600  # 添加昨日运行时间
            )
            db.session.add(snapshot)

        db.session.commit()

        # 创建访问令牌
        token = create_access_token(identity=str(user.id))

        try:
            # 测试获取每日统计数据
            start_date = (base_date - timedelta(days=10)).strftime("%Y-%m-%d")
            end_date = base_date.strftime("%Y-%m-%d")
            response = client.get(
                f"/api/auth/statistics/daily?start_date={start_date}&end_date={end_date}",
                headers={"Authorization": f"Bearer {token}"}
            )

            assert response.status_code == 200
            data = json.loads(response.data)
            assert data["code"] == 200

            # 验证返回的数据结构
            result_data = data["data"]
            assert len(result_data) > 0  # 至少应该有一天的数据

            # 验证最近一天的数据格式和内容
            today_str = base_date.strftime("%Y-%m-%d")
            if today_str in result_data:
                today_data = result_data[today_str]
                assert isinstance(today_data, list)
                # 验证服务记录
                service_records = [r for r in today_data if r["service_name"] == "test_service"]
                if service_records:
                    assert service_records[0]["running_time"] == 3600

                # 验证积分记录
                points_records = [r for r in today_data if r["service_name"] == "all"]
                if points_records:
                    assert points_records[0]["points"] == 100.0

        finally:
            # 清理测试数据
            ServiceMetricsSnapshot.query.filter_by(device_id=device.id).delete()
            PointRecord.query.filter_by(user_id=user.id).delete()
            Device.query.filter_by(id=device.id).delete()
            User.query.filter_by(id=user.id).delete()
            db.session.commit()


# 辅助函数：解析 HTTP 格式的日期字符串
def parse_http_date(date_str):
    fmt = "%a, %d %b %Y %H:%M:%S GMT"
    return datetime.strptime(date_str, fmt).replace(tzinfo=timezone.utc)

def test_invite_history(client):
    """
    测试邀请历史接口：
    - 正常返回（无 status 参数时返回所有记录）
    - 根据 status 筛选：pending（points==0）和 success（points>0）
    - 缺少必要字段时返回错误
    """
    with client.application.app_context():
        # 清理测试数据，避免重复数据干扰
        User.query.filter(User.username.in_(["inviter_test", "invitee_pending", "invitee_success"])).delete()
        db.session.commit()

        # 创建邀请人
        inviter = User(username="inviter_test", email="<EMAIL>")
        inviter.set_password("password123")
        db.session.add(inviter)
        db.session.commit()

        # 创建待邀请用户（pending 记录，points=0）
        invitee_pending = User(username="invitee_pending", email="<EMAIL>")
        invitee_pending.set_password("password123")
        db.session.add(invitee_pending)
        db.session.commit()

        # 创建待邀请用户（success 记录，points>0）
        invitee_success = User(username="invitee_success", email="<EMAIL>")
        invitee_success.set_password("password123")
        db.session.add(invitee_success)
        db.session.commit()

        # 为 invitee_pending 添加一个设备，填充所有必填字段
        device_pending = Device(
            owner=invitee_pending,
            name="pending_device",
            ip_address="***********0",
            mac_address="00:00:00:00:00:10",
            created_at=datetime(2021, 3, 1, tzinfo=timezone.utc)
        )
        db.session.add(device_pending)

        # 为 invitee_success 添加两个设备，测试 earliest_created_at 取最早日期
        device1 = Device(
            owner=invitee_success,
            name="success_device_1",
            ip_address="***********1",
            mac_address="00:00:00:00:00:11",
            created_at=datetime(2021, 1, 1, tzinfo=timezone.utc)
        )
        device2 = Device(
            owner=invitee_success,
            name="success_device_2",
            ip_address="***********2",
            mac_address="00:00:00:00:00:12",
            created_at=datetime(2021, 6, 1, tzinfo=timezone.utc)
        )
        db.session.add_all([device1, device2])
        db.session.commit()

        # 创建邀请记录：
        # pending 记录：points == 0，邀请时间为2022-01-01
        record_pending = PointRecord(
            user_id=inviter.id,
            record_type="invite",
            invitee_id=invitee_pending.id,
            points=0,
            created_at=datetime(2022, 1, 1, tzinfo=timezone.utc)
        )
        # success 记录：points > 0，邀请时间为2022-02-01
        record_success = PointRecord(
            user_id=inviter.id,
            record_type="invite",
            invitee_id=invitee_success.id,
            points=10,
            created_at=datetime(2022, 2, 1, tzinfo=timezone.utc)
        )
        db.session.add_all([record_pending, record_success])
        db.session.commit()

        # 为测试邀请历史接口生成 token
        token = create_access_token(identity=str(inviter.id))

        # 1. 测试不传 status 参数时返回全部记录（共2条）
        response = client.post(
            "/api/auth/invite_history",
            headers={"Authorization": f"Bearer {token}"},
            json={"page": 1, "per_page": 10}
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["code"] == 200
        assert data["data"]["total"] == 2
        items = data["data"]["items"]
        assert len(items) == 2


        # 验证设备绑定数及最早创建时间
        for item in items:
            if item["account"] == "<EMAIL>":
                # invitee_success 拥有两个设备，最早日期为2021-01-01
                assert item["device_binding_count"] == 2
                binding_time = parse_http_date(item["device_binding_time"])
                assert binding_time == datetime(2021, 1, 1, tzinfo=timezone.utc)
            elif item["account"] == "<EMAIL>":
                # invitee_pending 拥有一个设备，创建时间为2021-03-01
                assert item["device_binding_count"] == 1
                binding_time = parse_http_date(item["device_binding_time"])
                assert binding_time == datetime(2021, 3, 1, tzinfo=timezone.utc)


        # 2. 测试 status=pending 时只返回 points==0 的记录
        response = client.post(
            "/api/auth/invite_history",
            headers={"Authorization": f"Bearer {token}"},
            json={"page": 1, "per_page": 10, "status": "pending"}
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["code"] == 200
        assert data["data"]["total"] == 1
        items = data["data"]["items"]
        assert len(items) == 1
        assert items[0]["account"] == "<EMAIL>"
        assert items[0]["points"] == 0

        # 3. 测试 status=success 时只返回 points>0 的记录
        response = client.post(
            "/api/auth/invite_history",
            headers={"Authorization": f"Bearer {token}"},
            json={"page": 1, "per_page": 10, "status": "success"}
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["code"] == 200
        assert data["data"]["total"] == 1
        items = data["data"]["items"]
        assert len(items) == 1
        assert items[0]["account"] == "<EMAIL>"
        assert items[0]["points"] == 10

        # 4. 测试缺少必要字段时返回错误
        response = client.post(
            "/api/auth/invite_history",
            headers={"Authorization": f"Bearer {token}"},
            json={"page": 1}
        )
        assert response.status_code != 200
        data = json.loads(response.data)
        assert data["code"] != 200
