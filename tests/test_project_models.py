"""项目模型测试模块"""

import pytest

from app.models.device import Device
from app.models.device_project import DeviceProject
from app.models.project import Project
from app.models.project_file import ProjectFile
from app.models.service_config import ServiceConfig
from app.models.user import User
from app.services.project_service import ProjectService


@pytest.fixture
def normal_user(db_session):
    """创建普通用户"""
    user = User(username="test_user", email="<EMAIL>", role="user")
    user.set_password("password123")
    db_session.add(user)
    db_session.commit()
    return user


@pytest.fixture
def service_config(db_session):
    """创建测试用的服务配置"""
    config = ServiceConfig(
        name="test-service",
        description="测试服务",
        docker_compose="version: '3'\nservices:\n  test:\n    image: test:latest",
        default_env={"TEST_ENV": "test"}
    )
    db_session.add(config)
    db_session.commit()
    return config


@pytest.fixture
def project(db_session):
    """创建测试项目"""
    # 创建服务配置
    service_config = ServiceConfig(
        name="test-service",
        description="Test Service Config",
        docker_compose={
            "version": "3",
            "services": {
                "app": {
                    "image": "test-image:latest",
                    "environment": {
                        "EMAIL": "{{ email }}",
                        "PASSWORD": "{{ password }}",
                        "PROXY_PORT": "{{ proxy }}"
                    }
                }
            }
        }
    )
    db_session.add(service_config)
    db_session.commit()

    # 创建项目
    project = Project(
        name="test-project",
        description="Test Project",
        service_config_id=service_config.id,
        service_compose={
            "services": {
                "app": {
                    "ports": ["{{ proxy }}:80"],
                    "environment": {
                        "EMAIL": "{{ email }}",
                        "PASSWORD": "{{ password }}",
                        "PROXY_PORT": "{{ proxy }}"
                    }
                }
            }
        },
        form_schema={
            "type": "object",
            "properties": {
                "email": {
                    "type": "string",
                    "title": "邮箱"
                },
                "password": {
                    "type": "string",
                    "title": "密码"
                },
                "proxy": {
                    "type": "string",
                    "title": "代理端口"
                }
            }
        }
    )
    db_session.add(project)
    db_session.commit()

    yield project

    # 清理
    db_session.delete(project)
    db_session.delete(service_config)
    db_session.commit()


@pytest.fixture
def project_files(db_session, project):
    """创建测试项目文件"""
    files = [
        ProjectFile(
            project_id=project.id,
            name="config.json",
            content='{"email": "{{ email }}", "password": "{{ password }}"}'
        ),
        ProjectFile(
            project_id=project.id,
            name="proxy.conf",
            content="proxy_port={{ proxy }}"
        )
    ]
    for file in files:
        db_session.add(file)
    db_session.commit()

    yield files

    for file in files:
        db_session.delete(file)
    db_session.commit()


@pytest.fixture
def device(db_session, normal_user):
    """创建测试设备"""
    device = Device(
        name="test-device",
        description="Test Device",
        status=2,
        tags="test,device",
        ip_address="*************",
        mac_address="00:11:22:33:44:55",
        owner_id=normal_user.id,
        service_configs={}
    )
    db_session.add(device)
    db_session.commit()

    yield device

    db_session.delete(device)
    db_session.commit()


def test_project_creation(project):
    """测试项目创建"""
    assert project.name == "test-project"
    assert project.form_schema["properties"]["email"]["type"] == "string"
    assert project.form_schema["properties"]["password"]["type"] == "string"
    assert project.form_schema["properties"]["proxy"]["type"] == "string"


def test_project_file_creation(project_files):
    """测试项目文件创建"""
    assert len(project_files) == 2
    assert project_files[0].name == "config.json"
    assert project_files[1].name == "proxy.conf"


def test_device_project_creation(db_session, project, device):
    """测试设备项目关联创建"""
    device_project = DeviceProject(
        device_id=device.id,
        project_id=project.id,
        state="created",
        data={"test": "data"}
    )
    db_session.add(device_project)
    db_session.commit()

    # 验证关联关系
    assert device_project.device == device
    assert device_project.project == project
    assert device_project.state == "created"
    assert device_project.data == {"test": "data"}


def test_device_project_file_rendering(db_session, project, project_files, device):
    """测试设备项目文件渲染"""
    # 创建设备项目关联
    device_project = DeviceProject(
        device_id=device.id,
        project_id=project.id,
        state="created",
        data={
            "email": "test-service",
            "password": "test-password",
            "proxy": "8080"
        }
    )
    db_session.add(device_project)
    db_session.commit()

    # 测试文件渲染
    for file in project_files:
        rendered_content = device_project.render_file_content(file.content)
        assert rendered_content is not None
        if "config.json" in file.name:
            assert '"email": "test-service"' in rendered_content
            assert '"password": "test-password"' in rendered_content
        elif "proxy.conf" in file.name:
            assert "proxy_port=8080" in rendered_content


def test_generate_service_config(db_session, project, project_files, device):
    """测试生成服务配置"""
    # 创建设备项目关联
    device_project = DeviceProject(
        device_id=device.id,
        project_id=project.id,
        state="created",
        data={
            "email": "test-service",
            "password": "test-password",
            "proxy": "8080",
            "proxy_enable": True,
            "proxy_scope": "Project proxy"
        }
    )
    db_session.add(device_project)
    db_session.commit()

    # 生成服务配置
    project_service = ProjectService()
    config = project_service.generate_device_project_config(device_project)

    # 验证配置
    assert config is not None
    assert "docker_compose" in config

    # 验证 docker-compose 配置
    compose = config["docker_compose"]
    assert "services" in compose
    print(compose)
    assert "app" in compose["services"]
    assert compose["services"]["app"]["environment"]["EMAIL"] == "test-service"
    assert compose["services"]["app"]["environment"]["PASSWORD"] == "test-password"
    assert compose["services"]["app"]["environment"]["PROXY_PORT"] == "8080"
    assert compose["services"]["app"]["ports"] == ["8080:80"]

    # 验证文件配置
    assert "files" in config
    assert "config.json" in config["files"]
    assert "proxy.conf" in config["files"]
    assert '"email": "test-service"' in config["files"]["config.json"]
    assert '"password": "test-password"' in config["files"]["config.json"]
    assert "proxy_port=8080" in config["files"]["proxy.conf"]

    # 清理
    db_session.delete(device_project)
    db_session.commit()