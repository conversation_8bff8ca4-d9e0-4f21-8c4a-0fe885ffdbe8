"""公共服务配置 API 测试模块"""
import pytest

from app.models.base import db
from app.models.service_config import ServiceConfig


@pytest.fixture
def test_service_config(app):
    """创建测试服务配置"""
    with app.app_context():
        # 清理可能存在的配置
        ServiceConfig.query.filter_by(name="test-service").delete()
        db.session.commit()

        config = ServiceConfig(
            name="test-service",
            description="Test Service Config",
            docker_compose={
                "version": "3",
                "services": {
                    "test": {
                        "image": "test:latest"
                    }
                }
            },
            default_env={"TEST_PORT": "8080"}
        )
        db.session.add(config)
        db.session.commit()

        # 刷新对象以确保它在会话中
        db.session.refresh(config)
        yield config

        # 清理
        ServiceConfig.query.filter_by(name="test-service").delete()
        db.session.commit()

def test_list_service_configs(client, admin_token, test_service_config):
    """测试获取公共服务配置列表"""
    # 测试管理员获取列表
    response = client.get(
        "/api/service-configs",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    data = response.get_json()
    assert data["code"] == 200
    assert len(data["data"]) >= 1
    assert any(config["name"] == "test-service" for config in data["data"])

def test_list_service_configs_unauthorized(client, normal_user_headers):
    """测试未授权用户获取公共服务配置列表"""
    response = client.get("/api/service-configs", headers=normal_user_headers)
    assert response.status_code == 403
    data = response.get_json()
    assert data["code"] == 403
    assert "权限不足" in data["message"]

def test_create_service_config(client, admin_token):
    """测试创建公共服务配置"""
    # 清理测试数据
    with client.application.app_context():
        ServiceConfig.query.filter_by(name="Redis服务").delete()
        db.session.commit()

    # 准备测试数据
    config_data = {
        "name": "Redis服务",
        "description": "测试Redis服务",
        "docker_compose": {
            "version": "3",
            "services": {
                "redis": {
                    "image": "redis:latest"
                }
            }
        },
        "env": {"REDIS_PORT": "6379"}
    }

    # 测试创建配置
    response = client.post(
        "/api/service-configs",
        headers={"Authorization": f"Bearer {admin_token}"},
        json=config_data
    )
    assert response.status_code == 200
    data = response.get_json()
    assert data["code"] == 200
    assert data["data"]["name"] == config_data["name"]
    assert data["data"]["description"] == config_data["description"]

    # 验证数据库
    with client.application.app_context():
        config = ServiceConfig.query.filter_by(name=config_data["name"]).first()
        assert config is not None
        assert config.description == config_data["description"]
        assert config.default_env == config_data["env"]

def test_create_service_config_unauthorized(client, normal_user_headers):
    """测试未授权用户创建公共服务配置"""
    config_data = {
        "name": "Redis服务",
        "description": "测试Redis服务",
        "docker_compose": {
            "version": "3",
            "services": {
                "redis": {
                    "image": "redis:latest"
                }
            }
        },
        "env": {"REDIS_PORT": "6379"}
    }

    response = client.post("/api/service-configs", headers=normal_user_headers, json=config_data)
    assert response.status_code == 403
    data = response.get_json()
    assert data["code"] == 403
    assert "权限不足" in data["message"]

def test_create_service_config_invalid(client, admin_token, test_service_config):
    """测试创建无效的公共服务配置"""
    # 测试重复名称
    config_data = {
        "name": "test-service",  # 已存在的名称
        "description": "重复的服务",
        "docker_compose": {
            "version": "3",
            "services": {
                "test": {
                    "image": "test:latest"
                }
            }
        },
        "env": {"TEST_PORT": "8080"}
    }

    response = client.post(
        "/api/service-configs",
        headers={"Authorization": f"Bearer {admin_token}"},
        json=config_data
    )
    assert response.status_code == 400
    data = response.get_json()
    assert data["code"] == 400
    assert "该名称已存在" in data["message"]

    # 测试缺少必要字段
    invalid_data = {
        "name": "test-service-2"
        # 缺少 docker_compose
    }

    response = client.post(
        "/api/service-configs",
        headers={"Authorization": f"Bearer {admin_token}"},
        json=invalid_data
    )
    assert response.status_code == 400
    data = response.get_json()
    assert data["code"] == 400
    assert "必填项" in data["message"]

def test_get_service_config(client, admin_token, test_service_config):
    """测试获取单个公共服务配置"""
    response = client.get(
        f"/api/service-configs/{test_service_config.id}",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    data = response.get_json()
    assert data["code"] == 200
    assert data["data"]["name"] == test_service_config.name
    assert data["data"]["description"] == test_service_config.description

def test_get_service_config_not_found(client, admin_token):
    """测试获取不存在的公共服务配置"""
    response = client.get(
        "/api/service-configs/99999",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 404
    data = response.get_json()
    assert data["code"] == 404
    assert "不存在" in data["message"]

def test_update_service_config(client, admin_token, test_service_config):
    """测试更新公共服务配置"""
    update_data = {
        "name": "test-service-updated",
        "description": "Updated Service Config",
        "docker_compose": {
            "version": "3",
            "services": {
                "test": {
                    "image": "test:2.0"
                }
            }
        },
        "env": {"TEST_PORT": "8081"}
    }

    response = client.put(
        f"/api/service-configs/{test_service_config.id}",
        headers={"Authorization": f"Bearer {admin_token}"},
        json=update_data
    )
    assert response.status_code == 200
    data = response.get_json()
    assert data["code"] == 200
    assert data["data"]["name"] == update_data["name"]
    assert data["data"]["description"] == update_data["description"]

    # 验证数据库
    with client.application.app_context():
        config = ServiceConfig.query.get(test_service_config.id)
        assert config.name == update_data["name"]
        assert config.description == update_data["description"]
        assert config.default_env == update_data["env"]

def test_update_service_config_partial(client, admin_token, test_service_config):
    """测试部分更新公共服务配置"""
    update_data = {
        "description": "Only Update Description"
    }

    response = client.put(
        f"/api/service-configs/{test_service_config.id}",
        headers={"Authorization": f"Bearer {admin_token}"},
        json=update_data
    )
    assert response.status_code == 200
    data = response.get_json()
    assert data["code"] == 200
    assert data["data"]["name"] == test_service_config.name  # 名称未变
    assert data["data"]["description"] == update_data["description"]

def test_delete_service_config(client, admin_token, test_service_config):
    """测试删除公共服务配置"""
    response = client.delete(
        f"/api/service-configs/{test_service_config.id}",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    data = response.get_json()
    assert data["code"] == 200

    # 验证数据库
    with client.application.app_context():
        config = ServiceConfig.query.get(test_service_config.id)
        assert config is None

def test_delete_service_config_not_found(client, admin_token):
    """测试删除不存在的公共服务配置"""
    response = client.delete(
        "/api/service-configs/99999",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 404
    data = response.get_json()
    assert data["code"] == 404
    assert "不存在" in data["message"]
