"""测试配置模块"""

import os
import tempfile

import pytest
from unittest.mock import patch
from fakeredis import FakeStrictRedis

from app import create_app
from app.models.base import db
from app.models.device import Device
from app.models.project import Project
from app.models.project_file import ProjectFile
from app.models.user import User
from app.utils.redis_client import RedisClient


@pytest.fixture
def app():
    """创建应用实例"""
    # 创建临时数据库文件
    db_fd, db_path = tempfile.mkstemp()

    app = create_app("testing")
    
    # 确保使用测试数据库
    app.config["SQLALCHEMY_DATABASE_URI"] = f"sqlite:///{db_path}"
    
    # 设置 Celery 测试配置
    app.config.update({
        'CELERY_BROKER_URL': 'memory://',
        'CELERY_RESULT_BACKEND': 'cache',
        'CELERY_CACHE_BACKEND': 'memory',
        'CELERY_ALWAYS_EAGER': True,
        'CELERY_TASK_ALWAYS_EAGER': True,
        'CELERY_TASK_EAGER_PROPAGATES': True
    })

    # 使用 fakeredis 替代真实 redis
    fake_redis = FakeStrictRedis()
    RedisClient._instance = fake_redis

    with app.app_context():
        db.create_all()
        yield app

        # 清理数据库
        db.session.remove()
        db.drop_all()
        # 测试结束后清理 redis 实例
        RedisClient._instance = None

    # 删除临时文件
    os.close(db_fd)
    os.unlink(db_path)


@pytest.fixture
def client(app):
    """创建测试客户端"""
    return app.test_client()


@pytest.fixture
def db_session(app):
    """创建数据库会话"""
    with app.app_context():
        # 使用现有的数据库会话
        yield db.session
        # 回滚事务
        db.session.rollback()


@pytest.fixture
def admin_user(app):
    """创建管理员用户"""
    with app.app_context():
        # 清理已存在的用户
        User.query.filter_by(username="admin").delete()
        db.session.commit()

        # 创建新用户
        user = User(
            username="admin",
            email="<EMAIL>",
            role="admin"
        )
        user.set_password("password123")
        db.session.add(user)
        db.session.commit()
        return user


@pytest.fixture
def admin_token(client, admin_user):
    """获取管理员令牌"""
    response = client.post(
        "/api/auth/login", json={"username": "admin", "password": "password123"}
    )
    return response.get_json()["data"]["token"]


@pytest.fixture
def normal_user(app):
    """创建普通用户"""
    with app.app_context():
        # 清理已存在的用户
        User.query.filter_by(username="user").delete()
        db.session.commit()

        # 创建新用户
        user = User(
            username="user",
            email="<EMAIL>",
            role="user"
        )
        user.set_password("password123")
        db.session.add(user)
        db.session.commit()
        return user


@pytest.fixture
def normal_user_headers(app, client, normal_user):
    """获取普通用户的认证头"""
    with app.app_context():
        response = client.post(
            "/api/auth/login",
            json={
                "username": "user",
                "password": "password123"
            }
        )
        assert response.status_code == 200
        token = response.get_json()["data"]["token"]
        return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def test_device(app, normal_user):
    """创建测试设备"""
    with app.app_context():
        # 清理可能存在的设备
        device_name = "test-device"
        Device.query.filter_by(name=device_name).delete()
        db.session.commit()

        # 创建新设备
        device = Device(
            name=device_name,
            description="Test Device",
            status="active",
            tags="test,device",
            ip_address="*************",
            mac_address="00:11:22:33:44:55",
            owner_id=normal_user.id
        )
        db.session.add(device)
        db.session.commit()

        # 刷新对象以确保它在会话中
        db.session.refresh(device)
        yield device

        # 清理
        Device.query.filter_by(name=device_name).delete()
        db.session.commit()


@pytest.fixture
def test_project(app, service_config):
    """创建测试项目"""
    with app.app_context():
        # 清理可能存在的项目
        Project.query.filter_by(name="test-project").delete()
        db.session.commit()

        project = Project(
            name="test-project",
            description="Test Project",
            service_config_id=service_config.id,
            status=Project.STATUS_ENABLED
        )
        db.session.add(project)
        db.session.commit()

        # 刷新对象以确保它在会话中
        db.session.refresh(project)
        yield project

        # 清理
        Project.query.filter_by(name="test-project").delete()
        db.session.commit()


@pytest.fixture
def project_files(app, test_project):
    """创建测试项目文件"""
    with app.app_context():
        # 确保 test_project 在会话中
        db.session.add(test_project)
        db.session.commit()

        files = [
            ProjectFile(
                name="test/auth.txt",
                content="name: {{ name }}",
                project_id=test_project.id
            ),
            ProjectFile(
                name="test/proxy.txt",
                content="port: {{ port }}",
                project_id=test_project.id
            )
        ]
        for file in files:
            db.session.add(file)
        db.session.commit()
        return files


@pytest.fixture
def auth_headers(app, client, admin_user):
    """创建认证头"""
    with app.app_context():
        response = client.post(
            "/api/auth/login",
            json={
                "username": "admin",
                "password": "password123"
            }
        )
        assert response.status_code == 200
        token = response.get_json()["data"]["token"]
        return {"Authorization": f"Bearer {token}"}


@pytest.fixture(autouse=True)
def mock_celery():
    """Mock Celery for testing"""
    with patch('celery.shared_task', lambda *args, **kwargs: lambda f: f):
        yield


@pytest.fixture
def runner(app):
    """创建测试命令行运行器"""
    return app.test_cli_runner()
