import pytest
from datetime import datetime, timezone, timedelta
from unittest.mock import patch, MagicMock
from decimal import Decimal

from app.models.base import db
from app.models.device import Device
from app.models.project import Project
from app.models.blockchain import AssetType, AssetTypeEnum
from app.models.user import User
from app.models.service_config import ServiceConfig
from app.tasks.lighter_points_task import award_lighter_device_points, LIGHTER_PROJECT_NAME
from app.services.asset_service import AssetService


@pytest.fixture
def setup_lighter_test_data(app):
    """设置 lighter 项目积分测试数据"""
    with app.app_context():
        # 创建测试用户
        user = User(
            username="lighter_user",
            email="<EMAIL>"
        )
        user.set_password("password")
        db.session.add(user)
        db.session.commit()
        
        # 创建服务配置
        service_config = ServiceConfig(
            name="lighter-service",
            description="Lighter Service Config",
            docker_compose={"version": "3", "services": {"app": {"image": "lighter:latest"}}}
        )
        db.session.add(service_config)
        db.session.commit()
        
        # 创建 lighter 项目
        project = Project(
            name=LIGHTER_PROJECT_NAME,
            description="Lighter Project for Testing",
            service_config_id=service_config.id,
            status="enabled"
        )
        db.session.add(project)
        db.session.commit()
        
        # 创建积分资产类型
        asset_type = AssetType(
            name="Lighter Points",
            type=AssetTypeEnum.POINTS,
            decimals=2,
            project_id=project.id
        )
        db.session.add(asset_type)
        db.session.commit()
        
        # 创建测试设备
        device1 = Device(
            name="Lighter Device 1",
            status=2,  # running
            owner=user,
            ip_address="*************",
            mac_address="00:11:22:33:44:57",
            system_app_configs={}  # 没有上次获得积分的记录
        )
        
        # 创建已经获得过积分的设备
        now = datetime.now(timezone.utc)
        device2 = Device(
            name="Lighter Device 2",
            status=2,  # running
            owner=user,
            ip_address="*************",
            mac_address="00:11:22:33:44:58",
            system_app_configs={
                'lighter_last_award_time': now.isoformat()  # 刚刚获得过积分
            }
        )
        
        # 创建24小时前获得过积分的设备
        time_24h_ago = now - timedelta(hours=25)  # 25小时前，确保超过24小时
        device3 = Device(
            name="Lighter Device 3",
            status=2,  # running
            owner=user,
            ip_address="*************",
            mac_address="00:11:22:33:44:59",
            system_app_configs={
                'lighter_last_award_time': time_24h_ago.isoformat()  # 24小时前获得过积分
            }
        )
        
        # 创建没有所有者的设备
        device4 = Device(
            name="Lighter Device 4",
            status=2,  # running
            owner_id=None,  # 没有所有者
            ip_address="*************",
            mac_address="00:11:22:33:44:60",
            system_app_configs={}
        )
        
        db.session.add_all([device1, device2, device3, device4])
        db.session.commit()
        
        # 存储测试数据的ID
        test_data = {
            "user_id": user.id,
            "project_id": project.id,
            "asset_type_id": asset_type.id,
            "device1_id": device1.id,  # 从未获得过积分的设备
            "device2_id": device2.id,  # 刚刚获得过积分的设备
            "device3_id": device3.id,  # 24小时前获得过积分的设备
            "device4_id": device4.id,  # 没有所有者的设备
        }
        
        yield test_data
        
        # 清理测试数据
        db.session.query(Device).delete()
        db.session.query(AssetType).delete()
        db.session.query(Project).delete()
        db.session.query(User).delete()
        db.session.commit()


def test_award_lighter_device_points_success(app, setup_lighter_test_data):
    """测试 lighter 项目积分正常发放"""
    with app.app_context():
        test_data = setup_lighter_test_data
        
        # 模拟 AssetService.add_or_create_user_asset 方法
        with patch.object(AssetService, 'add_or_create_user_asset') as mock_add_asset:
            # 执行任务
            result = award_lighter_device_points()
            
            # 验证结果
            assert result["status"] == "success"
            
            # 应该有两个设备获得积分：device1（从未获得过积分）和 device3（24小时前获得过积分）
            assert result["awarded_count"] == 2
            
            # 验证 AssetService.add_or_create_user_asset 被调用了两次
            assert mock_add_asset.call_count == 2
            
            # 验证设备1的积分发放
            device1 = db.session.get(Device, test_data["device1_id"])
            assert 'lighter_last_award_time' in device1.system_app_configs
            
            # 验证设备3的积分发放
            device3 = db.session.get(Device, test_data["device3_id"])
            assert 'lighter_last_award_time' in device3.system_app_configs
            
            # 验证设备2没有获得积分（因为刚刚获得过）
            device2 = db.session.get(Device, test_data["device2_id"])
            # 确保时间戳没有变化（即没有重新发放积分）
            assert 'lighter_last_award_time' in device2.system_app_configs


def test_award_lighter_device_points_no_duplicate(app, setup_lighter_test_data):
    """测试 lighter 项目积分不会重复发放（24小时内）"""
    with app.app_context():
        test_data = setup_lighter_test_data
        
        # 模拟 AssetService.add_or_create_user_asset 方法
        with patch.object(AssetService, 'add_or_create_user_asset') as mock_add_asset:
            # 执行任务
            result = award_lighter_device_points()
            
            # 验证结果
            assert result["status"] == "success"
            
            # 验证设备2没有被调用 add_or_create_user_asset（因为24小时内已经获得过积分）
            for call_args in mock_add_asset.call_args_list:
                args, kwargs = call_args
                assert kwargs["user_id"] != test_data["device2_id"]


def test_award_lighter_device_points_project_not_exist(app):
    """测试 lighter 项目不存在的情况"""
    with app.app_context():
        # 确保 lighter 项目不存在
        Project.query.filter_by(name=LIGHTER_PROJECT_NAME).delete()
        db.session.commit()
        
        # 执行任务
        result = award_lighter_device_points()
        
        # 验证结果
        assert result["status"] == "error"
        assert LIGHTER_PROJECT_NAME in result["message"]


def test_award_lighter_device_points_asset_type_not_exist(app, setup_lighter_test_data):
    """测试积分资产类型不存在的情况"""
    with app.app_context():
        # 删除积分资产类型
        AssetType.query.filter_by(id=setup_lighter_test_data["asset_type_id"]).delete()
        db.session.commit()
        
        # 模拟 AssetService.add_or_create_user_asset 方法
        with patch.object(AssetService, 'add_or_create_user_asset') as mock_add_asset:
            # 执行任务
            result = award_lighter_device_points()
            
            # 验证结果 - 任务应该成功执行，但没有设备获得积分
            assert result["status"] == "success"
            assert result["awarded_count"] == 0
            
            # 验证 AssetService.add_or_create_user_asset 没有被调用
            mock_add_asset.assert_not_called()