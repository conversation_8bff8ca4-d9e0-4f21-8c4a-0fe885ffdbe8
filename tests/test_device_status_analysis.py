import json
from datetime import datetime, timezone, timedelta

import pytest

from app.models import Device, User, db
from app.models.device_project import DeviceProject
from app.models.metrics import ServiceMetricsDetail
from app.models.project import Project
from app.models.service_config import ServiceConfig


@pytest.fixture
def client(app):
    return app.test_client()


@pytest.fixture
def admin_user(app):
    """创建管理员用户"""
    with app.app_context():
        user = User(username="admin", email="<EMAIL>", role="admin")
        user.set_password("admin")
        db.session.add(user)
        db.session.commit()

        yield user

        User.query.filter_by(username="admin").delete()
        db.session.commit()


@pytest.fixture
def normal_user_headers(client, normal_user):
    """获取用户认证头"""
    response = client.post(
        "/api/auth/login", json={"username": "admin", "password": "admin"}
    )
    data = json.loads(response.data)
    return {"Authorization": f"Bearer {data['data']['token']}"}


def test_setup_data(app, client):
    with app.app_context():
        user = User(username="admin", email="<EMAIL>", role="admin")
        user.set_password("admin")
        db.session.add(user)

        config = ServiceConfig(
            name="test-service",
            description="Test Service Config",
            docker_compose="test-service",
        )
        db.session.add(config)
        db.session.flush()

        # 创建设备和项目
        device = Device(
            name='device_name',
            description="Test Device",
            status=4,
            tags="test,device",
            ip_address="*************",
            mac_address="00:11:22:33:44:55",
            owner_id=user.id
        )
        project = Project(name='Test Project', service_config_id = config.id)

        db.session.add(device)
        db.session.add(project)
        db.session.flush()

        device_project = DeviceProject(device_id=device.id, project_id=project.id, state="updated")

        metrics_arr = [
            ServiceMetricsDetail(device_id=device.id, updated_at=datetime.now(timezone.utc) - timedelta(hours=3),
                                 service_name='Test Project', points=100, running_time=3600),
            ServiceMetricsDetail(device_id=device.id, updated_at=datetime.now(timezone.utc) - timedelta(hours=2),
                                 service_name='Test Project', points=100, running_time=3600),
            ServiceMetricsDetail(device_id=device.id, updated_at=datetime.now(timezone.utc) - timedelta(hours=1),
                                 service_name='Test Project', points=100, running_time=3600),
            ServiceMetricsDetail(device_id=device.id, updated_at=datetime.now(timezone.utc) - timedelta(hours=0),
                                 service_name='Test Project', points=100, running_time=3600),
        ]
        db.session.add(device)
        db.session.add(project)
        db.session.add(device_project)

        db.session.add_all(metrics_arr)
        db.session.commit()

        response = client.post(
            "/api/auth/login", json={"username": "admin", "password": "admin"}
        )
        data = json.loads(response.data)
        auth_headers = {"Authorization": f"Bearer {data['data']['token']}"}

        response = client.get(
            "/api/devices/1/status_analysis",
            headers=auth_headers
        )
        print(json.loads(response.data))
