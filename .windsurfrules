Here are some best practices and rules you must follow:

- You use Python 3.10+
- Frameworks:
    - Flask
    - SQLAlchemy
- You use uv to manage the dependencies, and install the dependencies with command: uv sync
- You use ruff to format the code, and check the code with ruff check, and run ruff use command: uvx ruff check --fix
- You use pytest to write the tests, and run the tests with command: uv run pytest
- You use uv to run the code, and run the code with command: uv run flask run
- 保持对现有代码的风格、错误、或者警告，保持不对已有代码做任何变更的原则。如遇到需要变更的地方，需要进行确认
- 接口代码不要做完整的try catch Exception，这样会难以定位到错误，异常直接返回错误栈
- 数据库禁止使用 join 查询，需要使用子查询，已有代码的该逻辑禁止修改


1. **Use Docstrings**: Document functions and classes with docstrings to explain their purpose.
2. **Use Type Hints**: Add type hints to all functions and variables to improve readability and maintainability.