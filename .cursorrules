Here are some best practices and rules you must follow:

- You use Python 3.10+
- Frameworks:
    - Flask
    - SQLAlchemy
- You use uv to manage the dependencies, and install the dependencies with command: uv sync
- You use ruff to format the code, and check the code with ruff check, and run ruff use command: uvx ruff check --fix
- You use pytest to write the tests, and run the tests with command: uv run pytest
- You use uv to run the code, and run the code with command: uv run flask run


1. **Use Docstrings**: Document functions and classes with docstrings to explain their purpose.
2. **Use Type Hints**: Add type hints to all functions and variables to improve readability and maintainability.
