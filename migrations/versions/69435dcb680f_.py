"""描述信息

Revision ID: 69435dcb680f
Revises:
Create Date: 2025-04-09 22:02:53.054577

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '69435dcb680f'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('point_records', schema=None) as batch_op:
        batch_op.add_column(sa.Column('related_id', sa.Integer(), nullable=True))

    with op.batch_alter_table('service_metrics', schema=None) as batch_op:
        batch_op.add_column(sa.Column('ubi_points', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('project_id', sa.Integer(), nullable=True))

    with op.batch_alter_table('service_metrics_snapshots', schema=None) as batch_op:
        batch_op.add_column(sa.Column('ubi_points', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('project_id', sa.Integer(), nullable=True))

    with op.batch_alter_table('service_metric_details', schema=None) as batch_op:
        batch_op.add_column(sa.Column('project_id', sa.Integer(), nullable=True))

    with op.batch_alter_table('wallet_generated', schema=None) as batch_op:
        batch_op.create_index('idx_wallet_generated_group_user_id', ['group_id', 'user_id'])

    op.create_table('notify',
                    sa.Column('id', sa.Integer(), nullable=False),
                    sa.Column('notify_type', sa.String(50), nullable=False, comment='通知类型'),
                    sa.Column('title', sa.JSON(), nullable=False, comment='通知标题'),
                    sa.Column('content', sa.JSON(), nullable=False, comment='通知内容'),
                    sa.Column('notify_status', sa.Integer(), server_default="0", nullable=False, comment='通知状态 0 - 草稿; 1 - 已发布; 2 - 撤回'),
                    sa.Column('notify_priority', sa.Integer(), server_default="1", nullable=False, comment='通知优先级 0 - 低; 1 - 普通; 2 - 高'),
                    sa.Column('extend_field', sa.JSON(), nullable=True, comment='扩展字段'),
                    sa.Column('is_deleted', sa.Boolean(), server_default=sa.false(), nullable=False, comment='是否删除'),
                    sa.Column('creator', sa.Integer(), nullable=True, comment='创建人ID'),
                    sa.Column('updator', sa.Integer(), nullable=True, comment='更新人ID'),
                    sa.Column('created_at', sa.DateTime(), nullable=True),
                    sa.Column('updated_at', sa.DateTime(), nullable=True),
                    sa.PrimaryKeyConstraint('id'))
    with op.batch_alter_table('notify', schema=None) as batch_op:
        batch_op.create_index('idx_notify_type', ['notify_type'])

    op.create_table('user_notify',
                    sa.Column('id', sa.Integer(), nullable=False),
                    sa.Column('user_id', sa.Integer(), nullable=False, comment='用户ID'),
                    sa.Column('notify_id', sa.Integer(), nullable=False, comment='通知ID'),
                    sa.Column('is_read', sa.Boolean(), server_default=sa.false(), nullable=True, comment='阅读状态'),
                    sa.Column('read_timestamp', sa.DateTime(), nullable=True, comment='阅读时间'),
                    sa.Column('created_at', sa.DateTime(), nullable=True),
                    sa.Column('updated_at', sa.DateTime(), nullable=True),
                    sa.PrimaryKeyConstraint('id'))

    with op.batch_alter_table('user_notify', schema=None) as batch_op:
        batch_op.create_index('idx_user_id_notify_id', ['user_id', 'notify_id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    with op.batch_alter_table('service_metrics_snapshots', schema=None) as batch_op:
        batch_op.drop_column('project_id')
        batch_op.drop_column('ubi_points')

    with op.batch_alter_table('service_metrics', schema=None) as batch_op:
        batch_op.drop_column('project_id')
        batch_op.drop_column('ubi_points')

    with op.batch_alter_table('service_metric_details', schema=None) as batch_op:
        batch_op.drop_column('project_id')

    with op.batch_alter_table('point_records', schema=None) as batch_op:
        batch_op.drop_column('related_id')

    with op.batch_alter_table('wallet_generated', schema=None) as batch_op:
        batch_op.drop_index('idx_wallet_generated_group_user_id')

    op.drop_table('notify')
    op.drop_table('user_notify')
    # ### end Alembic commands ###
