"""empty message

Revision ID: 3f27ca789a6c
Revises: 
Create Date: 2025-04-01 09:54:38.891159

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '3f27ca789a6c'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('wallet_generated',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('address', sa.String(length=255), nullable=False),
    sa.Column('encrypt_key', sa.String(length=255), nullable=False),
    sa.Column('chain_code', sa.String(length=50), nullable=False),
    sa.Column('status', sa.Integer(), nullable=True),
    sa.Column('group_id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('wallet_generated', schema=None) as batch_op:
        batch_op.create_index('idx_wallet_generated_user_id', ['user_id'], unique=False)

    op.create_table('wallet_group',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=True),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('device_id', sa.Integer(), nullable=True),
    sa.Column('nonce', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('wallet_group', schema=None) as batch_op:
        batch_op.create_index('idx_wallet_group_user_id', ['user_id'], unique=False)

    op.create_table('wallet_log',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('wallet_id', sa.Integer(), nullable=False),
    sa.Column('wallet_address', sa.String(length=255), nullable=False),
    sa.Column('data', sa.Text(), nullable=False),
    sa.Column('chain_code', sa.String(length=50), nullable=False),
    sa.Column('tx_hash', sa.String(length=255), nullable=True),
    sa.Column('op_time', sa.DateTime(), nullable=True),
    sa.Column('operation', sa.String(length=50), nullable=False),
    sa.Column('error', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('wallet_log', schema=None) as batch_op:
        batch_op.create_index('idx_wallet_log_wallet_id', ['wallet_id'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('wallet_log', schema=None) as batch_op:
        batch_op.drop_index('idx_wallet_log_wallet_id')

    op.drop_table('wallet_log')
    with op.batch_alter_table('wallet_group', schema=None) as batch_op:
        batch_op.drop_index('idx_wallet_group_user_id')

    op.drop_table('wallet_group')
    with op.batch_alter_table('wallet_generated', schema=None) as batch_op:
        batch_op.drop_index('idx_wallet_generated_user_id')

    op.drop_table('wallet_generated')
    # ### end Alembic commands ###
