# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/
.env

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Flask
instance/
.webassets-cache

# SQLite
*.db
*.sqlite3

# Logs
*.log
logs/

# Coverage
.coverage
coverage.xml
htmlcov/

# pytest
.pytest_cache/
.cache

# mypy
.mypy_cache/

# Jupyter Notebook
.ipynb_checkpoints

# Local development configuration
local_settings.py

# Migration files
# migrations/
.python-version
celerybeat-schedule
