# Box 后端服务

[![Tests](https://github.com/0xUBI/box-backend/actions/workflows/test.yml/badge.svg)](https://github.com/0xUBI/box-backend/actions/workflows/test.yml)

Box 后端服务是一个基于 Python Flask 框架开发的 RESTful API 服务，提供设备远程配置、用户认证等功能。

## 目录

- [快速开始](#快速开始)
- [项目架构](#项目架构)
- [API 文档](#api-文档)
- [开发指南](#开发指南)
- [部署指南](#部署指南)

## 快速开始

### 环境要求

- Python 3.11+
- MySQL 8.0
- uv 包管理器

### 本地开发环境部署

1. **安装 uv 包管理器**

```bash
pip install uv
```

2. **安装项目依赖**

```bash
uv sync
```

3. **配置环境变量**

复制环境变量模板：
```bash
cp .env.template .env
```

4. **配置数据库**

开发/生产环境（MySQL）：
```bash
DATABASE_URL=mysql+pymysql://username:password@localhost/dbname?charset=utf8mb4
```

测试环境（SQLite）：
```bash
TEST_DATABASE_URL=sqlite:///:memory:
```

5. **初始化数据库**

```bash
# 创建数据库表
uv run flask init-db

# 创建管理员账号
uv run flask create-admin
```

6. **启动开发服务器**

```bash
uv run flask run
```

## 项目架构

```
box-backend/
├── app/                    # 应用主目录
│   ├── __init__.py        # 应用初始化
│   ├── models/            # 数据模型
│   ├── api/               # API 路由
│   ├── schemas/           # 序列化模式
│   ├── services/          # 业务逻辑
│   └── utils/             # 工具函数
├── tests/                 # 测试目录
├── config.py              # 配置文件
└── run.py                # 应用入口
```

## API 文档

本文档主要介绍面向设备的远程配置 API。如需了解系统内部的用户认证、设备管理等 API，请参考 [系统管理 API 文档](docs/api.md)。

### 远程配置接口

远程配置接口为设备提供加密的系统和服务配置信息。每个设备通过唯一的 token 进行认证，获取其专属配置。

#### 认证方式

所有远程配置接口（除了获取 token 接口）都需要在请求头中携带设备 token：

```http
Authorization: <device_token>
```

#### 接口列表

##### 1. 获取设备令牌

通过加密的 MAC 地址获取设备令牌（token）。

```http
POST /api/remote/devices/token

请求体：
{
    "encrypted": "base64编码的加密MAC地址",
    "nonce": "base64编码的nonce"
}

响应：
{
    "encrypted": "base64编码的加密token",
    "nonce": "base64编码的nonce"
}
```

##### 2. 获取系统配置

```http
GET /api/remote/configs/system

响应：
{
    "encrypted": "base64编码的加密数据",
    "nonce": "base64编码的nonce"
}
```

解密后的数据格式：
```json
{
    "name": "test-app",
    "version": "v1.0.0",
    "download_url": "https://example.com/test-app",
    "command": "test-app",
    "args": ["-c", "config.toml"],
    "configs": {
        "config.toml": "test = false"
    }
}
```

##### 3. 获取服务配置

```http
GET /api/remote/configs/service

响应：
{
    "encrypted": "base64编码的加密数据",
    "nonce": "base64编码的nonce"
}
```

解密后的数据格式：
```json
{
    "docker_compose": "version: '3'\nservices:\n  test:\n    image: test:latest",
    "projects": [
        {
            "name": "project1",
            "files": ["config.txt", "nodes.txt"]
        },
        {
            "name": "project2",
            "files": ["config.txt", "nodes.txt"]
        }
    ],
    "configs": {
        "project1/config.txt": "配置内容...",
        "project1/nodes.txt": "节点列表...",
        "project2/config.txt": "配置内容...",
        "project2/nodes.txt": "节点列表..." ,
    }
}
```

#### 3. 远程 JSON-RPC 接口

用于设备与远程服务安全通信，支持标准 JSON-RPC 2.0 协议，所有数据均加密传输。

```http
POST /api/remote/jsonrpc
Authorization: <device_token>
Content-Type: application/json

请求体：
{
    "encrypted": "base64编码的加密JSON-RPC请求体",
    "nonce": "base64编码的nonce"
}
```

加密前的 JSON-RPC 请求体示例：
```json
{
    "jsonrpc": "2.0",
    "method": "test_method",
    "params": [1, 2, 3],
    "id": 123
}
```

响应体：
```json
{
    "encrypted": "base64编码的加密JSON-RPC响应体",
    "nonce": "base64编码的nonce"
}
```

解密后的响应体示例：
```json
{
    "jsonrpc": "2.0",
    "id": 123,
    "result": {
        # 服务返回的结果
    }
}
```

- 加密算法：AES-GCM，密钥长度32字节，nonce长度12字节
- 所有请求和响应均需加密，防止数据泄露
- 仅支持 POST 方法
- 认证方式同其他远程配置接口

### 配置加密说明

- 加密算法：AES-GCM
- 密钥长度：32 字节
- Nonce 长度：12 字节
- 加密数据和 nonce 使用 base64 编码传输

### API 设计说明

远程配置 API 遵循 RESTful 设计原则：
1. 使用资源集合（configs）表示配置资源
2. 使用名词而不是动词
3. 使用层级结构表示资源关系
4. 统一的错误响应格式

## 开发指南

### 环境配置

在 `.env` 文件中配置以下环境变量：

| 变量名 | 说明 | 示例值 |
|--------|------|--------|
| FLASK_ENV | 运行环境 | development/testing/production |
| SECRET_KEY | Flask 密钥 | your-secret-key |
| JWT_SECRET_KEY | JWT 密钥 | your-jwt-secret |
| DATABASE_URL | 数据库连接 URL | mysql+pymysql://user:pass@localhost/db |
| ENCRYPTION_KEY | 配置加密密钥（32字节） | your-32-byte-encryption-key |
| REDIS_HOST | Redis 主机地址 | localhost |
| REDIS_PORT | Redis 端口 | 6379 |
| REDIS_PASSWORD | Redis 密码 | your-redis-password |
| REDIS_DB | Redis 数据库编号 | 0 |

### 运行测试

```bash
# 运行所有测试
uv run pytest

# 生成测试覆盖率报告
uv run pytest --cov=app tests/
```

### 运行应用

```bash
# 运行 Flask 应用
uv run flask run

# 运行 Celery worker
uv run celery -A run.celery worker --loglevel=info

# 运行 Celery beat
uv run celery -A run.celery beat --loglevel=info
```

### 开发规范

- 遵循 PEP 8 Python 代码规范
- 使用类型注解
- 编写完整的函数文档
- 保持良好的测试覆盖率
- 遵循 RESTful API 设计原则

## 部署指南

### 系统要求

- Linux 服务器
- Python 3.11+
- MySQL 8.0
- Nginx

### 部署步骤

1. **安装依赖**

```bash
pip install uv
uv sync
uv pip install gunicorn
```

2. **配置 Gunicorn 服务**

创建服务文件 `/etc/systemd/system/box-backend.service`：

```ini
[Unit]
Description=Box Backend
After=network.target

[Service]
User=www-data
WorkingDirectory=/path/to/backend
ExecStart=uv run gunicorn -w 4 -b 127.0.0.1:5000 run:app

[Install]
WantedBy=multi-user.target
```

3. **配置 Nginx**

```nginx
server {
    listen 80;
    server_name api.example.com;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

4. **启动服务**

```bash
sudo systemctl start box-backend
sudo systemctl enable box-backend
sudo systemctl restart nginx
```

## 技术栈

- Python 3.11+
- Flask 2.x
- SQLAlchemy 2.x
- MySQL 8.0
- JWT 认证
- pytest 测试框架

# 1. 创建项目和文件模板
project = Project(
    name="dawn",
    description="Dawn Project",
    service_config_id=1,
    status=1,  # enabled
    form_schema={
        "email": {"label": "邮箱", "type": "email"},
        "password": {"label": "密码", "type": "password"},
        "proxy": {"label": "代理", "type": "ip"}
    }
)
db.session.add(project)
db.session.commit()

project_file1 = ProjectFile(
    name="dawn/farm.txt",
    content="{{email}}:{{password}}",
    project_id=project.id
)

project_file2 = ProjectFile(
    name="dawn/proxy.txt",
    content="{{proxy}}",
    project_id=project.id
)
db.session.add(project_file1)
db.session.add(project_file2)
db.session.commit()

# 2. 创建设备项目关联并设置数据
device_project = DeviceProject(
    device_id=1,
    project_id=project.id,
    state="created",
    data={
        "email": "<EMAIL>",
        "password": "xxx",
        "proxy": "127.0.0.1"
    }
)
db.session.add(device_project)
db.session.commit()

# 3. 更新设备的服务配置
device_project.update_device_service_config()

# 4. 设备的 service_configs 将被更新为：
{
    "dawn": {
        "docker_compose": "原始的 docker-compose 内容",
        "env": {"原始的环境变量"},
        "configs": {
            "dawn/farm.txt": "<EMAIL>:xxx",
            "dawn/proxy.txt": "127.0.0.1"
        }
    }
}