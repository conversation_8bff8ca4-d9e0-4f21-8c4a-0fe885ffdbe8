name: Test

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.11"]

    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install uv
      run: |
        curl -LsSf https://astral.sh/uv/install.sh | sh
    
    - name: Run tests
      run: uv run pytest
    
    - name: Trigger webhook
      if: github.ref == 'refs/heads/master' && github.event_name == 'push'
      run: |
        curl --header "Content-Type: application/json" \
             --request POST \
             --data "{}" \
             http://flow-openapi.aliyun.com/pipeline/webhook/W2ibOdlWjI60psH3kosY 