# Chinese translations for your application.
# Copyright (C) 2023 ORGANIZATION
# This file is distributed under the same license as the project.
msgid ""
msgstr ""
"Project-Id-Version:  1.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-08-01 12:00+0000\n"
"PO-Revision-Date: 2025-04-06 22:57+0800\n"
"Last-Translator: AI Assistant\n"
"Language: zh\n"
"Language-Team: zh <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

msgid "资源不存在"
msgstr "资源不存在"

msgid "消息删除成功"
msgstr "消息删除成功"

msgid "用户信息更新成功"
msgstr "用户信息更新成功"

msgid "验证成功"
msgstr "验证成功"

#, python-brace-format
msgid "使用默认locale: {default_locale}"
msgstr "使用默认locale: {default_locale}"

msgid "缺少更新数据"
msgstr "缺少更新数据"

msgid "设备授权成功"
msgstr "设备授权成功"

msgid "消息标记为已读"
msgstr "消息标记为已读"

msgid "消息发送成功"
msgstr "消息发送成功"

msgid "该任务已完成"
msgstr "该任务已完成"

msgid "登录成功"
msgstr "登录成功"

msgid "检测到你尚未加入服务器，请先加入我们的 Discord！"
msgstr ""

msgid "没有找到匹配的设备"
msgstr "没有找到匹配的设备"

msgid "该用户已被邀请"
msgstr "该用户已被邀请"

msgid "注册失败，请稍后重试"
msgstr "注册失败，请稍后重试"

msgid "获取设备项目服务配置失败"
msgstr "获取设备项目服务配置失败"

msgid "项目配置不存在"
msgstr "项目配置不存在"

#, python-brace-format
msgid "获取用户信息失败: {user_resp.text}"
msgstr ""

msgid "该项目不允许配置代理"
msgstr "该项目不允许配置代理"

msgid "获取消息详情失败"
msgstr "获取消息详情失败"

msgid "获取服务配置失败"
msgstr "获取服务配置失败"

msgid "服务停止：连续两个检测周期未收到运行指标"
msgstr "服务停止：连续两个检测周期未收到运行指标"

msgid "设备项目不存在"
msgstr "设备项目不存在"

#, python-brace-format
msgid "成功授权 {len(associations)} 个设备"
msgstr "成功授权 {len(associations)} 个设备"

msgid "文件名已存在"
msgstr "文件名已存在"

msgid "获取验证码图像错误"
msgstr "获取验证码图像错误"

msgid "没有从 Discord 返回有效的 code 参数。"
msgstr ""

msgid "项目配置已存在"
msgstr "项目配置已存在"

msgid "输入验证错误"
msgstr "输入验证错误"

msgid "注册成功"
msgstr "注册成功"

msgid "无效的认证令牌"
msgstr "无效的认证令牌"

msgid "项目不存在"
msgstr "项目不存在"

msgid "等待服务启动：配置已完成但尚未收到运行指标"
msgstr "等待服务启动：配置已完成但尚未收到运行指标"

msgid "缺少验证码"
msgstr "缺少验证码"

msgid "获取设备列表失败"
msgstr "获取设备列表失败"

msgid "项目状态与指标数据不一致"
msgstr "项目状态与指标数据不一致"

msgid "项目正在被设备使用，无法删除"
msgstr "项目正在被设备使用，无法删除"

msgid "设备不存在"
msgstr "设备不存在"

msgid "邮箱验证码"
msgstr "邮箱验证码"

msgid "用户名已存在"
msgstr "用户名已存在"

msgid "设备有正在初始化的项目服务"
msgstr "设备有正在初始化的项目服务"

msgid "设备从未上报过运行指标"
msgstr "设备从未上报过运行指标"

msgid "获取设备状态统计失败"
msgstr "获取设备状态统计失败"

msgid "已翻译消息: "
msgstr "已翻译消息: "

msgid "获取用户资料失败"
msgstr "获取用户资料失败"

msgid "设备 mac 地址已存在"
msgstr "设备 mac 地址已存在"

msgid "需要管理员权限"
msgstr "需要管理员权限"

msgid "密码错误"
msgstr "密码错误"

msgid "token错误"
msgstr "token错误"

msgid "设备创建成功"
msgstr "设备创建成功"

msgid "发送消息失败"
msgstr "发送消息失败"

msgid "密码修改成功"
msgstr "密码修改成功"

msgid "修改密码失败"
msgstr "修改密码失败"

msgid "获取消息列表失败"
msgstr "获取消息列表失败"

#, python-brace-format
msgid "无效的代币code: {token_code}"
msgstr ""

#, python-brace-format
msgid "状态分析失败: {str(e)}"
msgstr "状态分析失败: {str(e)}"

msgid "设备有正在运行的项目服务"
msgstr "设备有正在运行的项目服务"

msgid "设备代理未配置"
msgstr "设备代理未配置"

#, python-brace-format
msgid "设备已离线超过{offline_days}天"
msgstr "设备已离线超过{offline_days}天"

msgid "查询失败"
msgstr ""

msgid "邮箱已存在"
msgstr "邮箱已存在"

msgid "设备项目配置不存在"
msgstr "设备项目配置不存在"

msgid "设备删除成功"
msgstr "设备删除成功"

#, python-brace-format
msgid "无效的链代码: {chain_code}"
msgstr ""

msgid "项目服务正常运行中"
msgstr "项目服务正常运行中"

msgid "获取设备令牌失败"
msgstr "获取设备令牌失败"

#, python-brace-format
msgid "获取 Discord access_token 失败: {token_resp.text}"
msgstr ""

msgid "缺少必要参数"
msgstr "缺少必要参数"

msgid "没有提供更新数据"
msgstr "没有提供更新数据"

msgid "项目尚未完成初始化配置"
msgstr "项目尚未完成初始化配置"

msgid "获取用户列表失败，请稍后重试"
msgstr "获取用户列表失败，请稍后重试"

msgid "服务器内部错误"
msgstr "服务器内部错误"

msgid "无效的请求数据"
msgstr "无效的请求数据"

msgid "无效的令牌"
msgstr "无效的令牌"

msgid "操作失败"
msgstr "操作失败"

msgid "密钥必须是32字节"
msgstr "密钥必须是32字节"

msgid "项目正在被设备使用，无法禁用"
msgstr "项目正在被设备使用，无法禁用"

msgid "操作成功"
msgstr "操作成功"

msgid "获取概览数据失败"
msgstr "获取概览数据失败"

msgid "项目名称已被使用"
msgstr "项目名称已被使用"

msgid "登录失败，请稍后重试"
msgstr "登录失败，请稍后重试"

msgid "项目配置删除成功"
msgstr "项目配置删除成功"

msgid "更新已接收，但未处理"
msgstr ""

msgid "生成邀请链接失败"
msgstr ""

msgid "文件不存在"
msgstr "文件不存在"

msgid "base64编码的nonce"
msgstr "base64编码的nonce"

msgid "更新用户信息失败，请稍后重试"
msgstr "更新用户信息失败，请稍后重试"

msgid "删除设备失败"
msgstr "删除设备失败"

#, python-brace-format
msgid "发送失败: {str(e)}"
msgstr "发送失败: {str(e)}"

msgid "任务类型不存在"
msgstr "任务类型不存在"

msgid "项目创建成功"
msgstr "项目创建成功"

msgid "所有项目服务停止且存在未解决的错误状态"
msgstr "所有项目服务停止且存在未解决的错误状态"

msgid "权限不足"
msgstr "权限不足"

msgid "service_compose 必须包含 services 字段"
msgstr "service_compose 必须包含 services 字段"

msgid "service_compose 必须是一个对象"
msgstr "service_compose 必须是一个对象"

msgid "缺少认证令牌"
msgstr "缺少认证令牌"

msgid "没有权限访问该项目"
msgstr "没有权限访问该项目"

msgid "令牌已被撤销"
msgstr "令牌已被撤销"

msgid "生成服务配置失败"
msgstr "生成服务配置失败"

msgid "所有消息已标记为已读"
msgstr "所有消息已标记为已读"

msgid "密钥未设置"
msgstr ""

msgid "缺少必要字段：msg_type, subject, content"
msgstr "缺少必要字段：msg_type, subject, content"

msgid "消息不存在"
msgstr "消息不存在"

msgid "设备项目不属于该项目"
msgstr "设备项目不属于该项目"

#, python-brace-format
msgid "从Accept-Language获取locale: {locale}"
msgstr "从Accept-Language获取locale: {locale}"

#, python-brace-format
msgid "从查询参数获取locale: {locale}"
msgstr "从查询参数获取locale: {locale}"

msgid "删除消息失败"
msgstr "删除消息失败"

msgid "解密失败"
msgstr "解密失败"

msgid "请勿重复创建钱包"
msgstr "請勿重複創建錢包"

#, python-brace-format
msgid "JSON解析错误: {str(e)}"
msgstr "JSON解析错误: {str(e)}"

#, python-brace-format
msgid "翻译响应消息时出错: {str(e)}"
msgstr "翻译响应消息时出错: {str(e)}"

msgid "钱包创建失败"
msgstr "錢包創建失敗"

msgid "重新生成系统应用配置失败"
msgstr "重新生成系统应用配置失败"

msgid "设备未分配服务配置"
msgstr "设备未分配服务配置"

msgid "设备未配置系统应用"
msgstr "设备未配置系统应用"

msgid "项目未被删除"
msgstr "项目未被删除"

msgid "缺少必要字段"
msgstr "缺少必要字段"

msgid "系统应用配置重新生成成功"
msgstr "系统应用配置重新生成成功"

#, python-brace-format
msgid "成功授权 {count} 个设备"
msgstr "成功授权 {count} 个设备"

msgid "项目代理未配置"
msgstr "项目代理未配置"

msgid "邮箱格式错误"
msgstr "邮箱格式错误"

msgid "设备更新成功"
msgstr "设备更新成功"

msgid "未能获取到 access_token"
msgstr ""

msgid "base64编码的加密MAC地址"
msgstr "base64编码的加密MAC地址"

msgid "项目更新成功"
msgstr "项目更新成功"

msgid "获取设备详情失败"
msgstr "获取设备详情失败"

msgid "邀请记录不存在"
msgstr "邀请记录不存在"

msgid "更新设备失败"
msgstr "更新设备失败"

msgid "无效的 MAC 地址"
msgstr "无效的 MAC 地址"

msgid "服务配置不存在"
msgstr "服务配置不存在"

msgid "缺少用户ID"
msgstr "缺少用户ID"

msgid "无法找到用户"
msgstr "无法找到用户"

msgid "用户不存在"
msgstr "用户不存在"

msgid "所有的项目都是 created 状态"
msgstr "所有的项目都是 created 状态"

msgid "该用户已被授权访问此设备"
msgstr "该用户已被授权访问此设备"

msgid "生成 docker-compose 配置失败"
msgstr "生成 docker-compose 配置失败"

msgid "需要新的认证令牌"
msgstr "需要新的认证令牌"

msgid "创建设备失败"
msgstr "创建设备失败"

msgid "验证码错误或已过期"
msgstr "验证码错误或已过期"

msgid "验证码发送成功"
msgstr "验证码发送成功"

msgid "名称和 docker-compose 配置为必填项"
msgstr "名称和 docker-compose 配置为必填项"

#, python-brace-format
msgid "数据验证失败: {e.message}"
msgstr "数据验证失败: {e.message}"

msgid "未登录或token已过期"
msgstr "未登录或token已过期"

msgid "没有提供标签"
msgstr "没有提供标签"

msgid "邀请积分已更新"
msgstr "邀请积分已更新"

msgid "该名称已存在"
msgstr "该名称已存在"

msgid "令牌已过期"
msgstr "令牌已过期"

msgid "项目名称已存在"
msgstr "项目名称已存在"

msgid "设备未注册"
msgstr "设备未注册"

msgid "项目删除成功"
msgstr "项目删除成功"

msgid "服务正常"
msgstr "服务正常"

msgid "配置错误"
msgstr "配置错误"

msgid "代理不可用"
msgstr "代理不可用"

msgid "服务内部错误"
msgstr "服务内部错误"

msgid "服务暂时不可用"
msgstr "服务暂时不可用"

msgid "未知状态"
msgstr "未知状态"