# Italian translations for your application.
# Copyright (C) 2023 ORGANIZATION
# This file is distributed under the same license as the project.
msgid ""
msgstr ""
"Project-Id-Version:  1.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-08-01 12:00+0000\n"
"PO-Revision-Date: 2025-04-07 01:15+0800\n"
"Last-Translator: AI Assistant\n"
"Language: it\n"
"Language-Team: it <<EMAIL>>\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

msgid "资源不存在"
msgstr "La risorsa non esiste"

msgid "消息删除成功"
msgstr "Messaggio eliminato con successo"

msgid "用户信息更新成功"
msgstr "Informazioni utente aggiornate con successo"

msgid "验证成功"
msgstr "Verifica completata con successo"

#, python-brace-format
msgid "使用默认locale: {default_locale}"
msgstr "Utilizzo della lingua predefinita: {default_locale}"

msgid "缺少更新数据"
msgstr "Dati di aggiornamento mancanti"

msgid "设备授权成功"
msgstr "Dispositivo autorizzato con successo"

msgid "消息标记为已读"
msgstr "Messaggio contrassegnato come letto"

msgid "消息发送成功"
msgstr "Messaggio inviato con successo"

msgid "该任务已完成"
msgstr "Questa attività è già completata"

msgid "登录成功"
msgstr "Accesso effettuato con successo"

msgid "检测到你尚未加入服务器，请先加入我们的 Discord！"
msgstr "Abbiamo rilevato che non hai ancora aderito al server, per favore unisciti prima al nostro Discord!"

msgid "没有找到匹配的设备"
msgstr "Nessun dispositivo corrispondente trovato"

msgid "该用户已被邀请"
msgstr "Questo utente è già stato invitato"

msgid "注册失败，请稍后重试"
msgstr "Registrazione fallita, riprova più tardi"

msgid "获取设备项目服务配置失败"
msgstr "Impossibile ottenere la configurazione del servizio del progetto del dispositivo"

msgid "项目配置不存在"
msgstr "La configurazione del progetto non esiste"

#, python-brace-format
msgid "获取用户信息失败: {user_resp.text}"
msgstr "Impossibile ottenere le informazioni dell'utente: {user_resp.text}"

msgid "该项目不允许配置代理"
msgstr "Questo progetto non consente la configurazione del proxy"

msgid "获取消息详情失败"
msgstr "Impossibile ottenere i dettagli del messaggio"

msgid "获取服务配置失败"
msgstr "Impossibile ottenere la configurazione del servizio"

msgid "服务停止：连续两个检测周期未收到运行指标"
msgstr "Servizio interrotto: Nessuna metrica operativa ricevuta per due cicli di rilevamento consecutivi"

msgid "设备项目不存在"
msgstr "Il progetto del dispositivo non esiste"

#, python-brace-format
msgid "成功授权 {len(associations)} 个设备"
msgstr "Autorizzazione riuscita di {len(associations)} dispositivi"

msgid "文件名已存在"
msgstr "Il nome del file esiste già"

msgid "获取验证码图像错误"
msgstr "Errore nell'ottenere l'immagine del codice di verifica"

msgid "没有从 Discord 返回有效的 code 参数。"
msgstr "Nessun parametro di codice valido restituito da Discord."

msgid "项目配置已存在"
msgstr "La configurazione del progetto esiste già"

msgid "输入验证错误"
msgstr "Errore di convalida dell'input"

msgid "注册成功"
msgstr "Registrazione completata con successo"

msgid "无效的认证令牌"
msgstr "Token di autenticazione non valido"

msgid "项目不存在"
msgstr "Il progetto non esiste"

msgid "等待服务启动：配置已完成但尚未收到运行指标"
msgstr "In attesa dell'avvio del servizio: La configurazione è completa ma non sono ancora state ricevute metriche operative"

msgid "缺少验证码"
msgstr "Codice di verifica mancante"

msgid "获取设备列表失败"
msgstr "Impossibile ottenere l'elenco dei dispositivi"

msgid "项目状态与指标数据不一致"
msgstr "Lo stato del progetto è incoerente con i dati delle metriche"

msgid "项目正在被设备使用，无法删除"
msgstr "Il progetto è in uso da dispositivi e non può essere eliminato"

msgid "设备不存在"
msgstr "Il dispositivo non esiste"

msgid "邮箱验证码"
msgstr "Codice di verifica email"

msgid "用户名已存在"
msgstr "Il nome utente esiste già"

msgid "设备有正在初始化的项目服务"
msgstr "Il dispositivo ha servizi di progetto in fase di inizializzazione"

msgid "设备从未上报过运行指标"
msgstr "Il dispositivo non ha mai riportato metriche operative"

msgid "获取设备状态统计失败"
msgstr "Impossibile ottenere le statistiche sullo stato del dispositivo"

msgid "已翻译消息: "
msgstr "Messaggio tradotto: "

msgid "获取用户资料失败"
msgstr "Impossibile ottenere il profilo utente"

msgid "设备 mac 地址已存在"
msgstr "L'indirizzo MAC del dispositivo esiste già"

msgid "需要管理员权限"
msgstr "Sono richiesti i permessi di amministratore"

msgid "密码错误"
msgstr "Password errata"

msgid "token错误"
msgstr ""

msgid "设备创建成功"
msgstr "Dispositivo creato con successo"

msgid "发送消息失败"
msgstr "Invio del messaggio fallito"

msgid "密码修改成功"
msgstr "Password modificata con successo"

msgid "修改密码失败"
msgstr "Modifica della password fallita"

msgid "获取消息列表失败"
msgstr "Impossibile ottenere l'elenco dei messaggi"

#, python-brace-format
msgid "无效的代币code: {token_code}"
msgstr "Codice token non valido: {token_code}"

#, python-brace-format
msgid "状态分析失败: {str(e)}"
msgstr "Analisi dello stato fallita: {str(e)}"

msgid "设备有正在运行的项目服务"
msgstr "Il dispositivo ha servizi di progetto in esecuzione"

msgid "设备代理未配置"
msgstr "Proxy del dispositivo non configurato"

#, python-brace-format
msgid "设备已离线超过{offline_days}天"
msgstr "Il dispositivo è offline da più di {offline_days} giorni"

msgid "查询失败"
msgstr "Query fallita"

msgid "邮箱已存在"
msgstr "L'email esiste già"

msgid "设备项目配置不存在"
msgstr "La configurazione del progetto del dispositivo non esiste"

msgid "设备删除成功"
msgstr "Dispositivo eliminato con successo"

#, python-brace-format
msgid "无效的链代码: {chain_code}"
msgstr "Codice di catena non valido: {chain_code}"

msgid "项目服务正常运行中"
msgstr "Il servizio del progetto funziona normalmente"

msgid "获取设备令牌失败"
msgstr "Impossibile ottenere il token del dispositivo"

#, python-brace-format
msgid "获取 Discord access_token 失败: {token_resp.text}"
msgstr "Impossibile ottenere il token di accesso Discord: {token_resp.text}"

msgid "缺少必要参数"
msgstr "Parametri necessari mancanti"

msgid "没有提供更新数据"
msgstr "Nessun dato di aggiornamento fornito"

msgid "项目尚未完成初始化配置"
msgstr "La configurazione di inizializzazione del progetto non è completa"

msgid "获取用户列表失败，请稍后重试"
msgstr "Impossibile ottenere l'elenco degli utenti, riprova più tardi"

msgid "服务器内部错误"
msgstr "Errore interno del server"

msgid "无效的请求数据"
msgstr "Dati della richiesta non validi"

msgid "无效的令牌"
msgstr "Token non valido"

msgid "操作失败"
msgstr "Operazione fallita"

msgid "密钥必须是32字节"
msgstr "La chiave deve essere di 32 byte"

msgid "项目正在被设备使用，无法禁用"
msgstr "Il progetto è in uso da dispositivi e non può essere disabilitato"

msgid "操作成功"
msgstr "Operazione riuscita"

msgid "获取概览数据失败"
msgstr "Impossibile ottenere i dati di panoramica"

msgid "项目名称已被使用"
msgstr "Il nome del progetto è già in uso"

msgid "登录失败，请稍后重试"
msgstr "Accesso fallito, riprova più tardi"

msgid "项目配置删除成功"
msgstr "Configurazione del progetto eliminata con successo"

msgid "更新已接收，但未处理"
msgstr "Aggiornamento ricevuto, ma non elaborato"

msgid "生成邀请链接失败"
msgstr "Impossibile generare il link di invito"

msgid "文件不存在"
msgstr "Il file non esiste"

msgid "base64编码的nonce"
msgstr "Nonce codificato in base64"

msgid "更新用户信息失败，请稍后重试"
msgstr "Impossibile aggiornare le informazioni dell'utente, riprova più tardi"

msgid "删除设备失败"
msgstr "Eliminazione del dispositivo fallita"

#, python-brace-format
msgid "发送失败: {str(e)}"
msgstr "Invio fallito: {str(e)}"

msgid "任务类型不存在"
msgstr "Il tipo di attività non esiste"

msgid "项目创建成功"
msgstr "Progetto creato con successo"

msgid "所有项目服务停止且存在未解决的错误状态"
msgstr "Tutti i servizi del progetto sono interrotti e ci sono stati di errore non risolti"

msgid "权限不足"
msgstr "Permessi insufficienti"

msgid "service_compose 必须包含 services 字段"
msgstr "service_compose deve includere il campo services"

msgid "service_compose 必须是一个对象"
msgstr "service_compose deve essere un oggetto"

msgid "缺少认证令牌"
msgstr "Token di autenticazione mancante"

msgid "没有权限访问该项目"
msgstr "Nessuna autorizzazione per accedere a questo progetto"

msgid "令牌已被撤销"
msgstr "Il token è stato revocato"

msgid "生成服务配置失败"
msgstr "Impossibile generare la configurazione del servizio"

msgid "所有消息已标记为已读"
msgstr "Tutti i messaggi sono stati contrassegnati come letti"

msgid "密钥未设置"
msgstr "Chiave non impostata"

msgid "缺少必要字段：msg_type, subject, content"
msgstr "Campi necessari mancanti: msg_type, subject, content"

msgid "消息不存在"
msgstr "Il messaggio non esiste"

msgid "设备项目不属于该项目"
msgstr "Il progetto del dispositivo non appartiene a questo progetto"

#, python-brace-format
msgid "从Accept-Language获取locale: {locale}"
msgstr "Ottenimento della lingua da Accept-Language: {locale}"

#, python-brace-format
msgid "从查询参数获取locale: {locale}"
msgstr "Ottenimento della lingua dai parametri di query: {locale}"

msgid "删除消息失败"
msgstr "Eliminazione del messaggio fallita"

msgid "解密失败"
msgstr "Decrittografia fallita"

msgid "请勿重复创建钱包"
msgstr "Non creare nuovamente il portafoglio"

#, python-brace-format
msgid "JSON解析错误: {str(e)}"
msgstr "Errore di analisi JSON: {str(e)}"

#, python-brace-format
msgid "翻译响应消息时出错: {str(e)}"
msgstr "Errore durante la traduzione del messaggio di risposta: {str(e)}"

msgid "钱包创建失败"
msgstr "Creazione del portafoglio fallita"

msgid "重新生成系统应用配置失败"
msgstr "Rigenerazione della configurazione dell'applicazione di sistema fallita"

msgid "设备未分配服务配置"
msgstr "Configurazione del servizio non assegnata al dispositivo"

msgid "设备未配置系统应用"
msgstr "Il dispositivo non è configurato con l'applicazione di sistema"

msgid "项目未被删除"
msgstr "Il progetto non è stato eliminato"

msgid "缺少必要字段"
msgstr "Campi necessari mancanti"

msgid "系统应用配置重新生成成功"
msgstr "Configurazione dell'applicazione di sistema rigenerata con successo"

#, python-brace-format
msgid "成功授权 {count} 个设备"
msgstr "Autorizzazione riuscita di {count} dispositivi"

msgid "项目代理未配置"
msgstr "Proxy del progetto non configurato"

msgid "邮箱格式错误"
msgstr "Formato email errato"

msgid "设备更新成功"
msgstr "Dispositivo aggiornato con successo"

msgid "未能获取到 access_token"
msgstr "Impossibile ottenere il token di accesso"

msgid "base64编码的加密MAC地址"
msgstr "Indirizzo MAC crittografato codificato in base64"

msgid "项目更新成功"
msgstr "Progetto aggiornato con successo"

msgid "获取设备详情失败"
msgstr "Impossibile ottenere i dettagli del dispositivo"

msgid "邀请记录不存在"
msgstr "Il record di invito non esiste"

msgid "更新设备失败"
msgstr "Aggiornamento del dispositivo fallito"

msgid "无效的 MAC 地址"
msgstr "Indirizzo MAC non valido"

msgid "服务配置不存在"
msgstr "La configurazione del servizio non esiste"

msgid "缺少用户ID"
msgstr "ID utente mancante"

msgid "无法找到用户"
msgstr "Impossibile trovare l'utente"

msgid "用户不存在"
msgstr "L'utente non esiste"

msgid "所有的项目都是 created 状态"
msgstr "Tutti i progetti sono nello stato 'creato'"

msgid "该用户已被授权访问此设备"
msgstr "Questo utente è già autorizzato ad accedere a questo dispositivo"

msgid "生成 docker-compose 配置失败"
msgstr "Impossibile generare la configurazione docker-compose"

msgid "需要新的认证令牌"
msgstr "È richiesto un nuovo token di autenticazione"

msgid "创建设备失败"
msgstr "Creazione del dispositivo fallita"

msgid "验证码错误或已过期"
msgstr "Codice di verifica errato o scaduto"

msgid "验证码发送成功"
msgstr "Codice di verifica inviato con successo"

msgid "名称和 docker-compose 配置为必填项"
msgstr "Nome e configurazione docker-compose sono obbligatori"

#, python-brace-format
msgid "数据验证失败: {e.message}"
msgstr "Convalida dei dati fallita: {e.message}"

msgid "未登录或token已过期"
msgstr "Non connesso o token scaduto"

msgid "没有提供标签"
msgstr "Nessuna etichetta fornita"

msgid "邀请积分已更新"
msgstr "Punti invito aggiornati"

msgid "该名称已存在"
msgstr "Questo nome esiste già"

msgid "令牌已过期"
msgstr "Il token è scaduto"

msgid "项目名称已存在"
msgstr "Il nome del progetto esiste già"

msgid "设备未注册"
msgstr "Dispositivo non registrato"

msgid "项目删除成功"
msgstr "Progetto eliminato con successo"

