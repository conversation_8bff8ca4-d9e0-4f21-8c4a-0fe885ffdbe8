# Arabic translations for your application.
# Copyright (C) 2023 ORGANIZATION
# This file is distributed under the same license as the project.
msgid ""
msgstr ""
"Project-Id-Version:  1.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-08-01 12:00+0000\n"
"PO-Revision-Date: 2025-04-07 00:15+0800\n"
"Last-Translator: AI Assistant\n"
"Language: ar\n"
"Language-Team: ar <<EMAIL>>\n"
"Plural-Forms: nplurals=6; plural=(n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : "
"n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

msgid "资源不存在"
msgstr "المورد غير موجود"

msgid "消息删除成功"
msgstr "تم حذف الرسالة بنجاح"

msgid "用户信息更新成功"
msgstr "تم تحديث معلومات المستخدم بنجاح"

msgid "验证成功"
msgstr "تم التحقق بنجاح"

#, python-brace-format
msgid "使用默认locale: {default_locale}"
msgstr "استخدام اللغة الافتراضية: {default_locale}"

msgid "缺少更新数据"
msgstr "بيانات التحديث مفقودة"

msgid "设备授权成功"
msgstr "تم ترخيص الجهاز بنجاح"

msgid "消息标记为已读"
msgstr "تم تحديد الرسالة كمقروءة"

msgid "消息发送成功"
msgstr "تم إرسال الرسالة بنجاح"

msgid "该任务已完成"
msgstr "هذه المهمة مكتملة بالفعل"

msgid "登录成功"
msgstr "تم تسجيل الدخول بنجاح"

msgid "检测到你尚未加入服务器，请先加入我们的 Discord！"
msgstr "تم اكتشاف أنك لم تنضم إلى الخادم بعد، يرجى الانضمام إلى Discord الخاص بنا أولاً!"

msgid "没有找到匹配的设备"
msgstr "لم يتم العثور على أجهزة مطابقة"

msgid "该用户已被邀请"
msgstr "تمت دعوة هذا المستخدم بالفعل"

msgid "注册失败，请稍后重试"
msgstr "فشل التسجيل، يرجى المحاولة لاحقًا"

msgid "获取设备项目服务配置失败"
msgstr "فشل الحصول على تكوين خدمة مشروع الجهاز"

msgid "项目配置不存在"
msgstr "تكوين المشروع غير موجود"

#, python-brace-format
msgid "获取用户信息失败: {user_resp.text}"
msgstr "فشل الحصول على معلومات المستخدم: {user_resp.text}"

msgid "该项目不允许配置代理"
msgstr "هذا المشروع لا يسمح بتكوين الوكيل"

msgid "获取消息详情失败"
msgstr "فشل الحصول على تفاصيل الرسالة"

msgid "获取服务配置失败"
msgstr "فشل الحصول على تكوين الخدمة"

msgid "服务停止：连续两个检测周期未收到运行指标"
msgstr "توقفت الخدمة: لم يتم تلقي مقاييس التشغيل لدورتي اكتشاف متتاليتين"

msgid "设备项目不存在"
msgstr "مشروع الجهاز غير موجود"

#, python-brace-format
msgid "成功授权 {len(associations)} 个设备"
msgstr "تم ترخيص {len(associations)} أجهزة بنجاح"

msgid "文件名已存在"
msgstr "اسم الملف موجود بالفعل"

msgid "获取验证码图像错误"
msgstr "خطأ في الحصول على صورة رمز التحقق"

msgid "没有从 Discord 返回有效的 code 参数。"
msgstr "لم يتم إرجاع معلمة رمز صالحة من Discord."

msgid "项目配置已存在"
msgstr "تكوين المشروع موجود بالفعل"

msgid "输入验证错误"
msgstr "خطأ في التحقق من المدخلات"

msgid "注册成功"
msgstr "تم التسجيل بنجاح"

msgid "无效的认证令牌"
msgstr "رمز المصادقة غير صالح"

msgid "项目不存在"
msgstr "المشروع غير موجود"

msgid "等待服务启动：配置已完成但尚未收到运行指标"
msgstr "انتظار بدء الخدمة: اكتمل التكوين ولكن لم يتم تلقي مقاييس التشغيل بعد"

msgid "缺少验证码"
msgstr "رمز التحقق مفقود"

msgid "获取设备列表失败"
msgstr "فشل الحصول على قائمة الأجهزة"

msgid "项目状态与指标数据不一致"
msgstr "حالة المشروع غير متسقة مع بيانات المقاييس"

msgid "项目正在被设备使用，无法删除"
msgstr "المشروع قيد الاستخدام بواسطة الأجهزة ولا يمكن حذفه"

msgid "设备不存在"
msgstr "الجهاز غير موجود"

msgid "邮箱验证码"
msgstr "رمز التحقق من البريد الإلكتروني"

msgid "用户名已存在"
msgstr "اسم المستخدم موجود بالفعل"

msgid "设备有正在初始化的项目服务"
msgstr "الجهاز لديه خدمات مشروع قيد التهيئة"

msgid "设备从未上报过运行指标"
msgstr "لم يُبلغ الجهاز أبدًا عن مقاييس التشغيل"

msgid "获取设备状态统计失败"
msgstr "فشل الحصول على إحصائيات حالة الجهاز"

msgid "已翻译消息: "
msgstr "الرسالة المترجمة: "

msgid "获取用户资料失败"
msgstr "فشل الحصول على ملف تعريف المستخدم"

msgid "设备 mac 地址已存在"
msgstr "عنوان MAC للجهاز موجود بالفعل"

msgid "需要管理员权限"
msgstr "مطلوب صلاحيات المسؤول"

msgid "密码错误"
msgstr "كلمة مرور خاطئة"

msgid "token错误"
msgstr ""

msgid "设备创建成功"
msgstr "تم إنشاء الجهاز بنجاح"

msgid "发送消息失败"
msgstr "فشل إرسال الرسالة"

msgid "密码修改成功"
msgstr "تم تغيير كلمة المرور بنجاح"

msgid "修改密码失败"
msgstr "فشل تعديل كلمة المرور"

msgid "获取消息列表失败"
msgstr "فشل الحصول على قائمة الرسائل"

#, python-brace-format
msgid "无效的代币code: {token_code}"
msgstr "رمز العملة غير صالح: {token_code}"

#, python-brace-format
msgid "状态分析失败: {str(e)}"
msgstr "فشل تحليل الحالة: {str(e)}"

msgid "设备有正在运行的项目服务"
msgstr "الجهاز لديه خدمات مشروع قيد التشغيل"

msgid "设备代理未配置"
msgstr "وكيل الجهاز غير مُكوّن"

#, python-brace-format
msgid "设备已离线超过{offline_days}天"
msgstr "الجهاز غير متصل منذ أكثر من {offline_days} يوم"

msgid "查询失败"
msgstr "فشل الاستعلام"

msgid "邮箱已存在"
msgstr "البريد الإلكتروني موجود بالفعل"

msgid "设备项目配置不存在"
msgstr "تكوين مشروع الجهاز غير موجود"

msgid "设备删除成功"
msgstr "تم حذف الجهاز بنجاح"

#, python-brace-format
msgid "无效的链代码: {chain_code}"
msgstr "رمز السلسلة غير صالح: {chain_code}"

msgid "项目服务正常运行中"
msgstr "خدمة المشروع تعمل بشكل طبيعي"

msgid "获取设备令牌失败"
msgstr "فشل الحصول على رمز الجهاز"

#, python-brace-format
msgid "获取 Discord access_token 失败: {token_resp.text}"
msgstr "فشل الحصول على رمز وصول Discord: {token_resp.text}"

msgid "缺少必要参数"
msgstr "المعلمات المطلوبة مفقودة"

msgid "没有提供更新数据"
msgstr "لم يتم تقديم بيانات تحديث"

msgid "项目尚未完成初始化配置"
msgstr "لم يكتمل تكوين تهيئة المشروع"

msgid "获取用户列表失败，请稍后重试"
msgstr "فشل الحصول على قائمة المستخدمين، يرجى المحاولة لاحقًا"

msgid "服务器内部错误"
msgstr "خطأ داخلي في الخادم"

msgid "无效的请求数据"
msgstr "بيانات الطلب غير صالحة"

msgid "无效的令牌"
msgstr "رمز غير صالح"

msgid "操作失败"
msgstr "فشلت العملية"

msgid "密钥必须是32字节"
msgstr "يجب أن يكون المفتاح 32 بايت"

msgid "项目正在被设备使用，无法禁用"
msgstr "المشروع قيد الاستخدام بواسطة الأجهزة ولا يمكن تعطيله"

msgid "操作成功"
msgstr "تمت العملية بنجاح"

msgid "获取概览数据失败"
msgstr "فشل الحصول على بيانات النظرة العامة"

msgid "项目名称已被使用"
msgstr "اسم المشروع قيد الاستخدام بالفعل"

msgid "登录失败，请稍后重试"
msgstr "فشل تسجيل الدخول، يرجى المحاولة لاحقًا"

msgid "项目配置删除成功"
msgstr "تم حذف تكوين المشروع بنجاح"

msgid "更新已接收，但未处理"
msgstr "تم استلام التحديث، ولكن لم تتم معالجته"

msgid "生成邀请链接失败"
msgstr "فشل إنشاء رابط الدعوة"

msgid "文件不存在"
msgstr "الملف غير موجود"

msgid "base64编码的nonce"
msgstr "رمز مشفر بتقنية base64"

msgid "更新用户信息失败，请稍后重试"
msgstr "فشل تحديث معلومات المستخدم، يرجى المحاولة لاحقًا"

msgid "删除设备失败"
msgstr "فشل حذف الجهاز"

#, python-brace-format
msgid "发送失败: {str(e)}"
msgstr "فشل الإرسال: {str(e)}"

msgid "任务类型不存在"
msgstr "نوع المهمة غير موجود"

msgid "项目创建成功"
msgstr "تم إنشاء المشروع بنجاح"

msgid "所有项目服务停止且存在未解决的错误状态"
msgstr "توقفت جميع خدمات المشروع وتوجد حالات خطأ غير محلولة"

msgid "权限不足"
msgstr "صلاحيات غير كافية"

msgid "service_compose 必须包含 services 字段"
msgstr "يجب أن يتضمن service_compose حقل services"

msgid "service_compose 必须是一个对象"
msgstr "يجب أن يكون service_compose كائنًا"

msgid "缺少认证令牌"
msgstr "رمز المصادقة مفقود"

msgid "没有权限访问该项目"
msgstr "لا توجد صلاحية للوصول إلى هذا المشروع"

msgid "令牌已被撤销"
msgstr "تم إلغاء الرمز"

msgid "生成服务配置失败"
msgstr "فشل إنشاء تكوين الخدمة"

msgid "所有消息已标记为已读"
msgstr "تم تحديد جميع الرسائل كمقروءة"

msgid "密钥未设置"
msgstr "لم يتم تعيين المفتاح"

msgid "缺少必要字段：msg_type, subject, content"
msgstr "حقول مطلوبة مفقودة: msg_type, subject, content"

msgid "消息不存在"
msgstr "الرسالة غير موجودة"

msgid "设备项目不属于该项目"
msgstr "مشروع الجهاز لا ينتمي إلى هذا المشروع"

#, python-brace-format
msgid "从Accept-Language获取locale: {locale}"
msgstr "الحصول على اللغة من Accept-Language: {locale}"

#, python-brace-format
msgid "从查询参数获取locale: {locale}"
msgstr "الحصول على اللغة من معلمات الاستعلام: {locale}"

msgid "删除消息失败"
msgstr "فشل حذف الرسالة"

msgid "解密失败"
msgstr "فشل فك التشفير"

msgid "请勿重复创建钱包"
msgstr "لا تقم بإنشاء المحفظة مرة أخرى"

#, python-brace-format
msgid "JSON解析错误: {str(e)}"
msgstr "خطأ في تحليل JSON: {str(e)}"

#, python-brace-format
msgid "翻译响应消息时出错: {str(e)}"
msgstr "خطأ أثناء ترجمة رسالة الاستجابة: {str(e)}"

msgid "钱包创建失败"
msgstr "فشل إنشاء المحفظة"

msgid "重新生成系统应用配置失败"
msgstr "فشل إعادة إنشاء تكوين تطبيق النظام"

msgid "设备未分配服务配置"
msgstr "لم يتم تعيين تكوين الخدمة للجهاز"

msgid "设备未配置系统应用"
msgstr "الجهاز غير مُكوّن بتطبيق النظام"

msgid "项目未被删除"
msgstr "لم يتم حذف المشروع"

msgid "缺少必要字段"
msgstr "حقول ضرورية مفقودة"

msgid "系统应用配置重新生成成功"
msgstr "تم إعادة إنشاء تكوين تطبيق النظام بنجاح"

#, python-brace-format
msgid "成功授权 {count} 个设备"
msgstr "تم ترخيص {count} أجهزة بنجاح"

msgid "项目代理未配置"
msgstr "وكيل المشروع غير مُكوّن"

msgid "邮箱格式错误"
msgstr "تنسيق البريد الإلكتروني غير صحيح"

msgid "设备更新成功"
msgstr "تم تحديث الجهاز بنجاح"

msgid "未能获取到 access_token"
msgstr "تعذر الحصول على رمز الوصول"

msgid "base64编码的加密MAC地址"
msgstr "عنوان MAC مشفر بترميز base64"

msgid "项目更新成功"
msgstr "تم تحديث المشروع بنجاح"

msgid "获取设备详情失败"
msgstr "فشل الحصول على تفاصيل الجهاز"

msgid "邀请记录不存在"
msgstr "سجل الدعوة غير موجود"

msgid "更新设备失败"
msgstr "فشل تحديث الجهاز"

msgid "无效的 MAC 地址"
msgstr "عنوان MAC غير صالح"

msgid "服务配置不存在"
msgstr "تكوين الخدمة غير موجود"

msgid "缺少用户ID"
msgstr "معرف المستخدم مفقود"

msgid "无法找到用户"
msgstr "تعذر العثور على المستخدم"

msgid "用户不存在"
msgstr "المستخدم غير موجود"

msgid "所有的项目都是 created 状态"
msgstr "جميع المشاريع في حالة 'تم إنشاؤها'"

msgid "该用户已被授权访问此设备"
msgstr "تم ترخيص هذا المستخدم بالفعل للوصول إلى هذا الجهاز"

msgid "生成 docker-compose 配置失败"
msgstr "فشل إنشاء تكوين docker-compose"

msgid "需要新的认证令牌"
msgstr "مطلوب رمز مصادقة جديد"

msgid "创建设备失败"
msgstr "فشل إنشاء الجهاز"

msgid "验证码错误或已过期"
msgstr "رمز التحقق غير صحيح أو منتهي الصلاحية"

msgid "验证码发送成功"
msgstr "تم إرسال رمز التحقق بنجاح"

msgid "名称和 docker-compose 配置为必填项"
msgstr "الاسم وتكوين docker-compose إلزاميان"

#, python-brace-format
msgid "数据验证失败: {e.message}"
msgstr "فشل التحقق من البيانات: {e.message}"

msgid "未登录或token已过期"
msgstr "لم يتم تسجيل الدخول أو انتهت صلاحية الرمز"

msgid "没有提供标签"
msgstr "لم يتم تقديم أي علامات"

msgid "邀请积分已更新"
msgstr "تم تحديث نقاط الدعوة"

msgid "该名称已存在"
msgstr "هذا الاسم موجود بالفعل"

msgid "令牌已过期"
msgstr "انتهت صلاحية الرمز"

msgid "项目名称已存在"
msgstr "اسم المشروع موجود بالفعل"

msgid "设备未注册"
msgstr "الجهاز غير مسجل"

msgid "项目删除成功"
msgstr "تم حذف المشروع بنجاح"

