# Russian translations for your application.
# Copyright (C) 2023 ORGANIZATION
# This file is distributed under the same license as the project.
msgid ""
msgstr ""
"Project-Id-Version:  1.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-08-01 12:00+0000\n"
"PO-Revision-Date: 2025-04-07 01:45+0800\n"
"Last-Translator: AI Assistant\n"
"Language: ru\n"
"Language-Team: ru <<EMAIL>>\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || "
"(n%100>=11 && n%100<=14)? 2 : 3);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

msgid "资源不存在"
msgstr "Ресурс не существует"

msgid "消息删除成功"
msgstr "Сообщение успешно удалено"

msgid "用户信息更新成功"
msgstr "Информация пользователя успешно обновлена"

msgid "验证成功"
msgstr "Проверка прошла успешно"

#, python-brace-format
msgid "使用默认locale: {default_locale}"
msgstr "Использование языка по умолчанию: {default_locale}"

msgid "缺少更新数据"
msgstr "Отсутствуют данные для обновления"

msgid "设备授权成功"
msgstr "Устройство успешно авторизовано"

msgid "消息标记为已读"
msgstr "Сообщение отмечено как прочитанное"

msgid "消息发送成功"
msgstr "Сообщение успешно отправлено"

msgid "该任务已完成"
msgstr "Эта задача уже выполнена"

msgid "登录成功"
msgstr "Вход выполнен успешно"

msgid "检测到你尚未加入服务器，请先加入我们的 Discord！"
msgstr "Обнаружено, что вы еще не присоединились к серверу, пожалуйста, сначала присоединитесь к нашему Discord!"

msgid "没有找到匹配的设备"
msgstr "Соответствующие устройства не найдены"

msgid "该用户已被邀请"
msgstr "Этот пользователь уже приглашен"

msgid "注册失败，请稍后重试"
msgstr "Ошибка регистрации, пожалуйста, повторите попытку позже"

msgid "获取设备项目服务配置失败"
msgstr "Не удалось получить конфигурацию службы проекта устройства"

msgid "项目配置不存在"
msgstr "Конфигурация проекта не существует"

#, python-brace-format
msgid "获取用户信息失败: {user_resp.text}"
msgstr "Не удалось получить информацию о пользователе: {user_resp.text}"

msgid "该项目不允许配置代理"
msgstr "Этот проект не разрешает настройку прокси"

msgid "获取消息详情失败"
msgstr "Не удалось получить детали сообщения"

msgid "获取服务配置失败"
msgstr "Не удалось получить конфигурацию службы"

msgid "服务停止：连续两个检测周期未收到运行指标"
msgstr "Служба остановлена: Не получены рабочие метрики в течение двух последовательных циклов обнаружения"

msgid "设备项目不存在"
msgstr "Проект устройства не существует"

#, python-brace-format
msgid "成功授权 {len(associations)} 个设备"
msgstr "Успешно авторизовано {len(associations)} устройств"

msgid "文件名已存在"
msgstr "Имя файла уже существует"

msgid "获取验证码图像错误"
msgstr "Ошибка получения изображения проверочного кода"

msgid "没有从 Discord 返回有效的 code 参数。"
msgstr "Не возвращен действительный параметр кода из Discord."

msgid "项目配置已存在"
msgstr "Конфигурация проекта уже существует"

msgid "输入验证错误"
msgstr "Ошибка проверки ввода"

msgid "注册成功"
msgstr "Регистрация успешна"

msgid "无效的认证令牌"
msgstr "Недействительный токен аутентификации"

msgid "项目不存在"
msgstr "Проект не существует"

msgid "等待服务启动：配置已完成但尚未收到运行指标"
msgstr "Ожидание запуска службы: Настройка завершена, но рабочие метрики еще не получены"

msgid "缺少验证码"
msgstr "Отсутствует проверочный код"

msgid "获取设备列表失败"
msgstr "Не удалось получить список устройств"

msgid "项目状态与指标数据不一致"
msgstr "Состояние проекта не соответствует данным метрик"

msgid "项目正在被设备使用，无法删除"
msgstr "Проект используется устройствами и не может быть удален"

msgid "设备不存在"
msgstr "Устройство не существует"

msgid "邮箱验证码"
msgstr "Код подтверждения электронной почты"

msgid "用户名已存在"
msgstr "Имя пользователя уже существует"

msgid "设备有正在初始化的项目服务"
msgstr "На устройстве есть инициализирующиеся службы проекта"

msgid "设备从未上报过运行指标"
msgstr "Устройство никогда не сообщало о рабочих метриках"

msgid "获取设备状态统计失败"
msgstr "Не удалось получить статистику состояния устройства"

msgid "已翻译消息: "
msgstr "Переведенное сообщение: "

msgid "获取用户资料失败"
msgstr "Не удалось получить профиль пользователя"

msgid "设备 mac 地址已存在"
msgstr "MAC-адрес устройства уже существует"

msgid "需要管理员权限"
msgstr "Требуются права администратора"

msgid "密码错误"
msgstr "Неверный пароль"

msgid "token错误"
msgstr ""

msgid "设备创建成功"
msgstr "Устройство успешно создано"

msgid "发送消息失败"
msgstr "Не удалось отправить сообщение"

msgid "密码修改成功"
msgstr "Пароль успешно изменен"

msgid "修改密码失败"
msgstr "Не удалось изменить пароль"

msgid "获取消息列表失败"
msgstr "Не удалось получить список сообщений"

#, python-brace-format
msgid "无效的代币code: {token_code}"
msgstr "Недействительный код токена: {token_code}"

#, python-brace-format
msgid "状态分析失败: {str(e)}"
msgstr "Анализ состояния не удался: {str(e)}"

msgid "设备有正在运行的项目服务"
msgstr "На устройстве запущены службы проекта"

msgid "设备代理未配置"
msgstr "Прокси устройства не настроен"

#, python-brace-format
msgid "设备已离线超过{offline_days}天"
msgstr "Устройство не в сети более {offline_days} дней"

msgid "查询失败"
msgstr "Запрос не удался"

msgid "邮箱已存在"
msgstr "Электронная почта уже существует"

msgid "设备项目配置不存在"
msgstr "Конфигурация проекта устройства не существует"

msgid "设备删除成功"
msgstr "Устройство успешно удалено"

#, python-brace-format
msgid "无效的链代码: {chain_code}"
msgstr "Недействительный код цепочки: {chain_code}"

msgid "项目服务正常运行中"
msgstr "Служба проекта работает нормально"

msgid "获取设备令牌失败"
msgstr "Не удалось получить токен устройства"

#, python-brace-format
msgid "获取 Discord access_token 失败: {token_resp.text}"
msgstr "Не удалось получить токен доступа Discord: {token_resp.text}"

msgid "缺少必要参数"
msgstr "Отсутствуют необходимые параметры"

msgid "没有提供更新数据"
msgstr "Не предоставлены данные для обновления"

msgid "项目尚未完成初始化配置"
msgstr "Начальная настройка проекта не завершена"

msgid "获取用户列表失败，请稍后重试"
msgstr "Не удалось получить список пользователей, повторите попытку позже"

msgid "服务器内部错误"
msgstr "Внутренняя ошибка сервера"

msgid "无效的请求数据"
msgstr "Недействительные данные запроса"

msgid "无效的令牌"
msgstr "Недействительный токен"

msgid "操作失败"
msgstr "Операция не удалась"

msgid "密钥必须是32字节"
msgstr "Ключ должен быть 32 байта"

msgid "项目正在被设备使用，无法禁用"
msgstr "Проект используется устройствами и не может быть отключен"

msgid "操作成功"
msgstr "Операция успешна"

msgid "获取概览数据失败"
msgstr "Не удалось получить обзорные данные"

msgid "项目名称已被使用"
msgstr "Имя проекта уже используется"

msgid "登录失败，请稍后重试"
msgstr "Ошибка входа, повторите попытку позже"

msgid "项目配置删除成功"
msgstr "Конфигурация проекта успешно удалена"

msgid "更新已接收，但未处理"
msgstr "Обновление получено, но не обработано"

msgid "生成邀请链接失败"
msgstr "Не удалось создать пригласительную ссылку"

msgid "文件不存在"
msgstr "Файл не существует"

msgid "base64编码的nonce"
msgstr "Nonce в кодировке base64"

msgid "更新用户信息失败，请稍后重试"
msgstr "Не удалось обновить информацию о пользователе, повторите попытку позже"

msgid "删除设备失败"
msgstr "Не удалось удалить устройство"

#, python-brace-format
msgid "发送失败: {str(e)}"
msgstr "Ошибка отправки: {str(e)}"

msgid "任务类型不存在"
msgstr "Тип задачи не существует"

msgid "项目创建成功"
msgstr "Проект успешно создан"

msgid "所有项目服务停止且存在未解决的错误状态"
msgstr "Все службы проекта остановлены и существуют нерешенные состояния ошибки"

msgid "权限不足"
msgstr "Недостаточно прав"

msgid "service_compose 必须包含 services 字段"
msgstr "service_compose должен включать поле services"

msgid "service_compose 必须是一个对象"
msgstr "service_compose должен быть объектом"

msgid "缺少认证令牌"
msgstr "Отсутствует токен аутентификации"

msgid "没有权限访问该项目"
msgstr "Нет разрешения на доступ к этому проекту"

msgid "令牌已被撤销"
msgstr "Токен отозван"

msgid "生成服务配置失败"
msgstr "Не удалось создать конфигурацию службы"

msgid "所有消息已标记为已读"
msgstr "Все сообщения отмечены как прочитанные"

msgid "密钥未设置"
msgstr "Ключ не установлен"

msgid "缺少必要字段：msg_type, subject, content"
msgstr "Отсутствуют необходимые поля: msg_type, subject, content"

msgid "消息不存在"
msgstr "Сообщение не существует"

msgid "设备项目不属于该项目"
msgstr "Проект устройства не принадлежит этому проекту"

#, python-brace-format
msgid "从Accept-Language获取locale: {locale}"
msgstr "Получение локали из Accept-Language: {locale}"

#, python-brace-format
msgid "从查询参数获取locale: {locale}"
msgstr "Получение локали из параметров запроса: {locale}"

msgid "删除消息失败"
msgstr "Не удалось удалить сообщение"

msgid "解密失败"
msgstr "Расшифровка не удалась"

msgid "请勿重复创建钱包"
msgstr "Не создавайте кошелек повторно"

#, python-brace-format
msgid "JSON解析错误: {str(e)}"
msgstr "Ошибка разбора JSON: {str(e)}"

#, python-brace-format
msgid "翻译响应消息时出错: {str(e)}"
msgstr "Ошибка при переводе ответного сообщения: {str(e)}"

msgid "钱包创建失败"
msgstr "Не удалось создать кошелек"

msgid "重新生成系统应用配置失败"
msgstr "Не удалось повторно создать конфигурацию системного приложения"

msgid "设备未分配服务配置"
msgstr "Устройству не назначена конфигурация службы"

msgid "设备未配置系统应用"
msgstr "Устройство не настроено с системным приложением"

msgid "项目未被删除"
msgstr "Проект не был удален"

msgid "缺少必要字段"
msgstr "Отсутствуют необходимые поля"

msgid "系统应用配置重新生成成功"
msgstr "Конфигурация системного приложения успешно воссоздана"

#, python-brace-format
msgid "成功授权 {count} 个设备"
msgstr "Успешно авторизовано {count} устройств"

msgid "项目代理未配置"
msgstr "Прокси проекта не настроен"

msgid "邮箱格式错误"
msgstr "Неверный формат электронной почты"

msgid "设备更新成功"
msgstr "Устройство успешно обновлено"

msgid "未能获取到 access_token"
msgstr "Не удалось получить токен доступа"

msgid "base64编码的加密MAC地址"
msgstr "Зашифрованный MAC-адрес в кодировке base64"

msgid "项目更新成功"
msgstr "Проект успешно обновлен"

msgid "获取设备详情失败"
msgstr "Не удалось получить сведения об устройстве"

msgid "邀请记录不存在"
msgstr "Запись приглашения не существует"

msgid "更新设备失败"
msgstr "Не удалось обновить устройство"

msgid "无效的 MAC 地址"
msgstr "Недействительный MAC-адрес"

msgid "服务配置不存在"
msgstr "Конфигурация службы не существует"

msgid "缺少用户ID"
msgstr "Отсутствует ID пользователя"

msgid "无法找到用户"
msgstr "Не удается найти пользователя"

msgid "用户不存在"
msgstr "Пользователь не существует"

msgid "所有的项目都是 created 状态"
msgstr "Все проекты находятся в состоянии 'создано'"

msgid "该用户已被授权访问此设备"
msgstr "Этот пользователь уже авторизован для доступа к этому устройству"

msgid "生成 docker-compose 配置失败"
msgstr "Не удалось создать конфигурацию docker-compose"

msgid "需要新的认证令牌"
msgstr "Требуется новый токен аутентификации"

msgid "创建设备失败"
msgstr "Не удалось создать устройство"

msgid "验证码错误或已过期"
msgstr "Проверочный код неверен или устарел"

msgid "验证码发送成功"
msgstr "Проверочный код успешно отправлен"

msgid "名称和 docker-compose 配置为必填项"
msgstr "Имя и конфигурация docker-compose обязательны"

#, python-brace-format
msgid "数据验证失败: {e.message}"
msgstr "Проверка данных не удалась: {e.message}"

msgid "未登录或token已过期"
msgstr "Не выполнен вход или срок действия токена истек"

msgid "没有提供标签"
msgstr "Не предоставлены метки"

msgid "邀请积分已更新"
msgstr "Баллы приглашения обновлены"

msgid "该名称已存在"
msgstr "Это имя уже существует"

msgid "令牌已过期"
msgstr "Срок действия токена истек"

msgid "项目名称已存在"
msgstr "Имя проекта уже существует"

msgid "设备未注册"
msgstr "Устройство не зарегистрировано"

msgid "项目删除成功"
msgstr "Проект успешно удален"

