# Portuguese translations for your application.
# Copyright (C) 2023 ORGANIZATION
# This file is distributed under the same license as the project.
msgid ""
msgstr ""
"Project-Id-Version:  1.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-08-01 12:00+0000\n"
"PO-Revision-Date: 2025-04-06 23:30+0800\n"
"Last-Translator: AI Assistant\n"
"Language: pt\n"
"Language-Team: pt <<EMAIL>>\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

msgid "资源不存在"
msgstr "O recurso não existe"

msgid "消息删除成功"
msgstr "Mensagem excluída com sucesso"

msgid "用户信息更新成功"
msgstr "Informações do usuário atualizadas com sucesso"

msgid "验证成功"
msgstr "Verificação bem-sucedida"

#, python-brace-format
msgid "使用默认locale: {default_locale}"
msgstr "Usando local padrão: {default_locale}"

msgid "缺少更新数据"
msgstr "Dados de atualização ausentes"

msgid "设备授权成功"
msgstr "Autorização do dispositivo bem-sucedida"

msgid "消息标记为已读"
msgstr "Mensagem marcada como lida"

msgid "消息发送成功"
msgstr "Mensagem enviada com sucesso"

msgid "该任务已完成"
msgstr "Esta tarefa já está concluída"

msgid "登录成功"
msgstr "Login bem-sucedido"

msgid "检测到你尚未加入服务器，请先加入我们的 Discord！"
msgstr "Detectamos que você ainda não se juntou ao servidor, por favor junte-se primeiro ao nosso Discord!"

msgid "没有找到匹配的设备"
msgstr "Nenhum dispositivo correspondente encontrado"

msgid "该用户已被邀请"
msgstr "Este usuário já foi convidado"

msgid "注册失败，请稍后重试"
msgstr "Falha no registro, tente novamente mais tarde"

msgid "获取设备项目服务配置失败"
msgstr "Falha ao obter a configuração do serviço do projeto do dispositivo"

msgid "项目配置不存在"
msgstr "A configuração do projeto não existe"

#, python-brace-format
msgid "获取用户信息失败: {user_resp.text}"
msgstr "Falha ao obter informações do usuário: {user_resp.text}"

msgid "该项目不允许配置代理"
msgstr "Este projeto não permite configuração de proxy"

msgid "获取消息详情失败"
msgstr "Falha ao obter detalhes da mensagem"

msgid "获取服务配置失败"
msgstr "Falha ao obter configuração de serviço"

msgid "服务停止：连续两个检测周期未收到运行指标"
msgstr "Serviço parado: Nenhuma métrica operacional recebida durante dois ciclos de detecção consecutivos"

msgid "设备项目不存在"
msgstr "O projeto do dispositivo não existe"

#, python-brace-format
msgid "成功授权 {len(associations)} 个设备"
msgstr "Autorização bem-sucedida de {len(associations)} dispositivos"

msgid "文件名已存在"
msgstr "O nome do arquivo já existe"

msgid "获取验证码图像错误"
msgstr "Erro ao obter imagem do código de verificação"

msgid "没有从 Discord 返回有效的 code 参数。"
msgstr "Nenhum parâmetro de código válido retornado do Discord."

msgid "项目配置已存在"
msgstr "A configuração do projeto já existe"

msgid "输入验证错误"
msgstr "Erro de validação de entrada"

msgid "注册成功"
msgstr "Registro bem-sucedido"

msgid "无效的认证令牌"
msgstr "Token de autenticação inválido"

msgid "项目不存在"
msgstr "O projeto não existe"

msgid "等待服务启动：配置已完成但尚未收到运行指标"
msgstr "Aguardando início do serviço: A configuração está concluída, mas as métricas operacionais ainda não foram recebidas"

msgid "缺少验证码"
msgstr "Código de verificação ausente"

msgid "获取设备列表失败"
msgstr "Falha ao obter lista de dispositivos"

msgid "项目状态与指标数据不一致"
msgstr "O estado do projeto é inconsistente com os dados de métricas"

msgid "项目正在被设备使用，无法删除"
msgstr "O projeto está sendo usado por dispositivos e não pode ser excluído"

msgid "设备不存在"
msgstr "O dispositivo não existe"

msgid "邮箱验证码"
msgstr "Código de verificação de email"

msgid "用户名已存在"
msgstr "O nome de usuário já existe"

msgid "设备有正在初始化的项目服务"
msgstr "O dispositivo tem serviços de projeto em inicialização"

msgid "设备从未上报过运行指标"
msgstr "O dispositivo nunca relatou métricas operacionais"

msgid "获取设备状态统计失败"
msgstr "Falha ao obter estatísticas de estado do dispositivo"

msgid "已翻译消息: "
msgstr "Mensagem traduzida: "

msgid "获取用户资料失败"
msgstr "Falha ao obter perfil do usuário"

msgid "设备 mac 地址已存在"
msgstr "O endereço MAC do dispositivo já existe"

msgid "需要管理员权限"
msgstr "Permissões de administrador necessárias"

msgid "密码错误"
msgstr "Senha incorreta"

msgid "token错误"
msgstr ""

msgid "设备创建成功"
msgstr "Dispositivo criado com sucesso"

msgid "发送消息失败"
msgstr "Falha ao enviar mensagem"

msgid "密码修改成功"
msgstr "Senha modificada com sucesso"

msgid "修改密码失败"
msgstr "Falha ao modificar senha"

msgid "获取消息列表失败"
msgstr "Falha ao obter lista de mensagens"

#, python-brace-format
msgid "无效的代币code: {token_code}"
msgstr "Código de token inválido: {token_code}"

#, python-brace-format
msgid "状态分析失败: {str(e)}"
msgstr "Análise de estado falhou: {str(e)}"

msgid "设备有正在运行的项目服务"
msgstr "O dispositivo tem serviços de projeto em execução"

msgid "设备代理未配置"
msgstr "Proxy do dispositivo não configurado"

#, python-brace-format
msgid "设备已离线超过{offline_days}天"
msgstr "O dispositivo está offline há mais de {offline_days} dias"

msgid "查询失败"
msgstr "Consulta falhou"

msgid "邮箱已存在"
msgstr "O email já existe"

msgid "设备项目配置不存在"
msgstr "A configuração do projeto do dispositivo não existe"

msgid "设备删除成功"
msgstr "Dispositivo excluído com sucesso"

#, python-brace-format
msgid "无效的链代码: {chain_code}"
msgstr "Código de cadeia inválido: {chain_code}"

msgid "项目服务正常运行中"
msgstr "O serviço do projeto está funcionando normalmente"

msgid "获取设备令牌失败"
msgstr "Falha ao obter token do dispositivo"

#, python-brace-format
msgid "获取 Discord access_token 失败: {token_resp.text}"
msgstr "Falha ao obter token de acesso do Discord: {token_resp.text}"

msgid "缺少必要参数"
msgstr "Parâmetros necessários ausentes"

msgid "没有提供更新数据"
msgstr "Nenhum dado de atualização fornecido"

msgid "项目尚未完成初始化配置"
msgstr "A configuração de inicialização do projeto não está concluída"

msgid "获取用户列表失败，请稍后重试"
msgstr "Falha ao obter a lista de usuários, tente novamente mais tarde"

msgid "服务器内部错误"
msgstr "Erro interno do servidor"

msgid "无效的请求数据"
msgstr "Dados de solicitação inválidos"

msgid "无效的令牌"
msgstr "Token inválido"

msgid "操作失败"
msgstr "Operação falhou"

msgid "密钥必须是32字节"
msgstr "A chave deve ter 32 bytes"

msgid "项目正在被设备使用，无法禁用"
msgstr "O projeto está sendo usado por dispositivos e não pode ser desativado"

msgid "操作成功"
msgstr "Operação bem-sucedida"

msgid "获取概览数据失败"
msgstr "Falha ao obter dados de visão geral"

msgid "项目名称已被使用"
msgstr "O nome do projeto já está em uso"

msgid "登录失败，请稍后重试"
msgstr "Falha no login, tente novamente mais tarde"

msgid "项目配置删除成功"
msgstr "Configuração do projeto excluída com sucesso"

msgid "更新已接收，但未处理"
msgstr "Atualização recebida, mas não processada"

msgid "生成邀请链接失败"
msgstr "Falha ao gerar link de convite"

msgid "文件不存在"
msgstr "O arquivo não existe"

msgid "base64编码的nonce"
msgstr "Nonce codificado em base64"

msgid "更新用户信息失败，请稍后重试"
msgstr "Falha ao atualizar informações do usuário, tente novamente mais tarde"

msgid "删除设备失败"
msgstr "Falha ao excluir dispositivo"

#, python-brace-format
msgid "发送失败: {str(e)}"
msgstr "Envio falhou: {str(e)}"

msgid "任务类型不存在"
msgstr "O tipo de tarefa não existe"

msgid "项目创建成功"
msgstr "Projeto criado com sucesso"

msgid "所有项目服务停止且存在未解决的错误状态"
msgstr "Todos os serviços de projeto estão parados e existem estados de erro não resolvidos"

msgid "权限不足"
msgstr "Permissões insuficientes"

msgid "service_compose 必须包含 services 字段"
msgstr "service_compose deve incluir o campo services"

msgid "service_compose 必须是一个对象"
msgstr "service_compose deve ser um objeto"

msgid "缺少认证令牌"
msgstr "Token de autenticação ausente"

msgid "没有权限访问该项目"
msgstr "Sem permissão para acessar este projeto"

msgid "令牌已被撤销"
msgstr "O token foi revogado"

msgid "生成服务配置失败"
msgstr "Falha ao gerar configuração de serviço"

msgid "所有消息已标记为已读"
msgstr "Todas as mensagens foram marcadas como lidas"

msgid "密钥未设置"
msgstr "Chave não configurada"

msgid "缺少必要字段：msg_type, subject, content"
msgstr "Campos obrigatórios ausentes: msg_type, subject, content"

msgid "消息不存在"
msgstr "A mensagem não existe"

msgid "设备项目不属于该项目"
msgstr "O projeto do dispositivo não pertence a este projeto"

#, python-brace-format
msgid "从Accept-Language获取locale: {locale}"
msgstr "Obtendo local a partir de Accept-Language: {locale}"

#, python-brace-format
msgid "从查询参数获取locale: {locale}"
msgstr "Obtendo local a partir de parâmetros de consulta: {locale}"

msgid "删除消息失败"
msgstr "Falha ao excluir mensagem"

msgid "解密失败"
msgstr "Falha na descriptografia"

msgid "请勿重复创建钱包"
msgstr "Não crie a carteira novamente"

#, python-brace-format
msgid "JSON解析错误: {str(e)}"
msgstr "Erro de análise JSON: {str(e)}"

#, python-brace-format
msgid "翻译响应消息时出错: {str(e)}"
msgstr "Erro ao traduzir mensagem de resposta: {str(e)}"

msgid "钱包创建失败"
msgstr "Falha ao criar carteira"

msgid "重新生成系统应用配置失败"
msgstr "Falha ao regenerar configuração do aplicativo do sistema"

msgid "设备未分配服务配置"
msgstr "Configuração de serviço não atribuída ao dispositivo"

msgid "设备未配置系统应用"
msgstr "O dispositivo não está configurado com o aplicativo do sistema"

msgid "项目未被删除"
msgstr "O projeto não foi excluído"

msgid "缺少必要字段"
msgstr "Campos necessários ausentes"

msgid "系统应用配置重新生成成功"
msgstr "Configuração do aplicativo do sistema regenerada com sucesso"

#, python-brace-format
msgid "成功授权 {count} 个设备"
msgstr "Autorização bem-sucedida de {count} dispositivos"

msgid "项目代理未配置"
msgstr "Proxy do projeto não configurado"

msgid "邮箱格式错误"
msgstr "Formato de email incorreto"

msgid "设备更新成功"
msgstr "Dispositivo atualizado com sucesso"

msgid "未能获取到 access_token"
msgstr "Não foi possível obter o token de acesso"

msgid "base64编码的加密MAC地址"
msgstr "Endereço MAC criptografado codificado em base64"

msgid "项目更新成功"
msgstr "Projeto atualizado com sucesso"

msgid "获取设备详情失败"
msgstr "Falha ao obter detalhes do dispositivo"

msgid "邀请记录不存在"
msgstr "O registro de convite não existe"

msgid "更新设备失败"
msgstr "Falha ao atualizar dispositivo"

msgid "无效的 MAC 地址"
msgstr "Endereço MAC inválido"

msgid "服务配置不存在"
msgstr "A configuração do serviço não existe"

msgid "缺少用户ID"
msgstr "ID de usuário ausente"

msgid "无法找到用户"
msgstr "Não foi possível encontrar o usuário"

msgid "用户不存在"
msgstr "O usuário não existe"

msgid "所有的项目都是 created 状态"
msgstr "Todos os projetos estão no estado 'criado'"

msgid "该用户已被授权访问此设备"
msgstr "Este usuário já está autorizado a acessar este dispositivo"

msgid "生成 docker-compose 配置失败"
msgstr "Falha ao gerar configuração docker-compose"

msgid "需要新的认证令牌"
msgstr "Novo token de autenticação necessário"

msgid "创建设备失败"
msgstr "Falha ao criar dispositivo"

msgid "验证码错误或已过期"
msgstr "Código de verificação incorreto ou expirado"

msgid "验证码发送成功"
msgstr "Código de verificação enviado com sucesso"

msgid "名称和 docker-compose 配置为必填项"
msgstr "Nome e configuração docker-compose são obrigatórios"

#, python-brace-format
msgid "数据验证失败: {e.message}"
msgstr "Validação de dados falhou: {e.message}"

msgid "未登录或token已过期"
msgstr "Não conectado ou token expirado"

msgid "没有提供标签"
msgstr "Nenhuma etiqueta fornecida"

msgid "邀请积分已更新"
msgstr "Pontos de convite atualizados"

msgid "该名称已存在"
msgstr "Este nome já existe"

msgid "令牌已过期"
msgstr "O token expirou"

msgid "项目名称已存在"
msgstr "O nome do projeto já existe"

msgid "设备未注册"
msgstr "Dispositivo não registrado"

msgid "项目删除成功"
msgstr "Projeto excluído com sucesso"

