# Japanese translations for your application.
# Copyright (C) 2023 ORGANIZATION
# This file is distributed under the same license as the project.
msgid ""
msgstr ""
"Project-Id-Version:  1.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-08-01 12:00+0000\n"
"PO-Revision-Date: 2025-04-07 02:00+0800\n"
"Last-Translator: AI Assistant\n"
"Language: ja\n"
"Language-Team: ja <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

msgid "资源不存在"
msgstr "リソースが存在しません"

msgid "消息删除成功"
msgstr "メッセージの削除に成功しました"

msgid "用户信息更新成功"
msgstr "ユーザー情報の更新に成功しました"

msgid "验证成功"
msgstr "検証に成功しました"

#, python-brace-format
msgid "使用默认locale: {default_locale}"
msgstr "デフォルトのロケールを使用: {default_locale}"

msgid "缺少更新数据"
msgstr "更新データがありません"

msgid "设备授权成功"
msgstr "デバイスの認証に成功しました"

msgid "消息标记为已读"
msgstr "メッセージが既読としてマークされました"

msgid "消息发送成功"
msgstr "メッセージの送信に成功しました"

msgid "该任务已完成"
msgstr "このタスクは完了しています"

msgid "登录成功"
msgstr "ログインに成功しました"

msgid "检测到你尚未加入服务器，请先加入我们的 Discord！"
msgstr ""

msgid "没有找到匹配的设备"
msgstr "一致するデバイスが見つかりません"

msgid "该用户已被邀请"
msgstr "このユーザーはすでに招待されています"

msgid "注册失败，请稍后重试"
msgstr "登録に失敗しました。後でもう一度お試しください"

msgid "获取设备项目服务配置失败"
msgstr "デバイスプロジェクトサービス設定の取得に失敗しました"

msgid "项目配置不存在"
msgstr "プロジェクト構成が存在しません"

#, python-brace-format
msgid "获取用户信息失败: {user_resp.text}"
msgstr ""

msgid "该项目不允许配置代理"
msgstr "このプロジェクトはプロキシ構成を許可していません"

msgid "获取消息详情失败"
msgstr "メッセージの詳細の取得に失敗しました"

msgid "获取服务配置失败"
msgstr "サービス構成の取得に失敗しました"

msgid "服务停止：连续两个检测周期未收到运行指标"
msgstr "サービス停止：連続2回の検出サイクルで動作指標を受信していません"

msgid "设备项目不存在"
msgstr "デバイスプロジェクトが存在しません"

#, python-brace-format
msgid "成功授权 {len(associations)} 个设备"
msgstr "{len(associations)}台のデバイスを正常に認証しました"

msgid "文件名已存在"
msgstr "ファイル名はすでに存在します"

msgid "获取验证码图像错误"
msgstr "認証コード画像の取得エラー"

msgid "没有从 Discord 返回有效的 code 参数。"
msgstr ""

msgid "项目配置已存在"
msgstr "プロジェクト構成はすでに存在します"

msgid "输入验证错误"
msgstr "入力検証エラー"

msgid "注册成功"
msgstr "登録に成功しました"

msgid "无效的认证令牌"
msgstr "無効な認証トークン"

msgid "项目不存在"
msgstr "プロジェクトが存在しません"

msgid "等待服务启动：配置已完成但尚未收到运行指标"
msgstr "サービス開始を待機中：構成は完了していますが、実行指標はまだ受信されていません"

msgid "缺少验证码"
msgstr "認証コードがありません"

msgid "获取设备列表失败"
msgstr "デバイスリストの取得に失敗しました"

msgid "项目状态与指标数据不一致"
msgstr "プロジェクトの状態と指標データが一致しません"

msgid "项目正在被设备使用，无法删除"
msgstr "プロジェクトはデバイスによって使用されており、削除できません"

msgid "设备不存在"
msgstr "デバイスが存在しません"

msgid "邮箱验证码"
msgstr "メール認証コード"

msgid "用户名已存在"
msgstr "ユーザー名はすでに存在します"

msgid "设备有正在初始化的项目服务"
msgstr "デバイスに初期化中のプロジェクトサービスがあります"

msgid "设备从未上报过运行指标"
msgstr "デバイスは動作指標を報告したことがありません"

msgid "获取设备状态统计失败"
msgstr "デバイス状態統計の取得に失敗しました"

msgid "已翻译消息: "
msgstr "翻訳されたメッセージ: "

msgid "获取用户资料失败"
msgstr "ユーザープロファイルの取得に失敗しました"

msgid "设备 mac 地址已存在"
msgstr "デバイスのMACアドレスはすでに存在します"

msgid "需要管理员权限"
msgstr "管理者権限が必要です"

msgid "密码错误"
msgstr "パスワードが間違っています"

msgid "token错误"
msgstr ""

msgid "设备创建成功"
msgstr "デバイスの作成に成功しました"

msgid "发送消息失败"
msgstr "メッセージの送信に失敗しました"

msgid "密码修改成功"
msgstr "パスワードの変更に成功しました"

msgid "修改密码失败"
msgstr "パスワードの変更に失敗しました"

msgid "获取消息列表失败"
msgstr "メッセージリストの取得に失敗しました"

#, python-brace-format
msgid "无效的代币code: {token_code}"
msgstr ""

#, python-brace-format
msgid "状态分析失败: {str(e)}"
msgstr "状態分析に失敗しました: {str(e)}"

msgid "设备有正在运行的项目服务"
msgstr "デバイスに実行中のプロジェクトサービスがあります"

msgid "设备代理未配置"
msgstr "デバイスプロキシが構成されていません"

#, python-brace-format
msgid "设备已离线超过{offline_days}天"
msgstr "デバイスは{offline_days}日以上オフラインになっています"

msgid "查询失败"
msgstr ""

msgid "邮箱已存在"
msgstr "メールアドレスはすでに存在します"

msgid "设备项目配置不存在"
msgstr "デバイスプロジェクト構成が存在しません"

msgid "设备删除成功"
msgstr "デバイスの削除に成功しました"

#, python-brace-format
msgid "无效的链代码: {chain_code}"
msgstr ""

msgid "项目服务正常运行中"
msgstr "プロジェクトサービスは正常に実行中です"

msgid "获取设备令牌失败"
msgstr "デバイストークンの取得に失敗しました"

#, python-brace-format
msgid "获取 Discord access_token 失败: {token_resp.text}"
msgstr ""

msgid "缺少必要参数"
msgstr "必須パラメータがありません"

msgid "没有提供更新数据"
msgstr "更新データが提供されていません"

msgid "项目尚未完成初始化配置"
msgstr "プロジェクトの初期化構成が完了していません"

msgid "获取用户列表失败，请稍后重试"
msgstr "ユーザーリストの取得に失敗しました。後でもう一度お試しください"

msgid "服务器内部错误"
msgstr "サーバー内部エラー"

msgid "无效的请求数据"
msgstr "無効なリクエストデータ"

msgid "无效的令牌"
msgstr "無効なトークン"

msgid "操作失败"
msgstr "操作に失敗しました"

msgid "密钥必须是32字节"
msgstr "キーは32バイトである必要があります"

msgid "项目正在被设备使用，无法禁用"
msgstr "プロジェクトはデバイスによって使用されており、無効にできません"

msgid "操作成功"
msgstr "操作に成功しました"

msgid "获取概览数据失败"
msgstr "概要データの取得に失敗しました"

msgid "项目名称已被使用"
msgstr "プロジェクト名はすでに使用されています"

msgid "登录失败，请稍后重试"
msgstr "ログインに失敗しました。後でもう一度お試しください"

msgid "项目配置删除成功"
msgstr "プロジェクト構成の削除に成功しました"

msgid "更新已接收，但未处理"
msgstr ""

msgid "生成邀请链接失败"
msgstr ""

msgid "文件不存在"
msgstr "ファイルが存在しません"

msgid "base64编码的nonce"
msgstr "Base64エンコードされたnonce"

msgid "更新用户信息失败，请稍后重试"
msgstr "ユーザー情報の更新に失敗しました。後でもう一度お試しください"

msgid "删除设备失败"
msgstr "デバイスの削除に失敗しました"

#, python-brace-format
msgid "发送失败: {str(e)}"
msgstr "送信に失敗しました: {str(e)}"

msgid "任务类型不存在"
msgstr "タスクタイプが存在しません"

msgid "项目创建成功"
msgstr "プロジェクトの作成に成功しました"

msgid "所有项目服务停止且存在未解决的错误状态"
msgstr "すべてのプロジェクトサービスが停止し、未解決のエラー状態があります"

msgid "权限不足"
msgstr "権限が不足しています"

msgid "service_compose 必须包含 services 字段"
msgstr "service_composeにはservicesフィールドが含まれている必要があります"

msgid "service_compose 必须是一个对象"
msgstr "service_composeはオブジェクトである必要があります"

msgid "缺少认证令牌"
msgstr "認証トークンがありません"

msgid "没有权限访问该项目"
msgstr "このプロジェクトにアクセスする権限がありません"

msgid "令牌已被撤销"
msgstr "トークンが取り消されました"

msgid "生成服务配置失败"
msgstr "サービス構成の生成に失敗しました"

msgid "所有消息已标记为已读"
msgstr "すべてのメッセージが既読としてマークされました"

msgid "密钥未设置"
msgstr ""

msgid "缺少必要字段：msg_type, subject, content"
msgstr "必須フィールドがありません：msg_type、subject、content"

msgid "消息不存在"
msgstr "メッセージが存在しません"

msgid "设备项目不属于该项目"
msgstr "デバイスプロジェクトはこのプロジェクトに属していません"

#, python-brace-format
msgid "从Accept-Language获取locale: {locale}"
msgstr "Accept-Languageからロケールを取得: {locale}"

#, python-brace-format
msgid "从查询参数获取locale: {locale}"
msgstr "クエリパラメータからロケールを取得: {locale}"

msgid "删除消息失败"
msgstr "メッセージの削除に失敗しました"

msgid "解密失败"
msgstr "復号化に失敗しました"

msgid "请勿重复创建钱包"
msgstr "ウォレットを重複して作成しないでください"

#, python-brace-format
msgid "JSON解析错误: {str(e)}"
msgstr "JSON解析エラー: {str(e)}"

#, python-brace-format
msgid "翻译响应消息时出错: {str(e)}"
msgstr "応答メッセージの翻訳中にエラーが発生しました: {str(e)}"

msgid "钱包创建失败"
msgstr "ウォレットの作成に失敗しました"

msgid "重新生成系统应用配置失败"
msgstr "システムアプリケーション構成の再生成に失敗しました"

msgid "设备未分配服务配置"
msgstr "デバイスにサービス構成が割り当てられていません"

msgid "设备未配置系统应用"
msgstr "デバイスにシステムアプリケーションが構成されていません"

msgid "项目未被删除"
msgstr "プロジェクトは削除されませんでした"

msgid "缺少必要字段"
msgstr "必須フィールドがありません"

msgid "系统应用配置重新生成成功"
msgstr "システムアプリケーション構成が正常に再生成されました"

#, python-brace-format
msgid "成功授权 {count} 个设备"
msgstr "{count}台のデバイスを正常に認証しました"

msgid "项目代理未配置"
msgstr "プロジェクトプロキシが構成されていません"

msgid "邮箱格式错误"
msgstr "メールアドレスの形式が正しくありません"

msgid "设备更新成功"
msgstr "デバイスの更新に成功しました"

msgid "未能获取到 access_token"
msgstr ""

msgid "base64编码的加密MAC地址"
msgstr "Base64エンコードされた暗号化MACアドレス"

msgid "项目更新成功"
msgstr "プロジェクトの更新に成功しました"

msgid "获取设备详情失败"
msgstr "デバイスの詳細の取得に失敗しました"

msgid "邀请记录不存在"
msgstr "招待記録が存在しません"

msgid "更新设备失败"
msgstr "デバイスの更新に失敗しました"

msgid "无效的 MAC 地址"
msgstr "無効なMACアドレス"

msgid "服务配置不存在"
msgstr "サービス構成が存在しません"

msgid "缺少用户ID"
msgstr "ユーザーIDがありません"

msgid "无法找到用户"
msgstr "ユーザーが見つかりません"

msgid "用户不存在"
msgstr "ユーザーが存在しません"

msgid "所有的项目都是 created 状态"
msgstr "すべてのプロジェクトが「作成済み」状態です"

msgid "该用户已被授权访问此设备"
msgstr "このユーザーはすでにこのデバイスへのアクセスを許可されています"

msgid "生成 docker-compose 配置失败"
msgstr "docker-compose設定の生成に失敗しました"

msgid "需要新的认证令牌"
msgstr "新しい認証トークンが必要です"

msgid "创建设备失败"
msgstr "デバイスの作成に失敗しました"

msgid "验证码错误或已过期"
msgstr "認証コードが間違っているか、期限切れです"

msgid "验证码发送成功"
msgstr "認証コードが正常に送信されました"

msgid "名称和 docker-compose 配置为必填项"
msgstr "名前とdocker-compose構成は必須項目です"

#, python-brace-format
msgid "数据验证失败: {e.message}"
msgstr "データ検証に失敗しました: {e.message}"

msgid "未登录或token已过期"
msgstr "ログインしていないか、トークンの期限が切れています"

msgid "没有提供标签"
msgstr "タグが提供されていません"

msgid "邀请积分已更新"
msgstr "招待ポイントが更新されました"

msgid "该名称已存在"
msgstr "この名前はすでに存在します"

msgid "令牌已过期"
msgstr "トークンの期限が切れました"

msgid "项目名称已存在"
msgstr "プロジェクト名はすでに存在します"

msgid "设备未注册"
msgstr "デバイスが登録されていません"

msgid "项目删除成功"
msgstr "プロジェクトの削除に成功しました"

