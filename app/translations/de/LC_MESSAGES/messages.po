# German translations for your application.
# Copyright (C) 2023 ORGANIZATION
# This file is distributed under the same license as the project.
msgid ""
msgstr ""
"Project-Id-Version:  1.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-08-01 12:00+0000\n"
"PO-Revision-Date: 2025-04-07 01:30+0800\n"
"Last-Translator: AI Assistant\n"
"Language: de\n"
"Language-Team: de <<EMAIL>>\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

msgid "资源不存在"
msgstr "Ressource existiert nicht"

msgid "消息删除成功"
msgstr "Nachricht erfolgreich gelöscht"

msgid "用户信息更新成功"
msgstr "Benutzerinformationen erfolgreich aktualisiert"

msgid "验证成功"
msgstr "Verifizierung erfolgreich"

#, python-brace-format
msgid "使用默认locale: {default_locale}"
msgstr "Verwenden der Standardsprache: {default_locale}"

msgid "缺少更新数据"
msgstr "Aktualisierungsdaten fehlen"

msgid "设备授权成功"
msgstr "Gerät erfolgreich autorisiert"

msgid "消息标记为已读"
msgstr "Nachricht als gelesen markiert"

msgid "消息发送成功"
msgstr "Nachricht erfolgreich gesendet"

msgid "该任务已完成"
msgstr "Diese Aufgabe ist bereits abgeschlossen"

msgid "登录成功"
msgstr "Anmeldung erfolgreich"

msgid "检测到你尚未加入服务器，请先加入我们的 Discord！"
msgstr "Wir haben festgestellt, dass du dem Server noch nicht beigetreten bist. Bitte tritt zuerst unserem Discord bei!"

msgid "没有找到匹配的设备"
msgstr "Keine passenden Geräte gefunden"

msgid "该用户已被邀请"
msgstr "Dieser Benutzer wurde bereits eingeladen"

msgid "注册失败，请稍后重试"
msgstr "Registrierung fehlgeschlagen, bitte versuche es später erneut"

msgid "获取设备项目服务配置失败"
msgstr "Abrufen der Geräteprojektdienstkonfiguration fehlgeschlagen"

msgid "项目配置不存在"
msgstr "Projektkonfiguration existiert nicht"

#, python-brace-format
msgid "获取用户信息失败: {user_resp.text}"
msgstr "Abrufen der Benutzerinformationen fehlgeschlagen: {user_resp.text}"

msgid "该项目不允许配置代理"
msgstr "Dieses Projekt erlaubt keine Proxy-Konfiguration"

msgid "获取消息详情失败"
msgstr "Abrufen der Nachrichtendetails fehlgeschlagen"

msgid "获取服务配置失败"
msgstr "Abrufen der Dienstkonfiguration fehlgeschlagen"

msgid "服务停止：连续两个检测周期未收到运行指标"
msgstr "Dienst gestoppt: Keine Betriebskennzahlen für zwei aufeinanderfolgende Erkennungszyklen erhalten"

msgid "设备项目不存在"
msgstr "Geräteprojekt existiert nicht"

#, python-brace-format
msgid "成功授权 {len(associations)} 个设备"
msgstr "{len(associations)} Geräte erfolgreich autorisiert"

msgid "文件名已存在"
msgstr "Dateiname existiert bereits"

msgid "获取验证码图像错误"
msgstr "Fehler beim Abrufen des Verifizierungscode-Bildes"

msgid "没有从 Discord 返回有效的 code 参数。"
msgstr "Kein gültiger Code-Parameter von Discord zurückgegeben."

msgid "项目配置已存在"
msgstr "Projektkonfiguration existiert bereits"

msgid "输入验证错误"
msgstr "Eingabevalidierungsfehler"

msgid "注册成功"
msgstr "Registrierung erfolgreich"

msgid "无效的认证令牌"
msgstr "Ungültiges Authentifizierungstoken"

msgid "项目不存在"
msgstr "Projekt existiert nicht"

msgid "等待服务启动：配置已完成但尚未收到运行指标"
msgstr "Warten auf Dienststart: Konfiguration abgeschlossen, aber noch keine Betriebskennzahlen erhalten"

msgid "缺少验证码"
msgstr "Verifizierungscode fehlt"

msgid "获取设备列表失败"
msgstr "Abrufen der Geräteliste fehlgeschlagen"

msgid "项目状态与指标数据不一致"
msgstr "Projektstatus stimmt nicht mit Metrikdaten überein"

msgid "项目正在被设备使用，无法删除"
msgstr "Projekt wird von Geräten verwendet und kann nicht gelöscht werden"

msgid "设备不存在"
msgstr "Gerät existiert nicht"

msgid "邮箱验证码"
msgstr "E-Mail-Verifizierungscode"

msgid "用户名已存在"
msgstr "Benutzername existiert bereits"

msgid "设备有正在初始化的项目服务"
msgstr "Gerät hat initialisierende Projektdienste"

msgid "设备从未上报过运行指标"
msgstr "Gerät hat noch nie Betriebskennzahlen gemeldet"

msgid "获取设备状态统计失败"
msgstr "Abrufen der Gerätestatus-Statistiken fehlgeschlagen"

msgid "已翻译消息: "
msgstr "Übersetzte Nachricht: "

msgid "获取用户资料失败"
msgstr "Abrufen des Benutzerprofils fehlgeschlagen"

msgid "设备 mac 地址已存在"
msgstr "Geräte-MAC-Adresse existiert bereits"

msgid "需要管理员权限"
msgstr "Administratorrechte erforderlich"

msgid "密码错误"
msgstr "Falsches Passwort"

msgid "token错误"
msgstr ""

msgid "设备创建成功"
msgstr "Gerät erfolgreich erstellt"

msgid "发送消息失败"
msgstr "Senden der Nachricht fehlgeschlagen"

msgid "密码修改成功"
msgstr "Passwort erfolgreich geändert"

msgid "修改密码失败"
msgstr "Ändern des Passworts fehlgeschlagen"

msgid "获取消息列表失败"
msgstr "Abrufen der Nachrichtenliste fehlgeschlagen"

#, python-brace-format
msgid "无效的代币code: {token_code}"
msgstr "Ungültiger Token-Code: {token_code}"

#, python-brace-format
msgid "状态分析失败: {str(e)}"
msgstr "Statusanalyse fehlgeschlagen: {str(e)}"

msgid "设备有正在运行的项目服务"
msgstr "Gerät hat laufende Projektdienste"

msgid "设备代理未配置"
msgstr "Geräte-Proxy nicht konfiguriert"

#, python-brace-format
msgid "设备已离线超过{offline_days}天"
msgstr "Gerät ist seit mehr als {offline_days} Tagen offline"

msgid "查询失败"
msgstr "Abfrage fehlgeschlagen"

msgid "邮箱已存在"
msgstr "E-Mail existiert bereits"

msgid "设备项目配置不存在"
msgstr "Geräteprojektkonfiguration existiert nicht"

msgid "设备删除成功"
msgstr "Gerät erfolgreich gelöscht"

#, python-brace-format
msgid "无效的链代码: {chain_code}"
msgstr "Ungültiger Kettencode: {chain_code}"

msgid "项目服务正常运行中"
msgstr "Projektdienst läuft normal"

msgid "获取设备令牌失败"
msgstr "Abrufen des Gerätetokens fehlgeschlagen"

#, python-brace-format
msgid "获取 Discord access_token 失败: {token_resp.text}"
msgstr "Abrufen des Discord-Zugriffstokens fehlgeschlagen: {token_resp.text}"

msgid "缺少必要参数"
msgstr "Erforderliche Parameter fehlen"

msgid "没有提供更新数据"
msgstr "Keine Aktualisierungsdaten bereitgestellt"

msgid "项目尚未完成初始化配置"
msgstr "Initialisierungskonfiguration des Projekts nicht abgeschlossen"

msgid "获取用户列表失败，请稍后重试"
msgstr "Abrufen der Benutzerliste fehlgeschlagen, bitte versuchen Sie es später erneut"

msgid "服务器内部错误"
msgstr "Interner Serverfehler"

msgid "无效的请求数据"
msgstr "Ungültige Anforderungsdaten"

msgid "无效的令牌"
msgstr "Ungültiges Token"

msgid "操作失败"
msgstr "Operation fehlgeschlagen"

msgid "密钥必须是32字节"
msgstr "Schlüssel muss 32 Byte lang sein"

msgid "项目正在被设备使用，无法禁用"
msgstr "Projekt wird von Geräten verwendet und kann nicht deaktiviert werden"

msgid "操作成功"
msgstr "Operation erfolgreich"

msgid "获取概览数据失败"
msgstr "Abrufen der Übersichtsdaten fehlgeschlagen"

msgid "项目名称已被使用"
msgstr "Projektname wird bereits verwendet"

msgid "登录失败，请稍后重试"
msgstr "Anmeldung fehlgeschlagen, bitte versuchen Sie es später erneut"

msgid "项目配置删除成功"
msgstr "Projektkonfiguration erfolgreich gelöscht"

msgid "更新已接收，但未处理"
msgstr "Aktualisierung empfangen, aber nicht verarbeitet"

msgid "生成邀请链接失败"
msgstr "Generierung des Einladungslinks fehlgeschlagen"

msgid "文件不存在"
msgstr "Datei existiert nicht"

msgid "base64编码的nonce"
msgstr "Base64-kodierte Nonce"

msgid "更新用户信息失败，请稍后重试"
msgstr "Aktualisierung der Benutzerinformationen fehlgeschlagen, bitte versuchen Sie es später erneut"

msgid "删除设备失败"
msgstr "Löschen des Geräts fehlgeschlagen"

#, python-brace-format
msgid "发送失败: {str(e)}"
msgstr "Senden fehlgeschlagen: {str(e)}"

msgid "任务类型不存在"
msgstr "Aufgabentyp existiert nicht"

msgid "项目创建成功"
msgstr "Projekt erfolgreich erstellt"

msgid "所有项目服务停止且存在未解决的错误状态"
msgstr "Alle Projektdienste gestoppt und ungelöste Fehlerzustände vorhanden"

msgid "权限不足"
msgstr "Unzureichende Berechtigungen"

msgid "service_compose 必须包含 services 字段"
msgstr "service_compose muss das Feld 'services' enthalten"

msgid "service_compose 必须是一个对象"
msgstr "service_compose muss ein Objekt sein"

msgid "缺少认证令牌"
msgstr "Authentifizierungstoken fehlt"

msgid "没有权限访问该项目"
msgstr "Keine Berechtigung zum Zugriff auf dieses Projekt"

msgid "令牌已被撤销"
msgstr "Token wurde widerrufen"

msgid "生成服务配置失败"
msgstr "Generierung der Dienstkonfiguration fehlgeschlagen"

msgid "所有消息已标记为已读"
msgstr "Alle Nachrichten wurden als gelesen markiert"

msgid "密钥未设置"
msgstr "Schlüssel nicht gesetzt"

msgid "缺少必要字段：msg_type, subject, content"
msgstr "Erforderliche Felder fehlen: msg_type, subject, content"

msgid "消息不存在"
msgstr "Nachricht existiert nicht"

msgid "设备项目不属于该项目"
msgstr "Geräteprojekt gehört nicht zu diesem Projekt"

#, python-brace-format
msgid "从Accept-Language获取locale: {locale}"
msgstr "Locale aus Accept-Language abrufen: {locale}"

#, python-brace-format
msgid "从查询参数获取locale: {locale}"
msgstr "Locale aus Abfrageparametern abrufen: {locale}"

msgid "删除消息失败"
msgstr "Löschen der Nachricht fehlgeschlagen"

msgid "解密失败"
msgstr "Entschlüsselung fehlgeschlagen"

msgid "请勿重复创建钱包"
msgstr "Erstellen Sie die Wallet nicht erneut"

#, python-brace-format
msgid "JSON解析错误: {str(e)}"
msgstr "JSON-Analysefehler: {str(e)}"

#, python-brace-format
msgid "翻译响应消息时出错: {str(e)}"
msgstr "Fehler beim Übersetzen der Antwortnachricht: {str(e)}"

msgid "钱包创建失败"
msgstr "Erstellen der Wallet fehlgeschlagen"

msgid "重新生成系统应用配置失败"
msgstr "Neuerstellen der Systemanwendungskonfiguration fehlgeschlagen"

msgid "设备未分配服务配置"
msgstr "Gerät nicht mit Dienstkonfiguration zugewiesen"

msgid "设备未配置系统应用"
msgstr "Gerät nicht mit Systemanwendung konfiguriert"

msgid "项目未被删除"
msgstr "Projekt wurde nicht gelöscht"

msgid "缺少必要字段"
msgstr "Erforderliche Felder fehlen"

msgid "系统应用配置重新生成成功"
msgstr "Systemanwendungskonfiguration erfolgreich neu erstellt"

#, python-brace-format
msgid "成功授权 {count} 个设备"
msgstr "{count} Geräte erfolgreich autorisiert"

msgid "项目代理未配置"
msgstr "Projekt-Proxy nicht konfiguriert"

msgid "邮箱格式错误"
msgstr "Falsches E-Mail-Format"

msgid "设备更新成功"
msgstr "Gerät erfolgreich aktualisiert"

msgid "未能获取到 access_token"
msgstr "Zugriffstokens konnte nicht abgerufen werden"

msgid "base64编码的加密MAC地址"
msgstr "Base64-kodierte verschlüsselte MAC-Adresse"

msgid "项目更新成功"
msgstr "Projekt erfolgreich aktualisiert"

msgid "获取设备详情失败"
msgstr "Abrufen der Gerätedetails fehlgeschlagen"

msgid "邀请记录不存在"
msgstr "Einladungsdatensatz existiert nicht"

msgid "更新设备失败"
msgstr "Aktualisierung des Geräts fehlgeschlagen"

msgid "无效的 MAC 地址"
msgstr "Ungültige MAC-Adresse"

msgid "服务配置不存在"
msgstr "Dienstkonfiguration existiert nicht"

msgid "缺少用户ID"
msgstr "Benutzer-ID fehlt"

msgid "无法找到用户"
msgstr "Benutzer nicht gefunden"

msgid "用户不存在"
msgstr "Benutzer existiert nicht"

msgid "所有的项目都是 created 状态"
msgstr "Alle Projekte sind im Status 'erstellt'"

msgid "该用户已被授权访问此设备"
msgstr "Dieser Benutzer wurde bereits für den Zugriff auf dieses Gerät autorisiert"

msgid "生成 docker-compose 配置失败"
msgstr "Generierung der docker-compose-Konfiguration fehlgeschlagen"

msgid "需要新的认证令牌"
msgstr "Neues Authentifizierungstoken erforderlich"

msgid "创建设备失败"
msgstr "Erstellen des Geräts fehlgeschlagen"

msgid "验证码错误或已过期"
msgstr "Überprüfungscode falsch oder abgelaufen"

msgid "验证码发送成功"
msgstr "Überprüfungscode erfolgreich gesendet"

msgid "名称和 docker-compose 配置为必填项"
msgstr "Name und docker-compose-Konfiguration sind erforderlich"

#, python-brace-format
msgid "数据验证失败: {e.message}"
msgstr "Datenvalidierung fehlgeschlagen: {e.message}"

msgid "未登录或token已过期"
msgstr "Nicht angemeldet oder Token abgelaufen"

msgid "没有提供标签"
msgstr "Keine Etiketten bereitgestellt"

msgid "邀请积分已更新"
msgstr "Einladungspunkte aktualisiert"

msgid "该名称已存在"
msgstr "Dieser Name existiert bereits"

msgid "令牌已过期"
msgstr "Token ist abgelaufen"

msgid "项目名称已存在"
msgstr "Projektname existiert bereits"

msgid "设备未注册"
msgstr "Gerät nicht registriert"

msgid "项目删除成功"
msgstr "Projekt erfolgreich gelöscht"

