# English translations for your application.
# Copyright (C) 2023 ORGANIZATION
# This file is distributed under the same license as the project.
msgid ""
msgstr ""
"Project-Id-Version:  1.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-08-01 12:00+0000\n"
"PO-Revision-Date: 2025-04-07 02:15+0800\n"
"Last-Translator: AI Assistant\n"
"Language: en\n"
"Language-Team: en <<EMAIL>>\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

msgid "资源不存在"
msgstr "Resource does not exist"

msgid "消息删除成功"
msgstr "Message deleted successfully"

msgid "用户信息更新成功"
msgstr "User information updated successfully"

msgid "验证成功"
msgstr "Verification successful"

#, python-brace-format
msgid "使用默认locale: {default_locale}"
msgstr "Using default locale: {default_locale}"

msgid "缺少更新数据"
msgstr "Missing update data"

msgid "设备授权成功"
msgstr "Device authorized successfully"

msgid "消息标记为已读"
msgstr "Message marked as read"

msgid "消息发送成功"
msgstr "Message sent successfully"

msgid "该任务已完成"
msgstr "This task has been completed"

msgid "登录成功"
msgstr "Login successful"

msgid "检测到你尚未加入服务器，请先加入我们的 Discord！"
msgstr ""

msgid "没有找到匹配的设备"
msgstr "No matching devices found"

msgid "该用户已被邀请"
msgstr "This user has already been invited"

msgid "注册失败，请稍后重试"
msgstr "Registration failed, please try again later"

msgid "获取设备项目服务配置失败"
msgstr "Failed to get device project service configuration"

msgid "项目配置不存在"
msgstr "Project configuration does not exist"

#, python-brace-format
msgid "获取用户信息失败: {user_resp.text}"
msgstr ""

msgid "该项目不允许配置代理"
msgstr "This project does not allow proxy configuration"

msgid "获取消息详情失败"
msgstr "Failed to get message details"

msgid "获取服务配置失败"
msgstr "Failed to get service configuration"

msgid "服务停止：连续两个检测周期未收到运行指标"
msgstr ""
"Service stopped: No operating metrics received for two consecutive "
"detection cycles"

msgid "设备项目不存在"
msgstr "Device project does not exist"

#, python-brace-format
msgid "成功授权 {len(associations)} 个设备"
msgstr "Successfully authorized {len(associations)} devices"

msgid "文件名已存在"
msgstr "File name already exists"

msgid "获取验证码图像错误"
msgstr "Error getting verification code image"

msgid "没有从 Discord 返回有效的 code 参数。"
msgstr ""

msgid "项目配置已存在"
msgstr "Project configuration already exists"

msgid "输入验证错误"
msgstr "Input validation error"

msgid "注册成功"
msgstr "Registration successful"

msgid "无效的认证令牌"
msgstr "Invalid authentication token"

msgid "项目不存在"
msgstr "Project does not exist"

msgid "等待服务启动：配置已完成但尚未收到运行指标"
msgstr ""
"Waiting for service to start: Configuration complete but no operating "
"metrics received yet"

msgid "缺少验证码"
msgstr "Missing verification code"

msgid "获取设备列表失败"
msgstr "Failed to get device list"

msgid "项目状态与指标数据不一致"
msgstr "Project status inconsistent with metric data"

msgid "项目正在被设备使用，无法删除"
msgstr "Project is being used by devices and cannot be deleted"

msgid "设备不存在"
msgstr "Device does not exist"

msgid "邮箱验证码"
msgstr "Email verification code"

msgid "用户名已存在"
msgstr "Username already exists"

msgid "设备有正在初始化的项目服务"
msgstr "Device has project services being initialized"

msgid "设备从未上报过运行指标"
msgstr "Device has never reported operating metrics"

msgid "获取设备状态统计失败"
msgstr "Failed to get device status statistics"

msgid "已翻译消息: "
msgstr "Translated message: "

msgid "获取用户资料失败"
msgstr "Failed to get user profile"

msgid "设备 mac 地址已存在"
msgstr "Device MAC address already exists"

msgid "需要管理员权限"
msgstr "Admin permission required"

msgid "密码错误"
msgstr "Incorrect password"

msgid "token错误"
msgstr "Incorrect token"

msgid "设备创建成功"
msgstr "Device created successfully"

msgid "发送消息失败"
msgstr "Failed to send message"

msgid "密码修改成功"
msgstr "Password changed successfully"

msgid "修改密码失败"
msgstr "Failed to change password"

msgid "获取消息列表失败"
msgstr "Failed to get message list"

#, python-brace-format
msgid "无效的代币code: {token_code}"
msgstr ""

#, python-brace-format
msgid "状态分析失败: {str(e)}"
msgstr "Status analysis failed: {str(e)}"

msgid "设备有正在运行的项目服务"
msgstr "Device has running project services"

msgid "设备代理未配置"
msgstr "Device proxy not configured"

#, python-brace-format
msgid "设备已离线超过{offline_days}天"
msgstr "Device has been offline for more than {offline_days} days"

msgid "查询失败"
msgstr ""

msgid "邮箱已存在"
msgstr "Email already exists"

msgid "设备项目配置不存在"
msgstr "Device project configuration does not exist"

msgid "设备删除成功"
msgstr "Device deleted successfully"

#, python-brace-format
msgid "无效的链代码: {chain_code}"
msgstr ""

msgid "项目服务正常运行中"
msgstr "Project service is running normally"

msgid "获取设备令牌失败"
msgstr "Failed to get device token"

#, python-brace-format
msgid "获取 Discord access_token 失败: {token_resp.text}"
msgstr ""

msgid "缺少必要参数"
msgstr "Missing required parameters"

msgid "没有提供更新数据"
msgstr "No update data provided"

msgid "项目尚未完成初始化配置"
msgstr "Project initialization configuration not completed"

msgid "获取用户列表失败，请稍后重试"
msgstr "Failed to get user list, please try again later"

msgid "服务器内部错误"
msgstr "Internal server error"

msgid "无效的请求数据"
msgstr "Invalid request data"

msgid "无效的令牌"
msgstr "Invalid token"

msgid "操作失败"
msgstr "Operation failed"

msgid "密钥必须是32字节"
msgstr "Key must be 32 bytes"

msgid "项目正在被设备使用，无法禁用"
msgstr "Project is being used by devices and cannot be disabled"

msgid "操作成功"
msgstr "Operation successful"

msgid "获取概览数据失败"
msgstr "Failed to get overview data"

msgid "项目名称已被使用"
msgstr "Project name already in use"

msgid "登录失败，请稍后重试"
msgstr "Login failed, please try again later"

msgid "项目配置删除成功"
msgstr "Project configuration deleted successfully"

msgid "更新已接收，但未处理"
msgstr ""

msgid "生成邀请链接失败"
msgstr ""

msgid "文件不存在"
msgstr "File does not exist"

msgid "base64编码的nonce"
msgstr "Base64 encoded nonce"

msgid "更新用户信息失败，请稍后重试"
msgstr "Failed to update user information, please try again later"

msgid "删除设备失败"
msgstr "Failed to delete device"

#, python-brace-format
msgid "发送失败: {str(e)}"
msgstr "Send failed: {str(e)}"

msgid "任务类型不存在"
msgstr "Task type does not exist"

msgid "项目创建成功"
msgstr "Project created successfully"

msgid "所有项目服务停止且存在未解决的错误状态"
msgstr "All project services stopped and there are unresolved error states"

msgid "权限不足"
msgstr "Insufficient permissions"

msgid "service_compose 必须包含 services 字段"
msgstr "service_compose must include services field"

msgid "service_compose 必须是一个对象"
msgstr "service_compose must be an object"

msgid "缺少认证令牌"
msgstr "Missing authentication token"

msgid "没有权限访问该项目"
msgstr "No permission to access this project"

msgid "令牌已被撤销"
msgstr "Token has been revoked"

msgid "生成服务配置失败"
msgstr "Failed to generate service configuration"

msgid "所有消息已标记为已读"
msgstr "All messages marked as read"

msgid "密钥未设置"
msgstr ""

msgid "缺少必要字段：msg_type, subject, content"
msgstr "Missing required fields: msg_type, subject, content"

msgid "消息不存在"
msgstr "Message does not exist"

msgid "设备项目不属于该项目"
msgstr "Device project does not belong to this project"

#, python-brace-format
msgid "从Accept-Language获取locale: {locale}"
msgstr "Getting locale from Accept-Language: {locale}"

#, python-brace-format
msgid "从查询参数获取locale: {locale}"
msgstr "Getting locale from query parameters: {locale}"

msgid "删除消息失败"
msgstr "Failed to delete message"

msgid "解密失败"
msgstr "Decryption failed"

msgid "请勿重复创建钱包"
msgstr "Please do not create the wallet again"

#, python-brace-format
msgid "JSON解析错误: {str(e)}"
msgstr "JSON parsing error: {str(e)}"

#, python-brace-format
msgid "翻译响应消息时出错: {str(e)}"
msgstr "Error translating response message: {str(e)}"

msgid "钱包创建失败"
msgstr "Failed to create wallet"

msgid "重新生成系统应用配置失败"
msgstr "Failed to regenerate system application configuration"

msgid "设备未分配服务配置"
msgstr "Device not assigned service configuration"

msgid "设备未配置系统应用"
msgstr "Device not configured with system application"

msgid "项目未被删除"
msgstr "Project was not deleted"

msgid "缺少必要字段"
msgstr "Missing required fields"

msgid "系统应用配置重新生成成功"
msgstr "System application configuration regenerated successfully"

#, python-brace-format
msgid "成功授权 {count} 个设备"
msgstr "Successfully authorized {count} devices"

msgid "项目代理未配置"
msgstr "Project proxy not configured"

msgid "邮箱格式错误"
msgstr "Invalid email format"

msgid "设备更新成功"
msgstr "Device updated successfully"

msgid "未能获取到 access_token"
msgstr ""

msgid "base64编码的加密MAC地址"
msgstr "Base64 encoded encrypted MAC address"

msgid "项目更新成功"
msgstr "Project updated successfully"

msgid "获取设备详情失败"
msgstr "Failed to get device details"

msgid "邀请记录不存在"
msgstr "Invitation record does not exist"

msgid "更新设备失败"
msgstr "Failed to update device"

msgid "无效的 MAC 地址"
msgstr "Invalid MAC address"

msgid "服务配置不存在"
msgstr "Service configuration does not exist"

msgid "缺少用户ID"
msgstr "Missing user ID"

msgid "无法找到用户"
msgstr "User not found"

msgid "用户不存在"
msgstr "User does not exist"

msgid "所有的项目都是 created 状态"
msgstr "All projects are in created status"

msgid "该用户已被授权访问此设备"
msgstr "This user has already been authorized to access this device"

msgid "生成 docker-compose 配置失败"
msgstr "Failed to generate docker-compose configuration"

msgid "需要新的认证令牌"
msgstr "New authentication token required"

msgid "创建设备失败"
msgstr "Failed to create device"

msgid "验证码错误或已过期"
msgstr "Verification code is incorrect or expired"

msgid "验证码发送成功"
msgstr "Verification code sent successfully"

msgid "名称和 docker-compose 配置为必填项"
msgstr "Name and docker-compose configuration are required"

#, python-brace-format
msgid "数据验证失败: {e.message}"
msgstr "Data validation failed: {e.message}"

msgid "未登录或token已过期"
msgstr "Not logged in or token expired"

msgid "没有提供标签"
msgstr "No tags provided"

msgid "邀请积分已更新"
msgstr "Invitation points updated"

msgid "该名称已存在"
msgstr "This name already exists"

msgid "令牌已过期"
msgstr "Token has expired"

msgid "项目名称已存在"
msgstr "Project name already exists"

msgid "设备未注册"
msgstr "Device not registered"

msgid "项目删除成功"
msgstr "Project deleted successfully"

msgid "服务正常"
msgstr "Service is running"

msgid "配置错误"
msgstr "Service config error"

msgid "代理不可用"
msgstr "proxy is not available"

msgid "服务内部错误"
msgstr "service internal error"

msgid "服务暂时不可用"
msgstr "service is temporarily unavailable"

msgid "未知状态"
msgstr "unknown status"