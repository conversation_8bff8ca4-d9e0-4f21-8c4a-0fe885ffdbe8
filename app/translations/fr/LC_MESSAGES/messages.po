# French translations for your application.
# Copyright (C) 2023 ORGANIZATION
# This file is distributed under the same license as the project.
msgid ""
msgstr ""
"Project-Id-Version:  1.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-08-01 12:00+0000\n"
"PO-Revision-Date: 2025-04-06 22:57+0800\n"
"Last-Translator: AI Assistant\n"
"Language: fr\n"
"Language-Team: fr <<EMAIL>>\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

msgid "资源不存在"
msgstr "La ressource n'existe pas"

msgid "消息删除成功"
msgstr "Message supprimé avec succès"

msgid "用户信息更新成功"
msgstr "Informations utilisateur mises à jour avec succès"

msgid "验证成功"
msgstr "Vérification réussie"

#, python-brace-format
msgid "使用默认locale: {default_locale}"
msgstr "Utilisation de la locale par défaut : {default_locale}"

msgid "缺少更新数据"
msgstr "Données de mise à jour manquantes"

msgid "设备授权成功"
msgstr "Autorisation de l'appareil réussie"

msgid "消息标记为已读"
msgstr "Message marqué comme lu"

msgid "消息发送成功"
msgstr "Message envoyé avec succès"

msgid "该任务已完成"
msgstr "Cette tâche est déjà terminée"

msgid "登录成功"
msgstr "Connexion réussie"

msgid "检测到你尚未加入服务器，请先加入我们的 Discord！"
msgstr "Nous avons détecté que vous n'avez pas encore rejoint le serveur, veuillez d'abord rejoindre notre Discord !"

msgid "没有找到匹配的设备"
msgstr "Aucun appareil correspondant trouvé"

msgid "该用户已被邀请"
msgstr "Cet utilisateur a déjà été invité"

msgid "注册失败，请稍后重试"
msgstr "Échec de l'inscription, veuillez réessayer plus tard"

msgid "获取设备项目服务配置失败"
msgstr "Échec de l'obtention de la configuration du service du projet de l'appareil"

msgid "项目配置不存在"
msgstr "La configuration du projet n'existe pas"

#, python-brace-format
msgid "获取用户信息失败: {user_resp.text}"
msgstr "Échec de l'obtention des informations utilisateur : {user_resp.text}"

msgid "该项目不允许配置代理"
msgstr "Ce projet ne permet pas la configuration de proxy"

msgid "获取消息详情失败"
msgstr "Échec de l'obtention des détails du message"

msgid "获取服务配置失败"
msgstr "Échec de l'obtention de la configuration du service"

msgid "服务停止：连续两个检测周期未收到运行指标"
msgstr "Service arrêté : Aucune métrique de fonctionnement reçue pendant deux cycles de détection consécutifs"

msgid "设备项目不存在"
msgstr "Le projet de l'appareil n'existe pas"

#, python-brace-format
msgid "成功授权 {len(associations)} 个设备"
msgstr "Autorisation réussie de {len(associations)} appareils"

msgid "文件名已存在"
msgstr "Le nom du fichier existe déjà"

msgid "获取验证码图像错误"
msgstr "Erreur lors de l'obtention de l'image du code de vérification"

msgid "没有从 Discord 返回有效的 code 参数。"
msgstr "Aucun paramètre de code valide n'a été renvoyé par Discord."

msgid "项目配置已存在"
msgstr "La configuration du projet existe déjà"

msgid "输入验证错误"
msgstr "Erreur de validation d'entrée"

msgid "注册成功"
msgstr "Inscription réussie"

msgid "无效的认证令牌"
msgstr "Jeton d'authentification invalide"

msgid "项目不存在"
msgstr "Le projet n'existe pas"

msgid "等待服务启动：配置已完成但尚未收到运行指标"
msgstr "En attente du démarrage du service : La configuration est terminée mais aucune métrique de fonctionnement n'a encore été reçue"

msgid "缺少验证码"
msgstr "Code de vérification manquant"

msgid "获取设备列表失败"
msgstr "Échec de l'obtention de la liste des appareils"

msgid "项目状态与指标数据不一致"
msgstr "L'état du projet est incohérent avec les données métriques"

msgid "项目正在被设备使用，无法删除"
msgstr "Le projet est utilisé par des appareils et ne peut pas être supprimé"

msgid "设备不存在"
msgstr "L'appareil n'existe pas"

msgid "邮箱验证码"
msgstr "Code de vérification par email"

msgid "用户名已存在"
msgstr "Le nom d'utilisateur existe déjà"

msgid "设备有正在初始化的项目服务"
msgstr "L'appareil a des services de projet en initialisation"

msgid "设备从未上报过运行指标"
msgstr "L'appareil n'a jamais signalé de métriques de fonctionnement"

msgid "获取设备状态统计失败"
msgstr "Échec de l'obtention des statistiques d'état de l'appareil"

msgid "已翻译消息: "
msgstr "Message traduit : "

msgid "获取用户资料失败"
msgstr "Échec de l'obtention du profil utilisateur"

msgid "设备 mac 地址已存在"
msgstr "L'adresse MAC de l'appareil existe déjà"

msgid "需要管理员权限"
msgstr "Permissions d'administrateur requises"

msgid "密码错误"
msgstr "Mot de passe incorrect"

msgid "token错误"
msgstr ""

msgid "设备创建成功"
msgstr "Appareil créé avec succès"

msgid "发送消息失败"
msgstr "Échec de l'envoi du message"

msgid "密码修改成功"
msgstr "Mot de passe modifié avec succès"

msgid "修改密码失败"
msgstr "Échec de la modification du mot de passe"

msgid "获取消息列表失败"
msgstr "Échec de l'obtention de la liste des messages"

#, python-brace-format
msgid "无效的代币code: {token_code}"
msgstr "Code de jeton invalide : {token_code}"

#, python-brace-format
msgid "状态分析失败: {str(e)}"
msgstr "Analyse de l'état échouée : {str(e)}"

msgid "设备有正在运行的项目服务"
msgstr "L'appareil a des services de projet en cours d'exécution"

msgid "设备代理未配置"
msgstr "Proxy de l'appareil non configuré"

#, python-brace-format
msgid "设备已离线超过{offline_days}天"
msgstr "L'appareil est hors ligne depuis plus de {offline_days} jours"

msgid "查询失败"
msgstr "Échec de la requête"

msgid "邮箱已存在"
msgstr "L'email existe déjà"

msgid "设备项目配置不存在"
msgstr "La configuration du projet de l'appareil n'existe pas"

msgid "设备删除成功"
msgstr "Appareil supprimé avec succès"

#, python-brace-format
msgid "无效的链代码: {chain_code}"
msgstr "Code de chaîne invalide : {chain_code}"

msgid "项目服务正常运行中"
msgstr "Le service du projet fonctionne normalement"

msgid "获取设备令牌失败"
msgstr "Échec de l'obtention du jeton de l'appareil"

#, python-brace-format
msgid "获取 Discord access_token 失败: {token_resp.text}"
msgstr "Échec de l'obtention du jeton d'accès Discord : {token_resp.text}"

msgid "缺少必要参数"
msgstr "Paramètres nécessaires manquants"

msgid "没有提供更新数据"
msgstr "Aucune donnée de mise à jour fournie"

msgid "项目尚未完成初始化配置"
msgstr "La configuration d'initialisation du projet n'est pas terminée"

msgid "获取用户列表失败，请稍后重试"
msgstr "Échec de l'obtention de la liste des utilisateurs, veuillez réessayer plus tard"

msgid "服务器内部错误"
msgstr "Erreur interne du serveur"

msgid "无效的请求数据"
msgstr "Données de requête invalides"

msgid "无效的令牌"
msgstr "Jeton invalide"

msgid "操作失败"
msgstr "Opération échouée"

msgid "密钥必须是32字节"
msgstr "La clé doit faire 32 octets"

msgid "项目正在被设备使用，无法禁用"
msgstr "Le projet est utilisé par des appareils et ne peut pas être désactivé"

msgid "操作成功"
msgstr "Opération réussie"

msgid "获取概览数据失败"
msgstr "Échec de l'obtention des données de synthèse"

msgid "项目名称已被使用"
msgstr "Le nom du projet est déjà utilisé"

msgid "登录失败，请稍后重试"
msgstr "Échec de la connexion, veuillez réessayer plus tard"

msgid "项目配置删除成功"
msgstr "Configuration du projet supprimée avec succès"

msgid "更新已接收，但未处理"
msgstr "Mise à jour reçue mais non traitée"

msgid "生成邀请链接失败"
msgstr "Échec de la génération du lien d'invitation"

msgid "文件不存在"
msgstr "Le fichier n'existe pas"

msgid "base64编码的nonce"
msgstr "Nonce encodé en base64"

msgid "更新用户信息失败，请稍后重试"
msgstr "Échec de la mise à jour des informations utilisateur, veuillez réessayer plus tard"

msgid "删除设备失败"
msgstr "Échec de la suppression de l'appareil"

#, python-brace-format
msgid "发送失败: {str(e)}"
msgstr "Envoi échoué : {str(e)}"

msgid "任务类型不存在"
msgstr "Le type de tâche n'existe pas"

msgid "项目创建成功"
msgstr "Projet créé avec succès"

msgid "所有项目服务停止且存在未解决的错误状态"
msgstr "Tous les services de projet sont arrêtés et il existe des états d'erreur non résolus"

msgid "权限不足"
msgstr "Permissions insuffisantes"

msgid "service_compose 必须包含 services 字段"
msgstr "service_compose doit inclure le champ services"

msgid "service_compose 必须是一个对象"
msgstr "service_compose doit être un objet"

msgid "缺少认证令牌"
msgstr "Jeton d'authentification manquant"

msgid "没有权限访问该项目"
msgstr "Pas d'autorisation pour accéder à ce projet"

msgid "令牌已被撤销"
msgstr "Le jeton a été révoqué"

msgid "生成服务配置失败"
msgstr "Échec de la génération de la configuration du service"

msgid "所有消息已标记为已读"
msgstr "Tous les messages ont été marqués comme lus"

msgid "密钥未设置"
msgstr "Clé non définie"

msgid "缺少必要字段：msg_type, subject, content"
msgstr "Champs obligatoires manquants : msg_type, subject, content"

msgid "消息不存在"
msgstr "Le message n'existe pas"

msgid "设备项目不属于该项目"
msgstr "Le projet de l'appareil n'appartient pas à ce projet"

#, python-brace-format
msgid "从Accept-Language获取locale: {locale}"
msgstr "Obtention de la locale à partir d'Accept-Language : {locale}"

#, python-brace-format
msgid "从查询参数获取locale: {locale}"
msgstr "Obtention de la locale à partir des paramètres de requête : {locale}"

msgid "删除消息失败"
msgstr "Échec de la suppression du message"

msgid "解密失败"
msgstr "Échec du déchiffrement"

msgid "请勿重复创建钱包"
msgstr "Ne créez pas le portefeuille à nouveau"

#, python-brace-format
msgid "JSON解析错误: {str(e)}"
msgstr "Erreur d'analyse JSON : {str(e)}"

#, python-brace-format
msgid "翻译响应消息时出错: {str(e)}"
msgstr "Erreur lors de la traduction du message de réponse : {str(e)}"

msgid "钱包创建失败"
msgstr "Échec de la création du portefeuille"

msgid "重新生成系统应用配置失败"
msgstr "Échec de la régénération de la configuration de l'application système"

msgid "设备未分配服务配置"
msgstr "Configuration de service non attribuée à l'appareil"

msgid "设备未配置系统应用"
msgstr "L'appareil n'est pas configuré avec l'application système"

msgid "项目未被删除"
msgstr "Le projet n'a pas été supprimé"

msgid "缺少必要字段"
msgstr "Champs nécessaires manquants"

msgid "系统应用配置重新生成成功"
msgstr "Configuration de l'application système régénérée avec succès"

#, python-brace-format
msgid "成功授权 {count} 个设备"
msgstr "Autorisation réussie de {count} appareils"

msgid "项目代理未配置"
msgstr "Proxy du projet non configuré"

msgid "邮箱格式错误"
msgstr "Format d'email incorrect"

msgid "设备更新成功"
msgstr "Appareil mis à jour avec succès"

msgid "未能获取到 access_token"
msgstr "Impossible d'obtenir le jeton d'accès"

msgid "base64编码的加密MAC地址"
msgstr "Adresse MAC cryptée encodée en base64"

msgid "项目更新成功"
msgstr "Projet mis à jour avec succès"

msgid "获取设备详情失败"
msgstr "Échec de l'obtention des détails de l'appareil"

msgid "邀请记录不存在"
msgstr "L'enregistrement d'invitation n'existe pas"

msgid "更新设备失败"
msgstr "Échec de la mise à jour de l'appareil"

msgid "无效的 MAC 地址"
msgstr "Adresse MAC invalide"

msgid "服务配置不存在"
msgstr "La configuration du service n'existe pas"

msgid "缺少用户ID"
msgstr "ID utilisateur manquant"

msgid "无法找到用户"
msgstr "Utilisateur introuvable"

msgid "用户不存在"
msgstr "L'utilisateur n'existe pas"

msgid "所有的项目都是 created 状态"
msgstr "Tous les projets sont à l'état 'créé'"

msgid "该用户已被授权访问此设备"
msgstr "Cet utilisateur a déjà été autorisé à accéder à cet appareil"

msgid "生成 docker-compose 配置失败"
msgstr "Échec de la génération de la configuration docker-compose"

msgid "需要新的认证令牌"
msgstr "Nouveau jeton d'authentification requis"

msgid "创建设备失败"
msgstr "Échec de la création de l'appareil"

msgid "验证码错误或已过期"
msgstr "Code de vérification incorrect ou expiré"

msgid "验证码发送成功"
msgstr "Code de vérification envoyé avec succès"

msgid "名称和 docker-compose 配置为必填项"
msgstr "Le nom et la configuration docker-compose sont obligatoires"

#, python-brace-format
msgid "数据验证失败: {e.message}"
msgstr "Validation des données échouée : {e.message}"

msgid "未登录或token已过期"
msgstr "Non connecté ou jeton expiré"

msgid "没有提供标签"
msgstr "Aucune étiquette fournie"

msgid "邀请积分已更新"
msgstr "Points d'invitation mis à jour"

msgid "该名称已存在"
msgstr "Ce nom existe déjà"

msgid "令牌已过期"
msgstr "Le jeton a expiré"

msgid "项目名称已存在"
msgstr "Le nom du projet existe déjà"

msgid "设备未注册"
msgstr "Appareil non enregistré"

msgid "项目删除成功"
msgstr "Projet supprimé avec succès"

