# Spanish translations for your application.
# Copyright (C) 2023 ORGANIZATION
# This file is distributed under the same license as the project.
msgid ""
msgstr ""
"Project-Id-Version:  1.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-08-01 12:00+0000\n"
"PO-Revision-Date: 2025-04-06 22:45+0800\n"
"Last-Translator: AI Assistant\n"
"Language: es\n"
"Language-Team: es <<EMAIL>>\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

msgid "资源不存在"
msgstr "El recurso no existe"

msgid "消息删除成功"
msgstr "Mensaje eliminado con éxito"

msgid "用户信息更新成功"
msgstr "Información de usuario actualizada con éxito"

msgid "验证成功"
msgstr "Verificación exitosa"

#, python-brace-format
msgid "使用默认locale: {default_locale}"
msgstr "Usando configuración regional predeterminada: {default_locale}"

msgid "缺少更新数据"
msgstr "Faltan datos de actualización"

msgid "设备授权成功"
msgstr "Dispositivo autorizado con éxito"

msgid "消息标记为已读"
msgstr "Mensaje marcado como leído"

msgid "消息发送成功"
msgstr "Mensaje enviado con éxito"

msgid "该任务已完成"
msgstr "Esta tarea ya está completada"

msgid "登录成功"
msgstr "Inicio de sesión exitoso"

msgid "检测到你尚未加入服务器，请先加入我们的 Discord！"
msgstr "Detectamos que aún no te has unido al servidor, ¡por favor únete primero a nuestro Discord!"

msgid "没有找到匹配的设备"
msgstr "No se encontraron dispositivos coincidentes"

msgid "该用户已被邀请"
msgstr "Este usuario ya ha sido invitado"

msgid "注册失败，请稍后重试"
msgstr "Registro fallido, por favor intente más tarde"

msgid "获取设备项目服务配置失败"
msgstr "Error al obtener la configuración del servicio del proyecto del dispositivo"

msgid "项目配置不存在"
msgstr "La configuración del proyecto no existe"

msgid "获取用户信息失败: {user_resp.text}"
msgstr "Error al obtener la información del usuario: {user_resp.text}"

msgid "该项目不允许配置代理"
msgstr "Este proyecto no permite la configuración de proxy"

msgid "获取消息详情失败"
msgstr "Error al obtener los detalles del mensaje"

msgid "获取服务配置失败"
msgstr "Error al obtener la configuración del servicio"

msgid "服务停止：连续两个检测周期未收到运行指标"
msgstr "Servicio detenido: No se recibieron métricas de funcionamiento durante dos ciclos de detección consecutivos"

msgid "设备项目不存在"
msgstr "El proyecto del dispositivo no existe"

#, python-brace-format
msgid "成功授权 {len(associations)} 个设备"
msgstr "Autorización exitosa de {len(associations)} dispositivos"

msgid "文件名已存在"
msgstr "El nombre de archivo ya existe"

msgid "获取验证码图像错误"
msgstr "Error al obtener la imagen del código de verificación"

msgid "没有从 Discord 返回有效的 code 参数。"
msgstr "No se devolvió un parámetro de código válido desde Discord."

msgid "项目配置已存在"
msgstr "La configuración del proyecto ya existe"

msgid "输入验证错误"
msgstr "Error de validación de entrada"

msgid "注册成功"
msgstr "Registro exitoso"

msgid "无效的认证令牌"
msgstr "Token de autenticación inválido"

msgid "项目不存在"
msgstr "El proyecto no existe"

msgid "等待服务启动：配置已完成但尚未收到运行指标"
msgstr "Esperando el inicio del servicio: La configuración está completa pero aún no se han recibido métricas de funcionamiento"

msgid "缺少验证码"
msgstr "Falta el código de verificación"

msgid "获取设备列表失败"
msgstr "Error al obtener la lista de dispositivos"

msgid "项目状态与指标数据不一致"
msgstr "El estado del proyecto es inconsistente con los datos de métricas"

msgid "项目正在被设备使用，无法删除"
msgstr "El proyecto está siendo utilizado por dispositivos y no puede eliminarse"

msgid "设备不存在"
msgstr "El dispositivo no existe"

msgid "邮箱验证码"
msgstr "Código de verificación de correo electrónico"

msgid "用户名已存在"
msgstr "El nombre de usuario ya existe"

msgid "设备有正在初始化的项目服务"
msgstr "El dispositivo tiene servicios de proyecto en inicialización"

msgid "设备从未上报过运行指标"
msgstr "El dispositivo nunca ha reportado métricas de funcionamiento"

msgid "获取设备状态统计失败"
msgstr "Error al obtener estadísticas de estado del dispositivo"

msgid "已翻译消息: "
msgstr "Mensaje traducido: "

msgid "获取用户资料失败"
msgstr "Error al obtener el perfil del usuario"

msgid "设备 mac 地址已存在"
msgstr "La dirección MAC del dispositivo ya existe"

msgid "需要管理员权限"
msgstr "Se requieren permisos de administrador"

msgid "密码错误"
msgstr "Contraseña incorrecta"

msgid "token错误"
msgstr ""

msgid "设备创建成功"
msgstr "Dispositivo creado con éxito"

msgid "发送消息失败"
msgstr "Error al enviar el mensaje"

msgid "密码修改成功"
msgstr "Contraseña modificada con éxito"

msgid "修改密码失败"
msgstr "Error al modificar la contraseña"

msgid "获取消息列表失败"
msgstr "Error al obtener la lista de mensajes"

#, python-brace-format
msgid "无效的代币code: {token_code}"
msgstr "Código de token inválido: {token_code}"

#, python-brace-format
msgid "状态分析失败: {str(e)}"
msgstr "Análisis de estado fallido: {str(e)}"

msgid "设备有正在运行的项目服务"
msgstr "El dispositivo tiene servicios de proyecto en ejecución"

msgid "设备代理未配置"
msgstr "Proxy del dispositivo no configurado"

#, python-brace-format
msgid "设备已离线超过{offline_days}天"
msgstr "El dispositivo ha estado fuera de línea por más de {offline_days} días"

msgid "查询失败"
msgstr "Consulta fallida"

msgid "邮箱已存在"
msgstr "El correo electrónico ya existe"

msgid "设备项目配置不存在"
msgstr "La configuración del proyecto del dispositivo no existe"

msgid "设备删除成功"
msgstr "Dispositivo eliminado con éxito"

#, python-brace-format
msgid "无效的链代码: {chain_code}"
msgstr "Código de cadena inválido: {chain_code}"

msgid "项目服务正常运行中"
msgstr "El servicio del proyecto funciona normalmente"

msgid "获取设备令牌失败"
msgstr "Error al obtener el token del dispositivo"

#, python-brace-format
msgid "获取 Discord access_token 失败: {token_resp.text}"
msgstr "Error al obtener el token de acceso de Discord: {token_resp.text}"

msgid "缺少必要参数"
msgstr "Faltan parámetros necesarios"

msgid "没有提供更新数据"
msgstr "No se proporcionaron datos de actualización"

msgid "项目尚未完成初始化配置"
msgstr "La configuración de inicialización del proyecto no está completa"

msgid "获取用户列表失败，请稍后重试"
msgstr "Error al obtener la lista de usuarios, por favor intente más tarde"

msgid "服务器内部错误"
msgstr "Error interno del servidor"

msgid "无效的请求数据"
msgstr "Datos de solicitud inválidos"

msgid "无效的令牌"
msgstr "Token inválido"

msgid "操作失败"
msgstr "Operación fallida"

msgid "密钥必须是32字节"
msgstr "La clave debe ser de 32 bytes"

msgid "项目正在被设备使用，无法禁用"
msgstr ""
"El proyecto está siendo utilizado por dispositivos y no puede "
"deshabilitarse"

msgid "操作成功"
msgstr "Operación exitosa"

msgid "获取概览数据失败"
msgstr "Error al obtener datos de vista general"

msgid "项目名称已被使用"
msgstr "El nombre del proyecto ya está en uso"

msgid "登录失败，请稍后重试"
msgstr "Error al iniciar sesión, por favor intente más tarde"

msgid "项目配置删除成功"
msgstr "Configuración del proyecto eliminada con éxito"

msgid "更新已接收，但未处理"
msgstr "Actualización recibida, pero no procesada"

msgid "生成邀请链接失败"
msgstr "Error al generar enlace de invitación"

msgid "文件不存在"
msgstr "El archivo no existe"

msgid "base64编码的nonce"
msgstr "Nonce codificado en base64"

msgid "更新用户信息失败，请稍后重试"
msgstr "Error al actualizar la información del usuario, por favor intente más tarde"

msgid "删除设备失败"
msgstr "Error al eliminar el dispositivo"

#, python-brace-format
msgid "发送失败: {str(e)}"
msgstr "Envío fallido: {str(e)}"

msgid "任务类型不存在"
msgstr "El tipo de tarea no existe"

msgid "项目创建成功"
msgstr "Proyecto creado con éxito"

msgid "所有项目服务停止且存在未解决的错误状态"
msgstr "Todos los servicios del proyecto están detenidos y existen estados de error no resueltos"

msgid "权限不足"
msgstr "Permisos insuficientes"

msgid "service_compose 必须包含 services 字段"
msgstr "service_compose debe incluir el campo services"

msgid "service_compose 必须是一个对象"
msgstr "service_compose debe ser un objeto"

msgid "缺少认证令牌"
msgstr "Falta el token de autenticación"

msgid "没有权限访问该项目"
msgstr "Sin permiso para acceder a este proyecto"

msgid "令牌已被撤销"
msgstr "El token ha sido revocado"

msgid "生成服务配置失败"
msgstr "Error al generar la configuración del servicio"

msgid "所有消息已标记为已读"
msgstr "Todos los mensajes han sido marcados como leídos"

msgid "密钥未设置"
msgstr "Clave no establecida"

msgid "缺少必要字段：msg_type, subject, content"
msgstr "Faltan campos necesarios: msg_type, subject, content"

msgid "消息不存在"
msgstr "El mensaje no existe"

msgid "设备项目不属于该项目"
msgstr "El proyecto del dispositivo no pertenece a este proyecto"

#, python-brace-format
msgid "从Accept-Language获取locale: {locale}"
msgstr "Obteniendo configuración regional desde Accept-Language: {locale}"

#, python-brace-format
msgid "从查询参数获取locale: {locale}"
msgstr "Obteniendo configuración regional desde parámetros de consulta: {locale}"

msgid "删除消息失败"
msgstr "Error al eliminar el mensaje"

msgid "解密失败"
msgstr "Descifrado fallido"

msgid "请勿重复创建钱包"
msgstr "No cree la billetera nuevamente"

#, python-brace-format
msgid "JSON解析错误: {str(e)}"
msgstr "Error de análisis JSON: {str(e)}"

#, python-brace-format
msgid "翻译响应消息时出错: {str(e)}"
msgstr "Error al traducir el mensaje de respuesta: {str(e)}"

msgid "钱包创建失败"
msgstr "Error al crear la billetera"

msgid "重新生成系统应用配置失败"
msgstr "Error al regenerar la configuración de la aplicación del sistema"

msgid "设备未分配服务配置"
msgstr "Configuración de servicio no asignada al dispositivo"

msgid "设备未配置系统应用"
msgstr "El dispositivo no está configurado con la aplicación del sistema"

msgid "项目未被删除"
msgstr "El proyecto no fue eliminado"

msgid "缺少必要字段"
msgstr "Faltan campos necesarios"

msgid "系统应用配置重新生成成功"
msgstr "Configuración de la aplicación del sistema regenerada con éxito"

#, python-brace-format
msgid "成功授权 {count} 个设备"
msgstr "Autorización exitosa de {count} dispositivos"

msgid "项目代理未配置"
msgstr "Proxy del proyecto no configurado"

msgid "邮箱格式错误"
msgstr "Formato de correo electrónico incorrecto"

msgid "设备更新成功"
msgstr "Dispositivo actualizado con éxito"

msgid "未能获取到 access_token"
msgstr "No se pudo obtener el token de acceso"

msgid "base64编码的加密MAC地址"
msgstr "Dirección MAC cifrada codificada en base64"

msgid "项目更新成功"
msgstr "Proyecto actualizado con éxito"

msgid "获取设备详情失败"
msgstr "Error al obtener los detalles del dispositivo"

msgid "邀请记录不存在"
msgstr "El registro de invitación no existe"

msgid "更新设备失败"
msgstr "Error al actualizar el dispositivo"

msgid "无效的 MAC 地址"
msgstr "Dirección MAC inválida"

msgid "服务配置不存在"
msgstr "La configuración del servicio no existe"

msgid "缺少用户ID"
msgstr "Falta el ID de usuario"

msgid "无法找到用户"
msgstr "No se puede encontrar al usuario"

msgid "用户不存在"
msgstr "El usuario no existe"

msgid "所有的项目都是 created 状态"
msgstr "Todos los proyectos están en estado 'creado'"

msgid "该用户已被授权访问此设备"
msgstr "Este usuario ya está autorizado para acceder a este dispositivo"

msgid "生成 docker-compose 配置失败"
msgstr "Error al generar la configuración de docker-compose"

msgid "需要新的认证令牌"
msgstr "Se requiere un nuevo token de autenticación"

msgid "创建设备失败"
msgstr "Error al crear el dispositivo"

msgid "验证码错误或已过期"
msgstr "Código de verificación incorrecto o caducado"

msgid "验证码发送成功"
msgstr "Código de verificación enviado con éxito"

msgid "名称和 docker-compose 配置为必填项"
msgstr "El nombre y la configuración de docker-compose son obligatorios"

#, python-brace-format
msgid "数据验证失败: {e.message}"
msgstr "Validación de datos fallida: {e.message}"

msgid "未登录或token已过期"
msgstr "No ha iniciado sesión o el token ha caducado"

msgid "没有提供标签"
msgstr "No se proporcionaron etiquetas"

msgid "邀请积分已更新"
msgstr "Puntos de invitación actualizados"

msgid "该名称已存在"
msgstr "Este nombre ya existe"

msgid "令牌已过期"
msgstr "El token ha caducado"

msgid "项目名称已存在"
msgstr "El nombre del proyecto ya existe"

msgid "设备未注册"
msgstr "Dispositivo no registrado"

msgid "项目删除成功"
msgstr "Proyecto eliminado con éxito"

