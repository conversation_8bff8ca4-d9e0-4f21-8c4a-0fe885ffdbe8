# Korean translations for your application.
# Copyright (C) 2023 ORGANIZATION
# This file is distributed under the same license as the project.
msgid ""
msgstr ""
"Project-Id-Version:  1.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-08-01 12:00+0000\n"
"PO-Revision-Date: 2025-04-06 22:57+0800\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: ko\n"
"Language-Team: ko <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

msgid "资源不存在"
msgstr "리소스가 존재하지 않습니다"

msgid "消息删除成功"
msgstr "메시지가 성공적으로 삭제되었습니다"

msgid "用户信息更新成功"
msgstr "사용자 정보가 성공적으로 업데이트되었습니다"

msgid "验证成功"
msgstr "인증 성공"

#, python-brace-format
msgid "使用默认locale: {default_locale}"
msgstr "기본 로케일 사용: {default_locale}"

msgid "缺少更新数据"
msgstr "업데이트 데이터가 누락되었습니다"

msgid "设备授权成功"
msgstr "기기 인증 성공"

msgid "消息标记为已读"
msgstr "메시지가 읽음으로 표시되었습니다"

msgid "消息发送成功"
msgstr "메시지가 성공적으로 전송되었습니다"

msgid "该任务已完成"
msgstr "이 작업은 이미 완료되었습니다"

msgid "登录成功"
msgstr "로그인 성공"

msgid "检测到你尚未加入服务器，请先加入我们的 Discord！"
msgstr ""

msgid "没有找到匹配的设备"
msgstr "일치하는 기기를 찾을 수 없습니다"

msgid "该用户已被邀请"
msgstr "이 사용자는 이미 초대되었습니다"

msgid "注册失败，请稍后重试"
msgstr "등록에 실패했습니다. 나중에 다시 시도해 주세요"

msgid "获取设备项目服务配置失败"
msgstr "기기 프로젝트 서비스 구성을 가져오지 못했습니다"

msgid "项目配置不存在"
msgstr "프로젝트 구성이 존재하지 않습니다"

#, python-brace-format
msgid "获取用户信息失败: {user_resp.text}"
msgstr ""

msgid "该项目不允许配置代理"
msgstr "이 프로젝트는 프록시 구성을 허용하지 않습니다"

msgid "获取消息详情失败"
msgstr "메시지 세부 정보를 가져오지 못했습니다"

msgid "获取服务配置失败"
msgstr "서비스 구성을 가져오지 못했습니다"

msgid "服务停止：连续两个检测周期未收到运行指标"
msgstr "서비스 중지: 연속 두 감지 주기 동안 운영 지표를 수신하지 못했습니다"

msgid "设备项目不存在"
msgstr "기기 프로젝트가 존재하지 않습니다"

#, python-brace-format
msgid "成功授权 {len(associations)} 个设备"
msgstr "{len(associations)}개의 기기를 성공적으로 인증했습니다"

msgid "文件名已存在"
msgstr "파일 이름이 이미 존재합니다"

msgid "获取验证码图像错误"
msgstr "인증 코드 이미지를 가져오는 중 오류가 발생했습니다"

msgid "没有从 Discord 返回有效的 code 参数。"
msgstr ""

msgid "项目配置已存在"
msgstr "프로젝트 구성이 이미 존재합니다"

msgid "输入验证错误"
msgstr "입력 유효성 검사 오류"

msgid "注册成功"
msgstr "등록 성공"

msgid "无效的认证令牌"
msgstr "유효하지 않은 인증 토큰"

msgid "项目不存在"
msgstr "프로젝트가 존재하지 않습니다"

msgid "等待服务启动：配置已完成但尚未收到运行指标"
msgstr "서비스 시작 대기 중: 구성은 완료되었지만 운영 지표가 아직 수신되지 않았습니다"

msgid "缺少验证码"
msgstr "인증 코드가 누락되었습니다"

msgid "获取设备列表失败"
msgstr "기기 목록을 가져오지 못했습니다"

msgid "项目状态与指标数据不一致"
msgstr "프로젝트 상태와 지표 데이터가 일치하지 않습니다"

msgid "项目正在被设备使用，无法删除"
msgstr "프로젝트가 기기에서 사용 중이므로 삭제할 수 없습니다"

msgid "设备不存在"
msgstr "기기가 존재하지 않습니다"

msgid "邮箱验证码"
msgstr "이메일 인증 코드"

msgid "用户名已存在"
msgstr "사용자 이름이 이미 존재합니다"

msgid "设备有正在初始化的项目服务"
msgstr "기기에 초기화 중인 프로젝트 서비스가 있습니다"

msgid "设备从未上报过运行指标"
msgstr "기기가 운영 지표를 보고한 적이 없습니다"

msgid "获取设备状态统计失败"
msgstr "기기 상태 통계를 가져오지 못했습니다"

msgid "已翻译消息: "
msgstr "번역된 메시지: "

msgid "获取用户资料失败"
msgstr "사용자 프로필을 가져오지 못했습니다"

msgid "设备 mac 地址已存在"
msgstr "기기 MAC 주소가 이미 존재합니다"

msgid "需要管理员权限"
msgstr "관리자 권한이 필요합니다"

msgid "密码错误"
msgstr "비밀번호가 올바르지 않습니다"

msgid "token错误"
msgstr ""

msgid "设备创建成功"
msgstr "기기가 성공적으로 생성되었습니다"

msgid "发送消息失败"
msgstr "메시지 전송에 실패했습니다"

msgid "密码修改成功"
msgstr "비밀번호가 성공적으로 변경되었습니다"

msgid "修改密码失败"
msgstr "비밀번호 변경에 실패했습니다"

msgid "获取消息列表失败"
msgstr "메시지 목록을 가져오지 못했습니다"

#, python-brace-format
msgid "无效的代币code: {token_code}"
msgstr ""

#, python-brace-format
msgid "状态分析失败: {str(e)}"
msgstr "상태 분석 실패: {str(e)}"

msgid "设备有正在运行的项目服务"
msgstr "기기에 실행 중인 프로젝트 서비스가 있습니다"

msgid "设备代理未配置"
msgstr "기기 프록시가 구성되지 않았습니다"

#, python-brace-format
msgid "设备已离线超过{offline_days}天"
msgstr "기기가 {offline_days}일 이상 오프라인 상태입니다"

msgid "查询失败"
msgstr ""

msgid "邮箱已存在"
msgstr "이메일이 이미 존재합니다"

msgid "设备项目配置不存在"
msgstr "기기 프로젝트 구성이 존재하지 않습니다"

msgid "设备删除成功"
msgstr "기기가 성공적으로 삭제되었습니다"

#, python-brace-format
msgid "无效的链代码: {chain_code}"
msgstr ""

msgid "项目服务正常运行中"
msgstr "프로젝트 서비스가 정상적으로 실행 중입니다"

msgid "获取设备令牌失败"
msgstr "기기 토큰을 가져오지 못했습니다"

#, python-brace-format
msgid "获取 Discord access_token 失败: {token_resp.text}"
msgstr ""

msgid "缺少必要参数"
msgstr "필수 매개변수가 누락되었습니다"

msgid "没有提供更新数据"
msgstr "업데이트 데이터가 제공되지 않았습니다"

msgid "项目尚未完成初始化配置"
msgstr "프로젝트 초기화 구성이 완료되지 않았습니다"

msgid "获取用户列表失败，请稍后重试"
msgstr "사용자 목록을 가져오지 못했습니다. 나중에 다시 시도해 주세요"

msgid "服务器内部错误"
msgstr "서버 내부 오류"

msgid "无效的请求数据"
msgstr "유효하지 않은 요청 데이터"

msgid "无效的令牌"
msgstr "유효하지 않은 토큰"

msgid "操作失败"
msgstr "작업 실패"

msgid "密钥必须是32字节"
msgstr "키는 32바이트여야 합니다"

msgid "项目正在被设备使用，无法禁用"
msgstr "프로젝트가 기기에서 사용 중이므로 비활성화할 수 없습니다"

msgid "操作成功"
msgstr "작업 성공"

msgid "获取概览数据失败"
msgstr "개요 데이터를 가져오지 못했습니다"

msgid "项目名称已被使用"
msgstr "프로젝트 이름이 이미 사용 중입니다"

msgid "登录失败，请稍后重试"
msgstr "로그인에 실패했습니다. 나중에 다시 시도해 주세요"

msgid "项目配置删除成功"
msgstr "프로젝트 구성이 성공적으로 삭제되었습니다"

msgid "更新已接收，但未处理"
msgstr ""

msgid "生成邀请链接失败"
msgstr ""

msgid "文件不存在"
msgstr "파일이 존재하지 않습니다"

msgid "base64编码的nonce"
msgstr "Base64로 인코딩된 nonce"

msgid "更新用户信息失败，请稍后重试"
msgstr "사용자 정보 업데이트에 실패했습니다. 나중에 다시 시도해 주세요"

msgid "删除设备失败"
msgstr "기기 삭제에 실패했습니다"

#, python-brace-format
msgid "发送失败: {str(e)}"
msgstr "전송 실패: {str(e)}"

msgid "任务类型不存在"
msgstr "작업 유형이 존재하지 않습니다"

msgid "项目创建成功"
msgstr "프로젝트가 성공적으로 생성되었습니다"

msgid "所有项目服务停止且存在未解决的错误状态"
msgstr "모든 프로젝트 서비스가 중지되었으며 해결되지 않은 오류 상태가 있습니다"

msgid "权限不足"
msgstr "권한이 부족합니다"

msgid "service_compose 必须包含 services 字段"
msgstr "service_compose에는 services 필드가 포함되어야 합니다"

msgid "service_compose 必须是一个对象"
msgstr "service_compose는 객체여야 합니다"

msgid "缺少认证令牌"
msgstr "인증 토큰이 누락되었습니다"

msgid "没有权限访问该项目"
msgstr "이 프로젝트에 접근할 권한이 없습니다"

msgid "令牌已被撤销"
msgstr "토큰이 취소되었습니다"

msgid "生成服务配置失败"
msgstr "서비스 구성 생성에 실패했습니다"

msgid "所有消息已标记为已读"
msgstr "모든 메시지가 읽음으로 표시되었습니다"

msgid "密钥未设置"
msgstr ""

msgid "缺少必要字段：msg_type, subject, content"
msgstr "필수 필드가 누락되었습니다: msg_type, subject, content"

msgid "消息不存在"
msgstr "메시지가 존재하지 않습니다"

msgid "设备项目不属于该项目"
msgstr "기기 프로젝트가 이 프로젝트에 속하지 않습니다"

#, python-brace-format
msgid "从Accept-Language获取locale: {locale}"
msgstr "Accept-Language에서 로케일 가져오기: {locale}"

#, python-brace-format
msgid "从查询参数获取locale: {locale}"
msgstr "쿼리 매개변수에서 로케일 가져오기: {locale}"

msgid "删除消息失败"
msgstr "메시지 삭제에 실패했습니다"

msgid "解密失败"
msgstr "복호화 실패"

msgid "请勿重复创建钱包"
msgstr ""

#, python-brace-format
msgid "JSON解析错误: {str(e)}"
msgstr "JSON 구문 분석 오류: {str(e)}"

#, python-brace-format
msgid "翻译响应消息时出错: {str(e)}"
msgstr "응답 메시지 번역 중 오류 발생: {str(e)}"

#, fuzzy
msgid "钱包创建失败"
msgstr "기기 생성에 실패했습니다"

msgid "重新生成系统应用配置失败"
msgstr "시스템 애플리케이션 구성 재생성에 실패했습니다"

msgid "设备未分配服务配置"
msgstr "기기에 서비스 구성이 할당되지 않았습니다"

msgid "设备未配置系统应用"
msgstr "기기에 시스템 애플리케이션이 구성되지 않았습니다"

msgid "项目未被删除"
msgstr "프로젝트가 삭제되지 않았습니다"

msgid "缺少必要字段"
msgstr "필수 필드가 누락되었습니다"

msgid "系统应用配置重新生成成功"
msgstr "시스템 애플리케이션 구성이 성공적으로 재생성되었습니다"

#, python-brace-format
msgid "成功授权 {count} 个设备"
msgstr "{count}개의 기기를 성공적으로 인증했습니다"

msgid "项目代理未配置"
msgstr "프로젝트 프록시가 구성되지 않았습니다"

msgid "邮箱格式错误"
msgstr "이메일 형식이 잘못되었습니다"

msgid "设备更新成功"
msgstr "기기가 성공적으로 업데이트되었습니다"

msgid "未能获取到 access_token"
msgstr ""

msgid "base64编码的加密MAC地址"
msgstr "Base64로 인코딩된 암호화된 MAC 주소"

msgid "项目更新成功"
msgstr "프로젝트가 성공적으로 업데이트되었습니다"

msgid "获取设备详情失败"
msgstr "기기 세부 정보를 가져오지 못했습니다"

msgid "邀请记录不存在"
msgstr "초대 기록이 존재하지 않습니다"

msgid "更新设备失败"
msgstr "기기 업데이트에 실패했습니다"

msgid "无效的 MAC 地址"
msgstr "유효하지 않은 MAC 주소"

msgid "服务配置不存在"
msgstr "서비스 구성이 존재하지 않습니다"

msgid "缺少用户ID"
msgstr "사용자 ID가 누락되었습니다"

msgid "无法找到用户"
msgstr "사용자를 찾을 수 없습니다"

msgid "用户不存在"
msgstr "사용자가 존재하지 않습니다"

msgid "所有的项目都是 created 状态"
msgstr "모든 프로젝트가 '생성됨' 상태입니다"

msgid "该用户已被授权访问此设备"
msgstr "이 사용자는 이미 이 기기에 접근할 권한이 있습니다"

msgid "生成 docker-compose 配置失败"
msgstr "docker-compose 구성 생성에 실패했습니다"

msgid "需要新的认证令牌"
msgstr "새 인증 토큰이 필요합니다"

msgid "创建设备失败"
msgstr "기기 생성에 실패했습니다"

msgid "验证码错误或已过期"
msgstr "인증 코드가 잘못되었거나 만료되었습니다"

msgid "验证码发送成功"
msgstr "인증 코드가 성공적으로 전송되었습니다"

msgid "名称和 docker-compose 配置为必填项"
msgstr "이름과 docker-compose 구성은 필수 항목입니다"

#, python-brace-format
msgid "数据验证失败: {e.message}"
msgstr "데이터 유효성 검사 실패: {e.message}"

msgid "未登录或token已过期"
msgstr "로그인하지 않았거나 토큰이 만료되었습니다"

msgid "没有提供标签"
msgstr "태그가 제공되지 않았습니다"

msgid "邀请积分已更新"
msgstr "초대 포인트가 업데이트되었습니다"

msgid "该名称已存在"
msgstr "이 이름은 이미 존재합니다"

msgid "令牌已过期"
msgstr "토큰이 만료되었습니다"

msgid "项目名称已存在"
msgstr "프로젝트 이름이 이미 존재합니다"

msgid "设备未注册"
msgstr "기기가 등록되지 않았습니다"

msgid "项目删除成功"
msgstr "프로젝트가 성공적으로 삭제되었습니다"

