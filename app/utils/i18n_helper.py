import json
import re
import logging
from flask import Response, current_app, request
from flask_babel import gettext as _

# 设置日志记录器
logger = logging.getLogger(__name__)

# 定义locale选择函数
def get_locale():
    """
    获取用户首选的语言区域
    
    根据查询参数和请求头确定用户的语言偏好
    
    Returns:
        str: 语言区域代码，例如'en'、'zh_CN'等
    """
    # 1. 首先从查询参数获取locale
    locale = request.args.get('locale')
    if locale:
        #logger.debug(f"从查询参数获取locale: {locale}")
        return locale
        
    # 2. 从请求头获取Accept-Language
    if not locale:
        accept_language = request.headers.get('Accept-Language')
        if accept_language:
            # 提取第一个语言偏好
            locale = accept_language.split(',')[0].split(';')[0].replace('-', '_')
            #logger.debug(f"从Accept-Language获取locale: {locale}")
            return locale
            
    # 3. 使用默认语言
    default_locale = current_app.config.get('BABEL_DEFAULT_LOCALE', 'zh_CN')
    #logger.debug(f"使用默认locale: {default_locale}")
    return default_locale

def translate_response_message(response: Response) -> Response:
    """
    对Flask响应中的message字段进行国际化翻译
    
    Args:
        response: Flask响应对象
    
    Returns:
        处理后的响应对象
    """
    # 仅处理JSON响应
    if not response.is_json:
        return response
        
    try:
        # 解析响应数据
        data = json.loads(response.get_data(as_text=True))
        
        # 检查是否包含message字段
        if 'message' in data and isinstance(data['message'], str):
            original_message = data['message']
            # 翻译消息
            data['message'] = _(original_message)
            
            # 更新响应数据
            response.set_data(json.dumps(data, ensure_ascii=False))
            
            # 调试日志
            #if original_message != data['message']:
                # logger.debug(f"已翻译消息: '{original_message}' -> '{data['message']}'")
            
    except json.JSONDecodeError as e:
        logger.error(f"JSON解析错误: {str(e)}")
    except Exception as e:
        # 如果处理失败，记录错误但返回原始响应
        logger.error(f"翻译响应消息时出错: {str(e)}")
        
    return response