
def mask_email(email: str) -> str:
    if not email or '@' not in email:
        return email

    username, domain = email.split('@', 1)

    # 脱敏用户名
    username = username[0] + '*' * 5

    # 脱敏域名
    if '.' in domain:
        parts = domain.split('.')
        main_domain = parts[0]
        tld = '.'.join(parts[1:])

        masked_domain = main_domain[0] + '*' * 5
        masked_domain = f"{masked_domain}.{tld}"
    else:
        masked_domain = domain[0] + '*' * (len(domain) - 1)

    return f"{username}@{masked_domain}"

if __name__ == "__main__":
    email = "<EMAIL>"
    print(mask_email(email))