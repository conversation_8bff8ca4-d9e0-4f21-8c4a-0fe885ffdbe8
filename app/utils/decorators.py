from functools import wraps

from flask import current_app

from app.models.base import db
from app.utils.errors import BusinessException
from app.utils.exceptions import PermissionError
from app.utils.response import Response


def handle_service_errors(func):
    """Service 层错误处理装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except PermissionError:
            raise
        except Exception as e:
            db.session.rollback()
            return None, str(e)
    return wrapper

def handle_api_errors(func):
    """API 层错误处理装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            result = func(*args, **kwargs)
            return result
        except PermissionError as e:
            return Response.error(str(e), 403)
        except BusinessException as e:
            current_app.logger.error(f"{func.__name__} BusinessException: {str(e)}")
            return Response.success(data=None, message=e.message, code=e.code)
        except Exception as e:
            current_app.logger.error(f"{func.__name__} error: {str(e)}")
            return Response.error(str(e), 500)
    return wrapper
