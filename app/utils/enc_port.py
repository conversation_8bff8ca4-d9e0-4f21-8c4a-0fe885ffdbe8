import os
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.backends import default_backend

NONCE_LENGTH = 12

class EncPort:
    _instance = None

    def __init__(self):
        self.aes_key = None
        self.nonce_size = 0
        self.seed = os.getenv('ENC_PORT_SEED', 'UQ8yv92zcRxbVOuOnm7k58bJ9hndm94g')
        digest = hashes.Hash(hashes.SHA256(), backend=default_backend())
        digest.update(self.seed.encode())
        self.aes_key = digest.finalize()

        # 创建AES-GCM实例

        self.nonce_size = NONCE_LENGTH

    @classmethod
    def get_instance(cls):
        if not cls._instance:
            cls._instance = EncPort()
        return cls._instance


    def encrypt(self, plaintext: bytes) -> bytes:
        if not self.aes_key:
            raise ValueError("密钥未设置")
        nonce = os.urandom(self.nonce_size)
        cipher = Cipher(
            algorithms.AES(self.aes_key),
            modes.GCM(nonce),
            backend=default_backend()
        )
        encryptor = cipher.encryptor()
        ciphertext = encryptor.update(plaintext) + encryptor.finalize()
        tag = encryptor.tag
        return nonce + tag + ciphertext

    def decrypt(self, ciphertext: bytes) -> bytes:
        if not self.aes_key:
            raise ValueError("密钥未设置")

        nonce = ciphertext[:self.nonce_size]
        tag = ciphertext[self.nonce_size:self.nonce_size + 16]
        ciphertext_body = ciphertext[self.nonce_size+16:]

        cipher = Cipher(
            algorithms.AES(self.aes_key),
            modes.GCM(nonce, tag),
            backend=default_backend()
        )
        decryptor = cipher.decryptor()
        decrypted = decryptor.update(ciphertext_body)
        decrypted += decryptor.finalize()
        return decrypted
