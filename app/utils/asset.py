from decimal import Decimal

MAKER_FEE_RATE = Decimal('0.002')  # Maker 0.2%
TAKER_FEE_RATE = Decimal('0.003')  # Taker 0.3%


def calculate_order_freeze(amount: Decimal, price: Decimal, fee_percent: Decimal, order_type: str) -> Decimal:
    """计算订单冻结金额"""
    if order_type == 'SELL':  # 卖单
        # 冻结基础资产数量 + 手续费（基础资产）
        freeze_amount = amount + (amount * fee_percent)
        return freeze_amount
    elif order_type == 'BUY':  # 买单
        # 计价资产的数量
        quote_asset_amount = amount * price
        # 冻结计价资产数量 + 手续费（计价资产）
        freeze_amount = quote_asset_amount + (quote_asset_amount * fee_percent)
        return freeze_amount
    else:
        raise ValueError("Invalid order_type. Must be 'BUY' or 'SELL'.")


def calculate_order_fee(amount: Decimal, price: Decimal, fee_percent: Decimal, order_type: str) -> Decimal:
    """计算订单手续费"""
    if order_type == 'SELL':  # 卖单，手续费为基础资产
        fee = amount * fee_percent
        return fee
    elif order_type == 'BUY':  # 买单，手续费为计价资产
        quote_asset_amount = amount * price
        fee = quote_asset_amount * fee_percent
        return fee
    else:
        raise ValueError("Invalid order_type. Must be 'BUY' or 'SELL'.")
