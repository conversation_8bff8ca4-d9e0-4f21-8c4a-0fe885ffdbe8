import base64
import os
import hashlib
from bip32utils import B<PERSON><PERSON><PERSON><PERSON>, BIP32_HARDEN
from cryptography.hazmat.primitives.ciphers.aead import AESGCM


class Crypto:
    def __init__(self, key: str):
        """初始化加密工具

        Args:
            key: 32字节的加密密钥
        """
        if len(key) != 32:
            raise ValueError("密钥必须是32字节")
        self.key = key.encode()
        self.aesgcm = AESGCM(self.key)

    def encrypt(self, data: bytes) -> tuple[str, str]:
        """加密数据

        Args:
            data: 要加密的数据

        Returns:
            (encrypted, nonce): 加密后的数据和nonce的base64编码
        """
        nonce = os.urandom(12)  # 生成12字节的nonce
        encrypted = self.aesgcm.encrypt(nonce, data, None)
        return (
            base64.b64encode(encrypted).decode(),
            base64.b64encode(nonce).decode()
        )

    def decrypt(self, encrypted: str, nonce: str) -> bytes:
        """解密数据

        Args:
            encrypted: base64编码的加密数据
            nonce: base64编码的nonce

        Returns:
            解密后的数据
        """
        encrypted_bytes = base64.b64decode(encrypted)
        nonce_bytes = base64.b64decode(nonce)
        return self.aesgcm.decrypt(nonce_bytes, encrypted_bytes, None)


def get_crypto():
    """获取加密工具实例"""
    from flask import current_app
    return Crypto(current_app.config['ENCRYPTION_KEY'])

def decrypt_data(encrypted: str, nonce: str) -> str:
    """解密数据

    Args:
        encrypted: base64编码的加密数据
        nonce: base64编码的nonce

    Returns:
        解密后的数据字符串
    """
    crypto = get_crypto()
    decrypted = crypto.decrypt(encrypted, nonce)
    return decrypted.decode()

def encrypt_data(data: str) -> tuple[str, str]:
    """加密数据

    Args:
        data: 要加密的数据字符串

    Returns:
        (encrypted, nonce): 加密后的数据和nonce的base64编码
    """
    crypto = get_crypto()
    return crypto.encrypt(data.encode())

def derive_master_key_bytes(seed: bytes, salt: bytes) -> bytes:
        """
        使用PBKDF2-HMAC-SHA256派生主密钥

        参数：
            seed: 原始种子字节
        返回：
            32字节的主派生密钥
        """
        dklen = 32
        iterations = 2048

        # 执行主密钥派生
        return hashlib.pbkdf2_hmac(
            'sha256',
            seed,
            salt,
            iterations,
            dklen
        )


def derive_bip44_key(master_key: BIP32Key,
                     coin_type: int,
                     account: int = 0,
                     change: int = 0,
                     address_index: int = 0) -> BIP32Key:
    """
    按BIP44路径进行层级派生：m/44'/coin_type'/account'/change/address_index

    参数：
        master_key: 主密钥BIP32对象
    返回：
        最终派生的子密钥对象
    """
    purpose = master_key.ChildKey(44 + BIP32_HARDEN)
    coin_type_key = purpose.ChildKey(coin_type + BIP32_HARDEN)
    account_key = coin_type_key.ChildKey(account + BIP32_HARDEN)
    change_key = account_key.ChildKey(change)
    child_key = change_key.ChildKey(address_index)
    return child_key


def generate_child_key(seed: bytes,
                       salt: bytes,
                       coin_type: int,
                       account: int = 0,
                       change: int = 0,
                       address_index: int = 0) -> BIP32Key:
    # 主密钥派生
    derived_key_bytes = derive_master_key_bytes(seed, salt)

    # BIP44派生路径
    master_key = BIP32Key.fromEntropy(derived_key_bytes)
    child_key = derive_bip44_key(master_key, coin_type, account, change, address_index)
    return child_key