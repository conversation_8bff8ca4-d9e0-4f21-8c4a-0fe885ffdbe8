from datetime import datetime, timedelta
from functools import wraps

import jwt
from flask import current_app, jsonify, request
from flask_jwt_extended import get_jwt, get_jwt_identity, verify_jwt_in_request

from app.models.base import db
from app.models.user import User
from app.utils.response import Response


def get_current_user():
    """获取当前用户"""
    user_id = get_jwt_identity()
    return db.session.get(User, user_id)

def generate_token(user_id):
    """生成JWT token"""
    payload = {
        "user_id": user_id,
        "exp": datetime.utcnow() + timedelta(days=1)
    }
    return jwt.encode(payload, current_app.config["SECRET_KEY"], algorithm="HS256")

def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = request.headers.get("Authorization")
        if not token:
            return jsonify({"message": "Token is missing"}), 401

        try:
            token = token.split("Bearer ")[1]
            data = jwt.decode(token, current_app.config["SECRET_KEY"], algorithms=["HS256"])
            current_user = db.session.get(User, data["user_id"])
            if not current_user:
                return jsonify({"message": "User not found"}), 401
        except Exception:
            return jsonify({"message": "Invalid token"}), 401

        return f(current_user, *args, **kwargs)
    return decorated

def admin_required():
    """管理员权限装饰器"""
    def wrapper(fn):
        @wraps(fn)
        def decorator(*args, **kwargs):
            verify_jwt_in_request()
            data = get_jwt()
            current_user = db.session.get(User, data["user_id"])
            if not current_user or not current_user.is_admin:
                return Response.error("权限不足", 403)
            return fn(*args, **kwargs)
        return decorator
    return wrapper
