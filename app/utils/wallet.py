"""
钱包相关的常量和工具类
保留ABI常量，移除枚举类，改为使用数据库模型
"""
from app.models.blockchain import TokenType

ERC20_ABI = [
    {
        "constant": True,
        "inputs": [],
        "name": "totalSupply",
        "outputs": [{"name": "", "type": "uint256"}],
        "payable": False,
        "stateMutability": "view",
        "type": "function"
    },
    {
        "constant": True,
        "inputs": [{"name": "owner", "type": "address"}],
        "name": "balanceOf",
        "outputs": [{"name": "", "type": "uint256"}],
        "payable": False,
        "stateMutability": "view",
        "type": "function"
    },
    {
        "constant": True,
        "inputs": [{"name": "owner", "type": "address"}, {"name": "spender", "type": "address"}],
        "name": "allowance",
        "outputs": [{"name": "", "type": "uint256"}],
        "payable": False,
        "stateMutability": "view",
        "type": "function"
    },
    {
        "constant": False,
        "inputs": [{"name": "recipient", "type": "address"}, {"name": "amount", "type": "uint256"}],
        "name": "transfer",
        "outputs": [{"name": "", "type": "bool"}],
        "payable": False,
        "stateMutability": "nonpayable",
        "type": "function"
    },
    {
        "constant": False,
        "inputs": [{"name": "sender", "type": "address"}, {"name": "recipient", "type": "address"},
                   {"name": "amount", "type": "uint256"}],
        "name": "transferFrom",
        "outputs": [{"name": "", "type": "bool"}],
        "payable": False,
        "stateMutability": "nonpayable",
        "type": "function"
    },
    {
        "constant": False,
        "inputs": [{"name": "spender", "type": "address"}, {"name": "amount", "type": "uint256"}],
        "name": "approve",
        "outputs": [{"name": "", "type": "bool"}],
        "payable": False,
        "stateMutability": "nonpayable",
        "type": "function"
    },
    {
        "constant": True,
        "inputs": [],
        "name": "name",
        "outputs": [{"name": "", "type": "string"}],
        "payable": False,
        "stateMutability": "view",
        "type": "function"
    },
    {
        "constant": True,
        "inputs": [],
        "name": "symbol",
        "outputs": [{"name": "", "type": "string"}],
        "payable": False,
        "stateMutability": "view",
        "type": "function"
    },
    {
        "constant": True,
        "inputs": [],
        "name": "decimals",
        "outputs": [{"name": "", "type": "uint8"}],
        "payable": False,
        "stateMutability": "view",
        "type": "function"
    }
]

ERC1363_ABI = [
    {
        "constant": False,
        "inputs": [{"name": "recipient", "type": "address"}, {"name": "amount", "type": "uint256"},
                   {"name": "data", "type": "bytes"}],
        "name": "transferAndCall",
        "outputs": [{"name": "", "type": "bool"}],
        "payable": False,
        "stateMutability": "nonpayable",
        "type": "function"
    },
    {
        "constant": False,
        "inputs": [{"name": "sender", "type": "address"}, {"name": "recipient", "type": "address"},
                   {"name": "amount", "type": "uint256"}, {"name": "data", "type": "bytes"}],
        "name": "transferFromAndCall",
        "outputs": [{"name": "", "type": "bool"}],
        "payable": False,
        "stateMutability": "nonpayable",
        "type": "function"
    },
    {
        "constant": False,
        "inputs": [{"name": "spender", "type": "address"}, {"name": "amount", "type": "uint256"},
                   {"name": "data", "type": "bytes"}],
        "name": "approveAndCall",
        "outputs": [{"name": "", "type": "bool"}],
        "payable": False,
        "stateMutability": "nonpayable",
        "type": "function"
    },
    {
        "constant": True,
        "inputs": [{"name": "operator", "type": "address"}, {"name": "from", "type": "address"},
                   {"name": "value", "type": "uint256"}, {"name": "data", "type": "bytes"}],
        "name": "onTransferReceived",
        "outputs": [{"name": "", "type": "bytes4"}],
        "payable": False,
        "stateMutability": "pure",
        "type": "function"
    }
]

ERC1363_ABI.extend(ERC20_ABI)


def get_token_abi(token_type_value):
    """根据代币类型获取对应的ABI"""
    if token_type_value == TokenType.NATIVE:
        return None  # 原生代币不需要ABI
    elif token_type_value == TokenType.ERC20:
        return ERC20_ABI
    elif token_type_value == TokenType.SPL:
        return None  # SPL代币有自己的处理方式
    else:
        return None
