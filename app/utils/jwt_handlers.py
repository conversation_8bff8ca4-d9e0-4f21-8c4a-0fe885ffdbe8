"""JWT 错误处理器模块"""

from app.utils.response import Response


def handle_expired_token_error(jwt_header, jwt_data):
    """处理令牌过期错误"""
    return Response.error("令牌已过期", 401)


def handle_invalid_token_error(error_string):
    """处理无效令牌错误"""
    return Response.error("无效的令牌", 401)


def handle_missing_token_error(error_string):
    """处理缺少令牌错误"""
    return Response.error("缺少认证令牌", 401)


def handle_revoked_token_error(jwt_header, jwt_data):
    """处理已撤销令牌错误"""
    return Response.error("令牌已被撤销", 401)


def handle_fresh_token_required_error(jwt_header, jwt_data):
    """处理需要新令牌错误"""
    return Response.error("需要新的认证令牌", 401)


def handle_user_lookup_error(jwt_header, jwt_data):
    """处理用户查找错误"""
    return Response.error("无法找到用户", 401)


def register_jwt_error_handlers(jwt):
    """注册 JWT 错误处理器"""
    # 基础错误处理
    jwt.expired_token_loader(handle_expired_token_error)
    jwt.invalid_token_loader(handle_invalid_token_error)
    jwt.unauthorized_loader(handle_missing_token_error)

    # 高级错误处理
    jwt.revoked_token_loader(handle_revoked_token_error)
    jwt.needs_fresh_token_loader(handle_fresh_token_required_error)
    jwt.user_lookup_error_loader(handle_user_lookup_error)
