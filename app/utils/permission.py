"""权限管理模块"""

from app.models.device import Device
from app.models.device_project import DeviceProject
from app.models.project import Project
from app.models.user import User


class PermissionManager:
    """权限管理器"""

    @staticmethod
    def check_device_permission(user: User, device_id: int, permission: str = "read") -> bool:
        """检查用户是否有设备权限
        Args:
            user: 用户对象
            device_id: 设备ID
            permission: 权限类型，可选值：read, write
        Returns:
            bool: 是否有权限
        """
        # 管理员有所有权限
        if user.role == "admin":
            return True

        # 检查设备是否存在
        device = Device.query.get(device_id)
        if not device:
            return False

        # 检查设备所有者
        return device.owner_id == user.id

    @staticmethod
    def check_project_permission(user: User, project_id: int, permission: str = "read") -> bool:
        """检查用户是否有项目权限
        Args:
            user: 用户对象
            project_id: 项目ID
            permission: 权限类型，可选值：read, write
        Returns:
            bool: 是否有权限
        """
        # 管理员有所有权限
        if user.role == "admin":
            return True

        # 检查项目是否存在
        project = Project.query.get(project_id)
        if not project:
            return False

        # 检查用户是否有关联的设备
        device_projects = DeviceProject.query.filter_by(project_id=project_id).all()
        for device_project in device_projects:
            device = Device.query.get(device_project.device_id)
            if device and device.owner_id == user.id:
                return True

        return False
