"""自定义错误类"""

class BaseError(Exception):
    """基础错误类"""
    def __init__(self, message="An error occurred", status_code=400):
        super().__init__(message)
        self.message = message
        self.status_code = status_code

class ValidationError(BaseError):
    """数据验证错误"""
    def __init__(self, message="Invalid data"):
        super().__init__(message=message, status_code=400)

class AuthenticationError(BaseError):
    """认证错误"""
    def __init__(self, message="Authentication failed"):
        super().__init__(message=message, status_code=401)

class AuthorizationError(BaseError):
    """授权错误"""
    def __init__(self, message="Permission denied"):
        super().__init__(message=message, status_code=403)

class NotFoundError(BaseError):
    """资源不存在错误"""
    def __init__(self, message="Resource not found"):
        super().__init__(message=message, status_code=404)

class ConflictError(BaseError):
    """资源冲突错误"""
    def __init__(self, message="Resource conflict"):
        super().__init__(message=message, status_code=409)

class BusinessException(Exception):
    def __init__(self, message, code=400):
        super().__init__(message)
        self.code = code
        self.message = message

    def to_dict(self):
        return {"code": self.code, "message": self.message}
