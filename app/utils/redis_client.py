import os

import redis


class RedisClient:
    _instance = None

    @classmethod
    def get_instance(cls):
        """获取 Redis 客户端实例"""
        if not cls._instance:
            cls._instance = redis.Redis(
                host=os.getenv("REDIS_HOST", "localhost"),
                port=int(os.getenv("REDIS_PORT", 6379)),
                password=os.getenv("REDIS_PASSWORD"),
                db=int(os.getenv("REDIS_DB", 0)),
                decode_responses=True
            )
        return cls._instance

    @classmethod
    def set_redis(cls, redis_instance):
        """设置 Redis 实例,用于测试等场景
        Args:
            redis_instance: Redis 客户端实例
        """
        cls._instance = redis_instance
