from typing import Optional, <PERSON><PERSON>
from eth_abi import decode
import web3
from bip32utils import B<PERSON><PERSON><PERSON><PERSON>
from eth_account import Account
from eth_account import messages
from eth_typing import HexStr
from flask import current_app
from web3 import Web3
from web3.middleware import ExtraDataToPOAMiddleware

from app.middlewares.web3.blockchain_strategy import BlockchainStrategy, GasEstimateInfo, TransactionInfo
from app.middlewares.web3.consts import TransactionStatus, ERC20_ABI
from app.utils.crypto import generate_child_key
from app.models.blockchain import Blockchain, Token
from app.utils.wallet import get_token_abi


class EVMStrategy(BlockchainStrategy):
    def __init__(self, chain: Blockchain, web3_provider: Web3):
        super().__init__(chain)
        self.web3 = web3_provider

        # 添加POA中间件（对于BSC等POA链是必需的）
        if chain.chain_code == "BSC":
            self.web3.middleware_onion.inject(ExtraDataToPOAMiddleware, layer=0)

    def generate_wallet(self, seed: bytes, salt: bytes) -> Tu<PERSON>[str, str]:
        # 派生秘钥
        child_key: BIP32Key = generate_child_key(seed, salt, self.chain.coin_type)
        private_bytes = child_key.PrivateKey()

        # 生成钱包地址
        account = Account.from_key(private_bytes)
        hex_str = private_bytes.hex()
        return hex_str, account.address

    def is_valid_address(self, address: str) -> bool:
        return self.web3.is_address(address)

    def get_balance(self, token: Token, address: str) -> int:
        try:
            checksum_address = self.web3.to_checksum_address(address)
            if token.token_type == "NATIVE":
                return self.web3.eth.get_balance(checksum_address)
            else:
                token_abi = get_token_abi(token.token_type)
                token_contract = self.web3.eth.contract(
                    address=token.contract_address,
                    abi=token_abi
                )
                return token_contract.functions.balanceOf(checksum_address).call() or 0
        except Exception as e:
            # 记录错误并返回0
            current_app.logger.error(f"Failed to get balance for {address}: {str(e)}")
            return 0

    def send_transaction(self, token: Token, sender_address: str, receiver_address: str, amount: int,
                         private_key: str) -> str:
        sender_checksum = self.web3.to_checksum_address(sender_address)
        gas_info = self.get_gas_estimate(token, sender_address, receiver_address, amount)
        total_gas = gas_info.gas_limit * gas_info.gas_price
        native_balance = self.web3.eth.get_balance(sender_checksum)
        # 校验余额
        if token.token_type == "NATIVE":
            if native_balance < amount + total_gas:
                raise ValueError("账户余额不足，无法发送交易。")
        else:
            if native_balance < total_gas:
                raise ValueError("账户余额不足，无法支付 GAS 费用。")
            token_balance = self.get_balance(token, sender_checksum)
            if token_balance < amount:
                raise ValueError("代币账户余额不足，无法发送交易。")

        # 构建交易
        nonce = self.web3.eth.get_transaction_count(sender_checksum)
        receiver_checksum = self.web3.to_checksum_address(receiver_address)
        self.web3.to_checksum_address(receiver_address)
        if token.token_type == "NATIVE":
            transaction = {
                'nonce': nonce,
                'to': receiver_checksum,
                'value': amount,
                'gas': gas_info.gas_limit,
                'gasPrice': gas_info.gas_price
            }
        else:
            token_contract_checksum = self.web3.to_checksum_address(token.contract_address)
            # 创建合约实例
            token_abi = get_token_abi(token.token_type)
            abi_token = self.web3.eth.contract(
                address=token_contract_checksum,
                abi=token_abi
            )
            transaction = abi_token.functions.transfer(
                receiver_checksum,
                amount
            ).build_transaction({
                'nonce': nonce,
                'gas': gas_info.gas_limit,
                'gasPrice': gas_info.gas_price,
                'chainId': self.web3.eth.chain_id
            })

        # 签名并发送交易
        signed_txn = self.web3.eth.account.sign_transaction(transaction, private_key)
        tx_hash = self.web3.eth.send_raw_transaction(signed_txn.raw_transaction)
        return self.web3.to_hex(tx_hash)

    def get_gas_estimate(self, token: Token, sender_address: str, receiver: str, amount: int) -> GasEstimateInfo:
        try:
            gas_price = self.web3.eth.gas_price
        except:
            gas_price = self.web3.to_wei('50', 'gwei')  # 默认 50 gwei

        try:
            if token.token_type == "NATIVE":
                gas_limit = self.web3.eth.estimate_gas({
                    'from': sender_address,
                    'to': receiver,
                    'value': self.web3.to_wei(amount, 'wei')
                })
            else:
                sender_checksum = self.web3.to_checksum_address(sender_address)
                receiver_checksum = self.web3.to_checksum_address(receiver)
                token_contract_checksum = self.web3.to_checksum_address(token.contract_address)

                # 创建合约实例
                token_abi = get_token_abi(token.token_type)
                token_contract = self.web3.eth.contract(address=token_contract_checksum, abi=token_abi)

                # 估算gas上限
                gas_limit = token_contract.functions.transfer(receiver_checksum, amount).estimate_gas({'from': sender_checksum})
        except:
            gas_limit = 21000 if token.token_type == "NATIVE" else 65000

        return GasEstimateInfo(gas_price=gas_price,
                               gas_limit=gas_limit,
                               transfer_seconds=self.estimate_transfer_time())

    def get_transaction(self, token: Token, tx_hash: str) -> Optional[TransactionInfo]:
        """获取 EVM 链上的交易详情。

        Args:
            token: 交易币种
            tx_hash: 交易哈希

        Returns:
            TransactionInfo: 统一格式的交易信息
        """
        try:
            # 获取交易基本信息
            tx = self.web3.eth.get_transaction(HexStr(tx_hash))
            if not tx:
                return None

            from_address = tx['from']
            gas_price = tx.get('gasPrice')
            if token.token_type == "NATIVE":
                to_address = tx['to']
                amount = tx.get('value')
            else:
                data = tx['input'][2:]
                # function_selector = data[:8]
                params = data[8:]
                # 解码参数
                to_address, amount = decode(['address', 'uint256'], bytes.fromhex(params))

            # 获取区块信息以获取时间戳
            block_number = tx.get('blockNumber')
            block = self.web3.eth.get_block(block_number) if block_number else None
            block_timestamp = block.get('timestamp') if block else None

            # 获取其他交易信息
            receipt = self.web3.eth.get_transaction_receipt(HexStr(tx_hash))
            gas_used = receipt.get('gasUsed') if receipt else None

            # 确定交易状态
            # 如果没有 receipt 且 blockNumber 为 null，说明交易还在 pending
            if not receipt and not block_number:
                status = TransactionStatus.PENDING.value
            # 如果有 receipt，根据 status 判断成功或失败
            elif receipt:
                status = TransactionStatus.CONFIRMED.value if receipt.get(
                    'status') == 1 else TransactionStatus.FALIED.value
            # 其他情况（有 blockNumber 但没有 receipt）也认为是 pending
            else:
                status = TransactionStatus.PENDING.value

            return TransactionInfo(
                tx_hash=tx_hash,
                from_address=from_address,
                to_address=to_address,
                amount=amount,
                status=status,
                block_number=block_number,
                timestamp=block_timestamp,
                gas_limit=gas_used,
                gas_price=gas_price,
                error=None if status != TransactionStatus.FALIED.value else 'Transaction failed'
            )
        except Exception as e:
            current_app.logger.error(f'error occurred while get_evm_transaction:{tx_hash},e: {str(e)}')
            return None

    def estimate_transfer_time(self) -> int:
        """Estimate transfer time in seconds."""
        # 不同链的估算值
        if self.chain.chain_code == "ETH":
            return 15 * 60  # 15分钟
        elif self.chain.chain_code == "BSC":
            return 5 * 60  # 5分钟
        elif self.chain.chain_code == "ARB":
            return 1 * 60  # 1分钟
        else:
            return 10 * 60  # 默认10分钟

    def sign_message(self, message: str, private_key: str) -> str:
        """Sign a message using the private key

        Args:
            message: The message to sign
            private_key: The private key to sign with

        Returns:
            The signed message
        """
        try:
            # Create message hash
            message_hash = messages.encode_defunct(text=message)

            # Sign the message
            signed_message = self.web3.eth.account.sign_message(
                message_hash,
                private_key=private_key
            )

            # Return the signature
            return signed_message.signature.hex()
        except Exception as e:
            current_app.logger.error(f"Failed to sign message: {str(e)}")
            raise
