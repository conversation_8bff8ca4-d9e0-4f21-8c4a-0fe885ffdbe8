from typing import Dict, Optional
from web3 import Web3
from solana.rpc.api import Client

from app.middlewares.web3.blockchain_strategy import BlockchainStrategy
from app.middlewares.web3.evm_strategy import EVMStrategy
from app.middlewares.web3.solana_strategy import SolanaStrategy
from app.models.blockchain import Blockchain, ChainProtocol
from app.services.blockchain_service import BlockchainService


class BlockchainStrategyFactory:
    """Factory class for creating and managing blockchain strategy instances.

    This factory initializes and manages strategy instances for different blockchain types.
    It follows the Strategy pattern to handle different blockchain operations uniformly.
    Uses lazy initialization to avoid database queries during application startup.
    """

    def __init__(self):
        """Initialize the factory with web3 connections and solana client.

        Note: Does not perform database queries during initialization to avoid
        blocking application startup. Strategies are created lazily when needed.
        """
        self.web3_connections = self._initialize_web3_connections()
        solana_chain = Blockchain.query.filter_by(chain_protocol=ChainProtocol.SOL).first()
        self.solana_client = Client(solana_chain.extend_field['rpc_url'])
        self._strategy_register: Dict[str, BlockchainStrategy] = {}
        self._initialized = False

    def _initialize_web3_connections(self):
        """Initialize Web3 connections for supported EVM networks."""
        blockchains = BlockchainService.get_all()
        return {blockchain.chain_code.lower(): Web3(
            Web3.HTTPProvider(
                blockchain.extend_field['rpc_url']
            )
        ) for blockchain in blockchains if
            blockchain.chain_protocol == ChainProtocol.EVM and blockchain.extend_field and blockchain.extend_field['rpc_url']}

    def _ensure_initialized(self) -> None:
        """Ensure strategies are initialized (lazy initialization).

        This method performs database queries to load blockchain configurations
        and create strategy instances. It's called lazily when strategies are
        first needed, avoiding database access during application startup.
        """
        if self._initialized:
            return

        try:
            # 注册 EVM 兼容链的策略
            for chain_desc in self.web3_connections.keys():
                web3_connection = self.web3_connections.get(chain_desc)
                if web3_connection:
                    chain = Blockchain.query.filter_by(chain_code=chain_desc.upper()).first()
                    if chain:
                        self._strategy_register[chain.chain_code.lower()] = EVMStrategy(chain, web3_connection)

            # 注册 Solana 策略
            solana_chain = Blockchain.query.filter_by(chain_protocol=ChainProtocol.SOL).first()
            if solana_chain:
                self._strategy_register[solana_chain.chain_code.lower()] = SolanaStrategy(
                    solana_chain,
                    self.solana_client
                )

            self._initialized = True

        except Exception as e:
            # 如果初始化失败，记录错误但不阻塞应用启动
            from flask import current_app
            if current_app:
                current_app.logger.error(f"Failed to initialize blockchain strategies: {str(e)}")
            # 不设置 _initialized = True，这样下次调用时会重试

    def get_strategy(self, chain: Blockchain) -> BlockchainStrategy:
        """Get the strategy instance for the specified blockchain.

        This method uses lazy initialization - strategies are created on first access
        to avoid database queries during application startup.

        Args:
            chain: The blockchain to get the strategy for

        Returns:
            The strategy instance for the specified blockchain

        Raises:
            NotImplementedError: If no strategy is registered for the specified chain
            RuntimeError: If strategy initialization fails
        """
        # 确保策略已初始化（lazy initialization）
        self._ensure_initialized()

        # 尝试获取已注册的策略
        strategy = self._strategy_register.get(chain.chain_code.lower())

        if not strategy:
            # 如果策略不存在，尝试动态创建
            strategy = self._create_strategy_for_chain(chain)
            if strategy:
                self._strategy_register[chain.chain_code.lower()] = strategy
            else:
                raise NotImplementedError(f"No strategy available for chain {chain.chain_code}")

        return strategy

    def _create_strategy_for_chain(self, chain: Blockchain) -> Optional[BlockchainStrategy]:
        """Dynamically create a strategy for the given chain.

        Args:
            chain: The blockchain to create a strategy for

        Returns:
            The created strategy instance, or None if creation fails
        """
        try:
            chain_code_lower = chain.chain_code.lower()

            # 检查是否为 EVM 兼容链
            if chain.chain_protocol == ChainProtocol.EVM:
                web3_connection = self.web3_connections.get(chain_code_lower)
                if web3_connection:
                    return EVMStrategy(chain, web3_connection)

            # 检查是否为 Solana 链
            elif chain.chian_protocol == ChainProtocol.EVM:
                return SolanaStrategy(chain, self.solana_client)

            return None

        except Exception as e:
            from flask import current_app
            if current_app:
                current_app.logger.error(f"Failed to create strategy for chain {chain.chain_code}: {str(e)}")
            return None
