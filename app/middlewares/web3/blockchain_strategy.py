from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Op<PERSON>, <PERSON><PERSON>

from app.middlewares.web3.consts import TransactionStatus
from app.models.blockchain import Blockchain, Token


@dataclass
class GasEstimateInfo:
    """Gas estimation information."""
    transfer_seconds: int # 预估转账花费时间
    gas_price: int  # 预估 gasPrice(wei/lamports)
    gas_limit: int  # 预估 GasLimit


@dataclass
class TransactionInfo:
    """Transaction information."""
    tx_hash: str  # Transaction hash
    amount: int
    block_number: int
    status: TransactionStatus  # Transaction status
    from_address: str  # Sender address
    to_address: str  # Recipient address
    gas_price: int  # Gas price in wei/lamports
    gas_limit: int  # Gas limit
    timestamp: int  # Transaction timestamp
    error: str


class BlockchainStrategy(ABC):
    """Abstract base class for blockchain strategies."""

    def __init__(self, chain: Blockchain):
        self.chain = chain

    # 钱包管理
    @abstractmethod
    def generate_wallet(self, seed: bytes, salt: bytes) -> <PERSON><PERSON>[str, str]:
        """Generate a wallet from seed and salt."""
        pass

    @abstractmethod
    def is_valid_address(self, address: str) -> bool:
        """Check if address is valid for this blockchain."""
        pass

    # 代币余额统一接口
    @abstractmethod
    def get_balance(self, token: Token, address: str) -> int:
        """获取代币余额（最小单位）"""
        pass

    # 交易相关
    @abstractmethod
    def send_transaction(self, token: Token, sender_address: str, receiver_address: str, amount: int, private_key: str) -> str:
        """代币转账通用接口"""
        pass

    @abstractmethod
    def get_gas_estimate(self, token: Token, sender_address: str, receiver_address: str, amount: int) -> GasEstimateInfo:
        """估算代币转账gas费用"""
        pass

    @abstractmethod
    def get_transaction(self, token: Token, tx_hash: str) -> Optional[TransactionInfo]:
        """获取交易详情"""
        pass

    @abstractmethod
    def sign_message(self, message: str, private_key: str) -> str:
        """Sign a message using the private key

        Args:
            message: The message to sign
            private_key: The private key to sign with

        Returns:
            The signed message
        """
        pass
