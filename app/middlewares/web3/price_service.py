import requests
from decimal import Decimal
from functools import lru_cache
import time
from flask import current_app
from app.models.blockchain import Token


class PriceService:
    COINGECKO_API = "https://api.coingecko.com/api/v3"
    BIRDEYE_API = "https://public-api.birdeye.so/defi"

    # Token Code 到 CoinGecko ID 的映射
    TOKEN_IDS = {
        'eth_native': 'ethereum',
        'bnb_native': 'binancecoin',
        'sol_native': 'solana',
        'arb_native': 'arbitrum',
        'nodecoin': 'nodecoin'
    }

    # Token Code 到 Birdeye 地址的映射
    BIRDEYE_ADDRESSES = {
        # 'nodecoin': 'B89Hd5Juz7JP2dxCZXFJWk4tMTcbw7feDhuWGb3kq5qE'
    }

    # 缓存时间（秒）
    CACHE_DURATION = 300  # 5分钟

    @classmethod
    @lru_cache(maxsize=32)
    def _get_cached_price(cls):
        """获取缓存的价格，返回 (price_dict, timestamp)"""
        try:
            prices = {}

            # 从CoinGecko获取普通代币价格
            if cls.TOKEN_IDS:
                token_id_arr = ",".join(set(cls.TOKEN_IDS.values()))
                response = requests.get(
                    f"{cls.COINGECKO_API}/simple/price",
                    params={
                        "ids": token_id_arr,
                        "vs_currencies": "usd"
                    },
                    timeout=10
                )
                response.raise_for_status()
                data = response.json()

                for token_code, token_id in cls.TOKEN_IDS.items():
                    if data.get(token_id) and data[token_id].get("usd"):
                        prices[token_code] = Decimal(str(data[token_id]["usd"]))
                    else:
                        prices[token_code] = Decimal('0.0')

            # 从Birdeye获取特殊代币价格（如NODECOIN）
            if cls.BIRDEYE_ADDRESSES:
                for token_code, address in cls.BIRDEYE_ADDRESSES.items():
                    try:
                        response = requests.get(
                            f"{cls.BIRDEYE_API}/price?address={address}",
                            headers={
                                "X-API-KEY": 'b11bb424966a4a3285e572fd067ad02c',
                                "accept": "application/json",
                                "x-chain": 'solana'
                            },
                            timeout=10
                        )
                        response.raise_for_status()
                        data = response.json()

                        if data.get('success') and data.get('data', {}).get('value'):
                            prices[token_code] = Decimal(str(data['data']['value']))
                        else:
                            prices[token_code] = Decimal('0.0')
                    except Exception as e:
                        current_app.logger.warning(f"Failed to get Birdeye price for {token_code}: {str(e)}")
                        prices[token_code] = Decimal('0.0')

            return prices, time.time()
        except Exception as e:
            current_app.logger.error(f"Failed to get prices: {str(e)}")
            # 返回所有已知代币的零价格
            all_tokens = set(cls.TOKEN_IDS.keys()) | set(cls.BIRDEYE_ADDRESSES.keys())
            return {token_code: Decimal('0.0') for token_code in all_tokens}, 0

    @classmethod
    def get_token_price(cls, token: Token) -> Decimal:
        """获取指定代币的价格"""
        price_data, timestamp = cls._get_cached_price()

        # 如果缓存过期，清除缓存
        if time.time() - timestamp > cls.CACHE_DURATION:
            cls._get_cached_price.cache_clear()
            price_data, _ = cls._get_cached_price()

        # 使用 token_code 作为键来查找价格
        token_code = token.token_code if hasattr(token, 'token_code') else str(token)
        return price_data.get(token_code, Decimal('0.0'))

    @classmethod
    def get_token_price_by_code(cls, token_code: str) -> Decimal:
        """根据代币代码获取价格"""
        price_data, timestamp = cls._get_cached_price()

        # 如果缓存过期，清除缓存
        if time.time() - timestamp > cls.CACHE_DURATION:
            cls._get_cached_price.cache_clear()
            price_data, _ = cls._get_cached_price()

        return price_data.get(token_code, Decimal('0.0'))

    @classmethod
    def get_all_prices(cls) -> dict:
        """获取所有代币的价格"""
        price_data, timestamp = cls._get_cached_price()

        # 如果缓存过期，清除缓存
        if time.time() - timestamp > cls.CACHE_DURATION:
            cls._get_cached_price.cache_clear()
            price_data, _ = cls._get_cached_price()

        return price_data

    @classmethod
    def clear_cache(cls):
        """清除价格缓存"""
        cls._get_cached_price.cache_clear()

    @classmethod
    def get_supported_tokens(cls) -> list:
        """获取支持的代币列表"""
        return list(set(cls.TOKEN_IDS.keys()) | set(cls.BIRDEYE_ADDRESSES.keys()))
