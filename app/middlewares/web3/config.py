"""
Configuration for blockchain connections via environment variables.
"""
import os
from typing import Dict, Any


class BlockchainConfig:
    """Configuration for blockchain connections loaded from environment variables."""

    # Default values
    DEFAULT_ENV = "local"
    
    # Default endpoints
    DEFAULT_EVM_ENDPOINT = "http://127.0.0.1:7545"  # Local Ganache
    DEFAULT_SOLANA_ENDPOINT = "http://localhost:8899"  # Local Solana
    
    @staticmethod
    def get_evm_config(chain_code: str = "ETH") -> str:
        """
        Get EVM configuration from environment variables.
        
        Args:
            chain_code: Chain code (ETH, BSC)
            
        Returns:
            Dict containing EVM configuration
        """
        # Get chain-specific endpoint
        endpoint_var = f"{chain_code}_ENDPOINT"
        endpoint = os.getenv(endpoint_var, BlockchainConfig)
        return endpoint
    
    @staticmethod
    def get_solana_config() -> str:
        """
        Get Solana configuration from environment variables.

        Returns:
            Solana endpoint
        """
        endpoint = os.getenv("SOLANA_ENDPOINT", BlockchainConfig.DEFAULT_SOLANA_ENDPOINT)
        
        return endpoint