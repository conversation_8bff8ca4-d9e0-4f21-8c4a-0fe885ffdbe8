from typing import Op<PERSON>, <PERSON><PERSON>, Any

from bip32 import base58
from bip32utils import <PERSON><PERSON><PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import ed25519
from flask import current_app
from solana.rpc.api import Client

from solana.rpc.types import TokenAccountOpts
from solders.solders import Pubkey, Keypair, transfer, Message, Transaction, Signature, TransactionConfirmationStatus, \
    TOKEN_PROGRAM_ID
from solders.system_program import TransferParams

from spl.token.instructions import get_associated_token_address, transfer_checked, TransferCheckedParams, \
    create_associated_token_account

from app.middlewares.web3.blockchain_strategy import BlockchainStrategy, GasEstimateInfo, TransactionInfo
from app.middlewares.web3.consts import TransactionStatus
from app.utils.crypto import generate_child_key
from app.models.blockchain import Blockchain, Token

SOL_FEE_LAMPORTS = 5000
# 租金 = 0.******** SOL * (10**9) * 586
SOL_RENT_FEE_LAMPORTS = 2039280

class SolanaStrategy(BlockchainStrategy):
    """Strategy for Solana blockchain."""

    def __init__(self, chain: Blockchain, solana_client: Client):
        super().__init__(chain)
        self.client = solana_client

    def generate_wallet(self, seed: bytes, salt: bytes) -> Tuple[str, str]:
        child_key: BIP32Key = generate_child_key(seed, salt, self.chain.coin_type)
        private_key = ed25519.Ed25519PrivateKey.from_private_bytes(child_key.PrivateKey())

        private_key_bytes = private_key.private_bytes(
            encoding=serialization.Encoding.Raw,
            format=serialization.PrivateFormat.Raw,
            encryption_algorithm=serialization.NoEncryption()
        )

        public_key_bytes = private_key.public_key().public_bytes(
            encoding=serialization.Encoding.Raw,
            format=serialization.PublicFormat.Raw
        )

        address = base58.b58encode(public_key_bytes).decode('UTF-8')
        sol_private_key_bytes = private_key_bytes + public_key_bytes
        private_key_base58 = base58.b58encode(sol_private_key_bytes).decode('utf-8')
        return private_key_base58, address

    def is_valid_address(self, address: str) -> bool:
        """Check if address is valid for Solana."""
        try:
            pub_key = Pubkey.from_string(address)
            return pub_key.is_on_curve()
        except Exception:
            return False

    def get_balance(self, token: Token, address: str) -> int:
        if token.token_type == "NATIVE":
            return self.get_native_balance(address)
        else:
            return self.get_spl_token_balance(address, token.contract_address)

    def get_native_balance(self, address: str) -> int:
        """Get SOL balance in lamports."""
        pub_key_bytes = base58.b58decode(address.encode('UTF-8'))
        pub_key = Pubkey(pub_key_bytes)
        value = self.client.get_balance(pub_key).value
        value = value or 0
        return value

    def get_spl_token_balance(self, address: str, token_mint: str) -> int:
        """Get SPL token balance in smallest unit."""
        try:
            # 获取关联代币账户地址
            owner_key = Pubkey.from_string(address)
            mint_key = Pubkey.from_string(token_mint)

            # 查找代币账户
            response = self.client.get_token_accounts_by_owner(
                owner_key,
                TokenAccountOpts(mint=mint_key)
            )

            if not response or not response.value:
                return 0

            # 获取代币账户余额
            account_data = response.value[0]
            account_pubkey = account_data.pubkey

            token_info = self.client.get_token_account_balance(account_pubkey)
            if token_info and token_info.value:
                token_amount_value = token_info.value
                return int(token_amount_value.amount)
            return 0
        except Exception as e:
            # 记录错误并返回0
            current_app.logger.error(f"Failed to get SPL token balance for {address}: {str(e)}")
            return 0

    def get_spl_account(self, token_mint: str, wallet_address: str):
        owner_key = Pubkey.from_string(wallet_address)
        mint_key = Pubkey.from_string(token_mint)
        # 查找代币账户
        response = self.client.get_token_accounts_by_owner(
            owner_key,
            TokenAccountOpts(mint=mint_key)
        )
        return response


    def send_transaction(self, token: Token, sender_address: str, receiver_address: str, amount: int, private_key: str) -> str:
        if token.token_type == "NATIVE":
            return self.transfer_native(receiver_address, amount, private_key)
        else:
            return self.transfer_spl_token(token, receiver_address, sender_address, amount, private_key)

    def transfer_native(self, receiver_address: str, amount: int, private_key: str) -> str:
        try:
            sender_keypair = Keypair.from_base58_string(private_key)

            receiver_pubkey = Pubkey.from_string(receiver_address)
            # 计算交易所需的总费用
            total_cost = amount + SOL_FEE_LAMPORTS
            balance = self.client.get_balance(sender_keypair.pubkey()).value
            balance = balance or 0.0
            if balance < total_cost:
                raise ValueError("账户余额不足，无法发送交易。")
            ix_transfers = [transfer(
                TransferParams(
                    from_pubkey=sender_keypair.pubkey(),
                    to_pubkey=receiver_pubkey,
                    lamports=amount
                )
            )]

            latest_blockhash = self.client.get_latest_blockhash().value.blockhash
            msg = Message(ix_transfers, sender_keypair.pubkey())
            tx = Transaction([sender_keypair], msg, latest_blockhash)
            response = self.client.send_transaction(tx)
            return str(response.value)
        except Exception as e:
            current_app.logger.error(f"Solana transaction failed: {str(e)}")
            raise e

    def transfer_spl_token(self, token: Token, receiver_address: str, sender_address: str, amount: int, private_key: str) -> str:
        """Transfer SPL token."""
        try:
            # 解码私钥
            sender_keypair = Keypair.from_base58_string(private_key)
            # 创建公钥
            token_mint_key = Pubkey.from_string(token.contract_address)
            receiver_key = Pubkey.from_string(receiver_address)

            # 获取源和目标代币账户
            sender_token_address = get_associated_token_address(sender_keypair.pubkey(), token_mint_key)
            recipient_token_address = get_associated_token_address(receiver_key, token_mint_key)

            sender_account_response = self.get_spl_account(token_mint=token.contract_address, wallet_address=sender_address)
            if not sender_account_response or not sender_account_response.value:
                raise ValueError("账户余额不足，无法发送交易。")

            sender_account_data = sender_account_response.value[0]
            sender_account_pubkey = sender_account_data.pubkey
            sender_token_info = self.client.get_token_account_balance(sender_account_pubkey)
            if sender_token_info and sender_token_info.value:
                sender_token_amount_value = sender_token_info.value
                sender_token_balance = int(sender_token_amount_value.amount)
                if sender_token_balance < amount:
                    raise ValueError("账户余额不足，无法发送交易。")

            gas_fee_lamports = SOL_FEE_LAMPORTS

            ix_transfers = []
            receiver_account = self.get_spl_account(token_mint=token.contract_address, wallet_address=receiver_address)
            if not receiver_account or not receiver_account.value:
                ix_transfers.append(create_associated_token_account(sender_keypair.pubkey(), receiver_key, token_mint_key))
                gas_fee_lamports += SOL_RENT_FEE_LAMPORTS

            native_balance = self.client.get_balance(sender_keypair.pubkey()).value
            native_balance = native_balance or 0.0
            if native_balance < gas_fee_lamports:
                raise ValueError("账户余额不足，无法发送交易。")

            ix_transfers.append(transfer_checked(
                TransferCheckedParams(
                    source=sender_token_address,
                    dest=recipient_token_address,
                    owner=sender_keypair.pubkey(),
                    mint=token_mint_key,
                    amount=amount,
                    decimals=token.decimals,
                    program_id=TOKEN_PROGRAM_ID
                )
            ))

            # 获取最近的区块哈希
            recent_blockhash = self.client.get_latest_blockhash().value.blockhash
            message = Message(ix_transfers, sender_keypair.pubkey())
            transaction = Transaction(
                from_keypairs=[sender_keypair],
                message=message,
                recent_blockhash=recent_blockhash
            )
            # 签名交易
            transaction.sign([sender_keypair], recent_blockhash)
            # 发送交易
            response = self.client.send_transaction(transaction)
            return str(response.value)
        except Exception as e:
            # 记录错误并重新抛出
            current_app.logger.error(f"Failed to transfer SPL token: {str(e)}")
            raise e

    def get_gas_estimate(self, token: Token, sender_address: str, receiver_address: str, amount: int) -> GasEstimateInfo:
        """Estimate gas for SOL transfer."""
        # Solana使用固定费用
        gas_price = 1  # lamports per signature
        gas_limit = SOL_FEE_LAMPORTS
        if token.token_type != "NATIVE":
            receiver_account = self.get_spl_account(token_mint=token.contract_address, wallet_address=receiver_address)
            # 创建账户需要额外的费用
            if not receiver_account or not receiver_account.value:
                gas_limit += SOL_RENT_FEE_LAMPORTS

        transfer_seconds = 30
        return GasEstimateInfo(
            gas_price=gas_price,
            gas_limit=gas_limit,
            transfer_seconds=transfer_seconds
        )

    def get_transaction(self, token: Token, tx_hash: str) -> Optional[TransactionInfo]:
        """获取 Solana 链上的交易详情。

        Args:
            tx_hash: 交易哈希（签名）
            token: 币种
        Returns:
            TransactionInfo: 统一格式的交易信息
        """
        try:
            # 将交易哈希转换为 Solana 签名格式
            tx_sig = Signature.from_string(tx_hash)

            # 获取交易信息
            response = self.client.get_transaction(tx_sig)
            if not response.value:
                return None

            else:
                tx_data = response.value
                meta = tx_data.transaction.meta
                message = tx_data.transaction.transaction.message

                # 解析交易信息
                pre_balances = meta.pre_balances if meta else []
                post_balances = meta.post_balances if meta else []

                # 获取发送方和接收方地址
                accounts = message.account_keys
                from_address = str(accounts[0]) if accounts else ''
                to_address = str(accounts[1]) if len(accounts) > 1 else ''

                # 计算转账金额（通过比较前后余额）
                if len(pre_balances) >= 2 and len(post_balances) >= 2:
                    balance_gap = abs(post_balances[0] - pre_balances[0])
                else:
                    balance_gap = 0

                # 计算交易费用
                gas_limit = meta.fee if meta else SOL_FEE_LAMPORTS
                if token.token_type != "NATIVE":
                    gas_limit = balance_gap
                    amount = 0
                else:
                    amount = balance_gap - gas_limit

                # 确定交易状态
                status_resp = self.client.get_signature_statuses([tx_sig], True)
                if not status_resp.value or not status_resp.value[0]:
                    status = TransactionStatus.PENDING.value
                    error = None
                else:
                    conf_status = status_resp.value[0].confirmation_status
                    if conf_status == TransactionConfirmationStatus.Finalized:
                        if meta and not meta.err:
                            status = TransactionStatus.FINALIZED.value
                            error = None
                        else:
                            status = TransactionStatus.FALIED.value
                            error = str(meta.err) if meta and meta.err else 'Unknown error'
                    elif conf_status == TransactionConfirmationStatus.Confirmed:
                        status = TransactionStatus.CONFIRMED.value
                        error = None
                    else:  # processed
                        status = TransactionStatus.PENDING.value
                        error = None

                # 获取区块信息
                block_time = tx_data.block_time
                slot = tx_data.slot

                return TransactionInfo(
                    tx_hash=tx_hash,
                    from_address=from_address,
                    to_address=to_address,
                    amount=amount,
                    status=status,
                    block_number=slot,
                    timestamp=block_time,
                    gas_limit=gas_limit,
                    gas_price=1,
                    error=error
                )

        except Exception as e:
            current_app.logger.error(f'error occurred while get_sol_transaction:{tx_hash},e: {str(e)}')
            return None

    def sign_message(self, message: str, private_key: str) -> str:
        """Sign a message using the private key

        Args:
            message: The message to sign
            private_key: The private key to sign with

        Returns:
            The signed message in base58 format
        """
        try:
            # Create keypair from private key
            keypair = Keypair.from_base58_string(private_key)

            # Sign the message
            message_bytes = message.encode('utf-8')
            signature = keypair.sign_message(message_bytes)
            return signature.to_bytes().hex()
        except Exception as e:
            current_app.logger.error(f"Failed to sign message: {str(e)}")
            raise
