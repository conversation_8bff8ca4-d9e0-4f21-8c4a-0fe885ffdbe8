from typing import Optional

from app.middlewares.web3.blockchain_strategy import GasEstimateInfo, TransactionInfo
from app.middlewares.web3.blockchain_strategy_factory import BlockchainStrategyFactory
from app.models.blockchain import Blockchain, Token


class Web3Facade:
    _instance = None

    @classmethod
    def get_instance(cls):
        if not cls._instance:
            cls._instance = Web3Facade()
        return cls._instance

    def __init__(self):
        self.strategy_factory = BlockchainStrategyFactory()

    def generate_wallet(self, chain: Blockchain, seed: bytes, salt: bytes) -> tuple[str, str]:
        strategy = self.strategy_factory.get_strategy(chain)
        return strategy.generate_wallet(seed, salt)

    def get_balance(self, wallet_address: str, token: Token) -> int:
        chain = Blockchain.query.get(token.chain_id)
        if not chain:
            raise ValueError(f"Chain not found for token {token.id}")
        strategy = self.strategy_factory.get_strategy(chain)
        return strategy.get_balance(token, wallet_address)

    def create_transfer(self, token: Token, sender_address: str, receiver_address: str, amount: int,
                        private_key: str) -> str:
        chain = Blockchain.query.get(token.chain_id)
        if not chain:
            raise ValueError(f"Chain not found for token {token.id}")
        strategy = self.strategy_factory.get_strategy(chain)
        return strategy.send_transaction(token, sender_address, receiver_address, amount, private_key)

    def get_gas_estimate(self, token: Token, sender_address: str, receiver_address: str,
                         amount: int) -> GasEstimateInfo:
        chain = Blockchain.query.get(token.chain_id)
        if not chain:
            raise ValueError(f"Chain not found for token {token.id}")
        strategy = self.strategy_factory.get_strategy(chain)
        return strategy.get_gas_estimate(token, sender_address, receiver_address, amount)

    def get_transaction(self, token: Token, tx_hash: str) -> Optional[TransactionInfo]:
        chain = Blockchain.query.get(token.chain_id)
        if not chain:
            raise ValueError(f"Chain not found for token {token.id}")
        strategy = self.strategy_factory.get_strategy(chain)
        return strategy.get_transaction(token, tx_hash)

    def is_valid_address(self, address: str, chain: Blockchain) -> bool:
        strategy = self.strategy_factory.get_strategy(chain)
        return strategy.is_valid_address(address)

    def sign_message(self, message: str, private_key: str, chain: Blockchain):
        strategy = self.strategy_factory.get_strategy(chain)
        return strategy.sign_message(message, private_key)