"""权限相关的中间件"""
from functools import wraps

from flask_jwt_extended import get_jwt_identity, jwt_required

from app.models.base import db
from app.models.user import User
from app.utils.response import Response


def admin_required(f):
    """管理员权限装饰器"""
    @wraps(f)
    @jwt_required()
    def decorated_function(*args, **kwargs):
        current_user = db.session.get(User, get_jwt_identity())
        if not current_user or current_user.role != "admin":
            return Response.forbidden("权限不足")
        return f(*args, **kwargs)
    return decorated_function

def device_permission_required(permission="read"):
    """设备权限装饰器

    Args:
        permission: 权限类型，可选值：read, write
    """
    def decorator(f):
        @wraps(f)
        @jwt_required()
        def decorated_function(device_id, *args, **kwargs):
            current_user = db.session.get(User, get_jwt_identity())
            if not current_user:
                return Response.not_found("用户不存在")

            # 管理员拥有所有权限
            if current_user.role == "admin":
                return f(device_id, *args, **kwargs)

            # 检查用户是否有设备权限
            if not current_user.has_device_permission(device_id, permission):
                return Response.forbidden("权限不足")

            return f(device_id, *args, **kwargs)
        return decorated_function
    return decorator

def project_permission_required(permission="read"):
    """项目权限装饰器

    Args:
        permission: 权限类型，可选值：read, write
    """
    def decorator(f):
        @wraps(f)
        @jwt_required()
        def decorated_function(project_id, *args, **kwargs):
            current_user = db.session.get(User, get_jwt_identity())
            if not current_user:
                return Response.not_found("用户不存在")

            # 管理员拥有所有权限
            if current_user.role == "admin":
                return f(project_id, *args, **kwargs)

            # 检查用户是否有项目权限
            if not current_user.has_project_permission(project_id, permission):
                return Response.forbidden("权限不足")

            return f(project_id, *args, **kwargs)
        return decorated_function
    return decorator
