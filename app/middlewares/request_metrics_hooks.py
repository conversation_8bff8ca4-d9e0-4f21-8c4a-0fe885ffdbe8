from flask import request
from flask_jwt_extended import get_jwt_identity, verify_jwt_in_request, get_jwt
from app.utils.redis_client import RedisClient
from app.utils.response import Response
import datetime  # 新增时间模块
import threading

# 初始化开关状态
metrics_enabled = None
blacklist_enabled = None

def init_switch_subscriber():
    """初始化Redis订阅"""
    redis = RedisClient.get_instance()
    pubsub = redis.pubsub()
    pubsub.subscribe('switch_update')
    
    def listener():
        for message in pubsub.listen():
            if message['data'] == 'refresh':
                refresh_switches()
    
    thread = threading.Thread(target=listener)
    thread.daemon = True
    thread.start()

def refresh_switches():
    """初始化开关状态"""
    global metrics_enabled, blacklist_enabled
    redis = RedisClient.get_instance()
    metrics_enabled = redis.get("switch:metrics") != "false"
    blacklist_enabled = redis.get("switch:blacklist") != "false"

def get_real_ip():
    """获取真实用户IP地址"""
    # 优先从 X-Forwarded-For 获取IP（适用于反向代理场景）
    if request.headers.getlist("X-Forwarded-For"):
        ip = request.headers.getlist("X-Forwarded-For")[0]
    else:
        ip = request.remote_addr
    return ip

def metrics_request_hook():
    """全局请求钩子：实现小时级计数+黑名单检查"""
    try:
        if metrics_enabled is None or blacklist_enabled is None:
            refresh_switches()

        redis = RedisClient.get_instance()

        # 1. 获取关键信息（用户ID、IP、API路径）
        user_id = None
        try:
            verify_jwt_in_request(optional=True)
            jwt_data = get_jwt()
            user_id = get_jwt_identity() if jwt_data else None
        except Exception:
            pass  # 未认证请求，user_id保持为None

        ip = get_real_ip()  # 使用 get_real_ip() 获取真实IP
        api_path = request.path

        # 2. 黑名单检查（优先级最高）
        if blacklist_enabled:
            blacklist_keys = [
                f"blacklist:user:{user_id}" if user_id else None,
                f"blacklist:ip:{ip}",
                f"blacklist:api:{api_path}"
            ]
            for key in blacklist_keys:
                if key and redis.exists(key):
                    return Response.error("request forbidden", 403)

        # 3. 统计逻辑
        if metrics_enabled:
            # 获取当前小时（格式：2024052015 表示2024年5月20日15点）
            current_hour = datetime.datetime.now().strftime("%Y%m%d%H")

            if user_id:
                # 全量统计（非小时级）
                incr_and_reset_ttl(f"api_metrics_count:user:{user_id}")
                incr_and_reset_ttl(f"api_metrics_count:user_api:{user_id}:{api_path}")
                incr_and_reset_ttl(f"api_metrics_count:user_api_ip:{user_id}:{api_path}:{ip}")

                # 用户总访问量（小时级）
                incr_and_reset_ttl(f"api_metrics_count:user_hour:{user_id}:{current_hour}")

                # 用户+API访问量（小时级）
                incr_and_reset_ttl(f"api_metrics_count:user_api_hour:{user_id}:{api_path}:{current_hour}")

                # 用户+API+IP访问量（小时级）
                incr_and_reset_ttl(f"api_metrics_count:user_api_ip_hour:{user_id}:{api_path}:{ip}:{current_hour}")

            incr_and_reset_ttl(f"api_metrics_count:ip:{ip}")
            incr_and_reset_ttl(f"api_metrics_count:api:{api_path}")

            # IP和API的小时级统计
            incr_and_reset_ttl(f"api_metrics_count:ip_hour:{ip}:{current_hour}")
            incr_and_reset_ttl(f"api_metrics_count:api_hour:{api_path}:{current_hour}")
    except Exception:
        pass
    return None

def incr_and_reset_ttl(redis_key: str, ttl=24 * 3600):
    redis = RedisClient.get_instance()
    redis.incr(redis_key)
    redis.expire(redis_key, ttl)  # 设置24小时TTL

