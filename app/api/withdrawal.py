"""
提现相关 API
实现延迟结算的提现功能
"""
from decimal import Decimal
from flask import Blueprint, request
from flask_jwt_extended import jwt_required, get_jwt_identity

from app.middlewares.auth import admin_required
from app.models.asset import UserAsset
from app.services.settlement_service import SettlementService
from app.utils.errors import BusinessException
from app.utils.response import Response

withdrawal_bp = Blueprint('withdrawal', __name__, url_prefix='/api/withdrawals')


@withdrawal_bp.route('/balance/<int:asset_type_id>', methods=['GET'])
@jwt_required()
def get_withdrawable_balance(asset_type_id):
    """获取可提现余额"""
    try:
        current_user_id = get_jwt_identity()
        
        balance_info = SettlementService.calculate_withdrawable_amount(
            current_user_id, 
            asset_type_id
        )
        
        return Response.success({
            "asset_type_id": asset_type_id,
            "balance_info": balance_info
        })
        
    except BusinessException as e:
        return Response.error(e.message, e.code)
    except Exception as e:
        return Response.error(f"获取余额失败: {str(e)}", 500)


@withdrawal_bp.route('/balance', methods=['GET'])
@jwt_required()
def get_all_withdrawable_balances():
    """获取所有资产的可提现余额"""
    try:
        current_user_id = get_jwt_identity()
        
        # 获取用户所有持仓的资产类型
        assets = UserAsset.query.filter_by(user_id=current_user_id).all()
        
        balances = []
        for asset in assets:
            balance_info = SettlementService.calculate_withdrawable_amount(
                current_user_id,
                asset.asset_type_id
            )
            balances.append({
                "asset_type_id": asset.asset_type_id,
                "balance_info": balance_info
            })
        
        return Response.success({
            "balances": balances
        })
        
    except BusinessException as e:
        return Response.error(e.message, e.code)
    except Exception as e:
        return Response.error(f"获取余额失败: {str(e)}", 500)


@withdrawal_bp.route('', methods=['POST'])
@jwt_required()
def create_withdrawal():
    """创建提现申请"""
    try:
        current_user_id = get_jwt_identity()
        data = request.get_json()
        
        if not data:
            return Response.error("请求数据不能为空", 400)
        
        # 参数验证
        required_fields = ['asset_type_id', 'amount']
        for field in required_fields:
            if field not in data:
                return Response.error(f"缺少必要参数: {field}", 400)
        
        asset_type_id = data['asset_type_id']
        amount = Decimal(str(data['amount']))
        withdraw_address = data.get('withdraw_address')
        
        if amount <= 0:
            return Response.error("提现金额必须大于0", 400)
        
        # 创建提现结算
        settlement = SettlementService.create_withdrawal_settlement(
            user_id=current_user_id,
            asset_type_id=asset_type_id,
            withdraw_amount=amount,
            withdraw_address=withdraw_address
        )
        
        return Response.success({
            "settlement_id": settlement.id,
            "withdraw_amount": str(settlement.withdraw_amount),
            "status": settlement.status.value,
            "settlement_details": {
                "total_buy_amount": str(settlement.total_buy_amount),
                "total_sell_amount": str(settlement.total_sell_amount),
                "net_amount": str(settlement.net_amount),
                "realized_pnl": str(settlement.realized_pnl),
                "total_fee": str(settlement.total_fee)
            },
            "message": "提现申请已创建，正在处理中"
        })
        
    except BusinessException as e:
        return Response.error(e.message, e.code)
    except ValueError as e:
        return Response.error(f"金额格式错误: {str(e)}", 400)
    except Exception as e:
        return Response.error(f"创建提现申请失败: {str(e)}", 500)


@withdrawal_bp.route('/<int:settlement_id>', methods=['GET'])
@jwt_required()
def get_withdrawal_detail(settlement_id):
    """获取提现详情"""
    try:
        current_user_id = get_jwt_identity()
        
        from app.models.settlement import UserSettlement
        settlement = UserSettlement.query.filter_by(
            id=settlement_id,
            user_id=current_user_id
        ).first()
        
        if not settlement:
            return Response.error("提现记录不存在", 404)
        
        return Response.success({
            "settlement": settlement.to_dict()
        })
        
    except BusinessException as e:
        return Response.error(e.message, e.code)
    except Exception as e:
        return Response.error(f"获取提现详情失败: {str(e)}", 500)


@withdrawal_bp.route('/history', methods=['GET'])
@jwt_required()
def get_withdrawal_history():
    """获取提现历史"""
    try:
        current_user_id = get_jwt_identity()
        
        # 参数获取
        asset_type_id = request.args.get('asset_type_id', type=int)
        page = request.args.get('page', 1, type=int)
        size = request.args.get('size', 20, type=int)
        
        # 参数验证
        if page < 1:
            return Response.error("页码必须大于0", 400)
        if size < 1 or size > 100:
            return Response.error("每页数量必须在1-100之间", 400)
        
        result = SettlementService.get_user_settlement_history(
            user_id=current_user_id,
            asset_type_id=asset_type_id,
            page=page,
            size=size
        )
        
        return Response.success(result)
        
    except BusinessException as e:
        return Response.error(e.message, e.code)
    except Exception as e:
        return Response.error(f"获取提现历史失败: {str(e)}", 500)


# ========================================
# 管理员提现管理接口
# ========================================

@admin_required
@withdrawal_bp.route('/admin', methods=['GET'])
def admin_get_withdrawals():
    """管理员获取所有提现申请"""
    try:
        # 参数获取
        user_id = request.args.get('user_id', type=int)
        asset_type_id = request.args.get('asset_type_id', type=int)
        status = request.args.get('status')
        page = request.args.get('page', 1, type=int)
        size = request.args.get('size', 20, type=int)
        
        # 参数验证
        if page < 1:
            return Response.error("页码必须大于0", 400)
        if size < 1 or size > 100:
            return Response.error("每页数量必须在1-100之间", 400)
        
        from app.models.settlement import UserSettlement
        query = UserSettlement.query
        
        # 过滤条件
        if user_id:
            query = query.filter_by(user_id=user_id)
        if asset_type_id:
            query = query.filter_by(asset_type_id=asset_type_id)
        if status:
            query = query.filter_by(status=status)
        
        # 分页
        total = query.count()
        offset = (page - 1) * size
        settlements = query.order_by(UserSettlement.created_at.desc()).offset(offset).limit(size).all()
        
        return Response.success({
            "records": [settlement.to_dict() for settlement in settlements],
            "total": total,
            "page": page,
            "size": size
        })
        
    except BusinessException as e:
        return Response.error(e.message, e.code)
    except Exception as e:
        return Response.error(f"获取提现申请失败: {str(e)}", 500)


@admin_required
@withdrawal_bp.route('/admin/<int:settlement_id>/approve', methods=['POST'])
def admin_approve_withdrawal(settlement_id):
    """管理员批准提现"""
    try:
        settlement = SettlementService.complete_settlement(settlement_id)
        
        return Response.success({
            "settlement_id": settlement.id,
            "status": settlement.status.value,
            "message": "提现已批准并完成"
        })
        
    except BusinessException as e:
        return Response.error(e.message, e.code)
    except Exception as e:
        return Response.error(f"批准提现失败: {str(e)}", 500)


@admin_required
@withdrawal_bp.route('/admin/<int:settlement_id>/reject', methods=['POST'])
def admin_reject_withdrawal(settlement_id):
    """管理员拒绝提现"""
    try:
        data = request.get_json() or {}
        reason = data.get('reason', '管理员拒绝')
        
        from app.models.settlement import UserSettlement, SettlementStatusEnum
        settlement = UserSettlement.query.get(settlement_id)
        
        if not settlement:
            return Response.error("提现记录不存在", 404)
        
        if settlement.status != SettlementStatusEnum.PROCESSING:
            return Response.error("只能拒绝处理中的提现申请", 400)
        
        # 更新结算状态
        settlement.status = SettlementStatusEnum.FAILED
        settlement.remark = reason
        
        # 恢复用户资产（解冻）
        asset = UserAsset.query.filter_by(
            user_id=settlement.user_id,
            asset_type_id=settlement.asset_type_id
        ).first()

        if asset:
            asset.available_balance += settlement.withdraw_amount
            asset.frozen_balance -= settlement.withdraw_amount
        
        # 恢复流水状态
        from app.models.settlement import TradeFlow
        flows = TradeFlow.query.filter_by(settlement_id=settlement_id).all()
        for flow in flows:
            flow.settlement_status = SettlementStatusEnum.PENDING
            flow.settlement_id = None
        
        from app.models import db
        db.session.commit()
        
        return Response.success({
            "settlement_id": settlement.id,
            "status": settlement.status.value,
            "message": f"提现已拒绝: {reason}"
        })
        
    except BusinessException as e:
        return Response.error(e.message, e.code)
    except Exception as e:
        return Response.error(f"拒绝提现失败: {str(e)}", 500)
