"""用户资产管理工具API - 用于测试和管理"""

from decimal import Decimal

from flask import Blueprint, request
from flask_jwt_extended import get_jwt_identity

from app.middlewares.auth import admin_required
from app.models.asset import UserAsset, AssetTransaction, TransactionTypeEnum, TransactionStatusEnum
from app.models.base import db
from app.models.blockchain import AssetType
from app.services.asset_service import AssetService
from app.utils.auth import get_current_user
from app.utils.decorators import handle_api_errors
from app.utils.errors import BusinessException
from app.utils.response import Response

asset_management_tools_bp = Blueprint("asset_management_tools", __name__)


@asset_management_tools_bp.route('/create', methods=['POST'])
@admin_required
@handle_api_errors
def create_user_asset():
    """创建用户资产记录
    
    为用户创建指定资产类型的资产记录，可设置初始余额。
    
    Args:
        Request Body:
            asset_type_id (int): 资产类型ID
            initial_balance (str, optional): 初始余额，默认为0
    
    Returns:
        Response: 创建的用户资产信息
    
    Example:
        POST /api/asset-tools/create
        {
            "asset_type_id": 1,
            "initial_balance": "1000.00000000"
        }
    """
    current_user_id = get_jwt_identity()

    data = request.get_json()
    if not data:
        return Response.success(data=None, message="请求数据不能为空", code=400)

    asset_type_id = data.get('asset_type_id')
    initial_balance = data.get('initial_balance', '0')
    user_id = data.get('user_id', current_user_id)
    if not asset_type_id:
        return Response.success(data=None, message="资产类型ID不能为空", code=400)

    try:
        initial_balance = Decimal(str(initial_balance))
        if initial_balance < 0:
            return Response.success(data=None, message="初始余额不能为负数", code=400)
    except (ValueError, TypeError):
        return Response.success(data=None, message="初始余额格式无效", code=400)

    with db.session.begin_nested():
        user_asset = AssetService.add_or_create_user_asset(
            user_id=user_id,
            asset_type_id=asset_type_id,
            amount=initial_balance
        )
    db.session.commit()

    # 获取资产类型信息
    asset_type = db.session.get(AssetType, asset_type_id)

    return Response.success({
        "user_id": user_asset.user_id,
        "asset_type_id": user_asset.asset_type_id,
        "asset_name": asset_type.name if asset_type else "未知资产",
        "available_balance": str(user_asset.available_balance),
        "frozen_balance": str(user_asset.frozen_balance),
        "occupied_balance": str(user_asset.occupied_balance),
        "total_balance": str(
            user_asset.available_balance +
            user_asset.frozen_balance +
            user_asset.occupied_balance
        ),
        "created_at": user_asset.created_at.isoformat() if user_asset.created_at else None,
        "message": "用户资产创建成功"
    })


@asset_management_tools_bp.route('/add-balance', methods=['POST'])
@admin_required
@handle_api_errors
def add_user_balance():
    """增加用户资产余额
    
    为用户指定资产类型增加余额，会自动记录交易流水。
    
    Args:
        Request Body:
            asset_type_id (int): 资产类型ID
            amount (str): 增加的金额
            transaction_type (str, optional): 交易类型，默认为DEPOSIT
            reference_id (str, optional): 关联ID
            remark (str, optional): 备注
    
    Returns:
        Response: 更新后的用户资产信息
    
    Example:
        POST /api/asset-tools/add-balance
        {
            "asset_type_id": 1,
            "amount": "500.00000000",
            "transaction_type": "DEPOSIT",
            "reference_id": "TEST_DEPOSIT_001",
            "remark": "测试充值"
        }
    """
    current_user_id = get_jwt_identity()

    data = request.get_json()
    if not data:
        return Response.success(data=None, message="请求数据不能为空", code=400)

    asset_type_id = data.get('asset_type_id')
    amount = data.get('amount')
    transaction_type_str = data.get('transaction_type', 'DEPOSIT')
    remark = data.get('remark', '')
    user_id = data.get('user_id', current_user_id)

    if not asset_type_id:
        return Response.success(data=None, message="资产类型ID不能为空", code=400)

    if not amount:
        return Response.success(data=None, message="金额不能为空", code=400)

    try:
        amount = Decimal(str(amount))
        if amount <= 0:
            return Response.success(data=None, message="金额必须大于0", code=400)
    except (ValueError, TypeError):
        return Response.success(data=None, message="金额格式无效", code=400)

    try:
        transaction_type = TransactionTypeEnum[transaction_type_str]
    except KeyError:
        return Response.error(f"无效的交易类型: {transaction_type_str}", 400)

    with db.session.begin_nested():
        user_asset = AssetService.add_or_create_user_asset(
            user_id=user_id,
            asset_type_id=asset_type_id,
            amount=amount,
            remark=remark
        )
    db.session.commit()

    # 获取资产类型信息
    asset_type = db.session.get(AssetType, asset_type_id)

    return Response.success({
        "user_id": user_asset.user_id,
        "asset_type_id": user_asset.asset_type_id,
        "asset_name": asset_type.name if asset_type else "未知资产",
        "available_balance": str(user_asset.available_balance),
        "frozen_balance": str(user_asset.frozen_balance),
        "occupied_balance": str(user_asset.occupied_balance),
        "total_balance": str(
            user_asset.available_balance +
            user_asset.frozen_balance +
            user_asset.occupied_balance
        ),
        "added_amount": str(amount),
        "transaction_type": transaction_type.value,
        "updated_at": user_asset.updated_at.isoformat() if user_asset.updated_at else None,
        "message": f"成功增加 {amount} {asset_type.name if asset_type else '资产'}"
    })


@asset_management_tools_bp.route('/subtract-balance', methods=['POST'])
@admin_required
@handle_api_errors
def subtract_user_balance():
    """减少用户资产余额
    
    为用户指定资产类型减少余额，会自动记录交易流水。
    
    Args:
        Request Body:
            asset_type_id (int): 资产类型ID
            amount (str): 减少的金额
            transaction_type (str, optional): 交易类型，默认为WITHDRAW
            reference_id (str, optional): 关联ID
            remark (str, optional): 备注
    
    Returns:
        Response: 更新后的用户资产信息
    
    Example:
        POST /api/asset-tools/subtract-balance
        {
            "asset_type_id": 1,
            "amount": "100.00000000",
            "transaction_type": "WITHDRAW",
            "reference_id": "TEST_WITHDRAW_001",
            "remark": "测试提现"
        }
    """
    current_user_id = get_jwt_identity()

    data = request.get_json()
    if not data:
        return Response.success(data=None, message="请求数据不能为空", code=400)

    asset_type_id = data.get('asset_type_id')
    amount = data.get('amount')
    transaction_type_str = data.get('transaction_type', 'WITHDRAW')
    reference_id = data.get('reference_id', '')
    remark = data.get('remark', '')
    user_id = data.get('use_id', current_user_id)

    if not asset_type_id:
        return Response.success(data=None, message="资产类型ID不能为空", code=400)

    if not amount:
        return Response.success(data=None, message="金额不能为空", code=400)

    try:
        amount = Decimal(str(amount))
        if amount <= 0:
            return Response.success(data=None, message="金额必须大于0", code=400)
    except (ValueError, TypeError):
        return Response.success(data=None, message="金额格式无效", code=400)

    try:
        transaction_type = TransactionTypeEnum[transaction_type_str]
    except KeyError:
        return Response.error(f"无效的交易类型: {transaction_type_str}", 400)

    with db.session.begin_nested():
        user_asset = AssetService.subtract_balance(
            user_id=user_id,
            asset_type_id=asset_type_id,
            amount=amount,
            transaction_type=transaction_type,
            reference_id=reference_id,
            remark=remark
        )
    db.session.commit()

    # 获取资产类型信息
    asset_type = db.session.get(AssetType, asset_type_id)

    return Response.success({
        "user_id": user_asset.user_id,
        "asset_type_id": user_asset.asset_type_id,
        "asset_name": asset_type.name if asset_type else "未知资产",
        "available_balance": str(user_asset.available_balance),
        "frozen_balance": str(user_asset.frozen_balance),
        "occupied_balance": str(user_asset.occupied_balance),
        "total_balance": str(
            user_asset.available_balance +
            user_asset.frozen_balance +
            user_asset.occupied_balance
        ),
        "subtracted_amount": str(amount),
        "transaction_type": transaction_type.value,
        "updated_at": user_asset.updated_at.isoformat() if user_asset.updated_at else None,
        "message": f"成功减少 {amount} {asset_type.name if asset_type else '资产'}"
    })


@asset_management_tools_bp.route('/transactions', methods=['GET'])
@admin_required
@handle_api_errors
def get_user_transactions():
    """获取用户资产交易记录
    
    获取当前用户的资产交易流水记录。
    
    Args:
        Query Parameters:
            asset_type_id (int, optional): 资产类型ID过滤
            transaction_type (str, optional): 交易类型过滤
            start_time (str, optional): 开始时间过滤
            end_time (str, optional): 结束时间过滤
            page (int, optional): 页码，默认为1
            per_page (int, optional): 每页数量，默认为20
    
    Returns:
        Response: 交易记录列表
    
    Example:
        GET /api/asset-tools/transactions?asset_type_id=1&transaction_type=DEPOSIT
    """
    current_user = get_current_user()
    target_user_id = current_user.id

    if current_user.is_admin and request.args.get('user_id'):
        target_user_id = request.args.get('user_id')

    # 获取查询参数
    asset_type_id = request.args.get('asset_type_id', type=int)
    transaction_type = request.args.get('transaction_type')
    start_time = request.args.get('start_time')
    end_time = request.args.get('end_time')
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)

    # 构建查询
    query = db.session.query(AssetTransaction, AssetType).join(
        AssetType, AssetTransaction.asset_type_id == AssetType.id
    ).filter(AssetTransaction.user_id == target_user_id)

    # 资产类型过滤
    if asset_type_id:
        query = query.filter(AssetTransaction.asset_type_id == asset_type_id)

    # 交易类型过滤
    if transaction_type:
        try:
            transaction_type_enum = TransactionTypeEnum[transaction_type]
            query = query.filter(AssetTransaction.transaction_type == transaction_type_enum)
        except KeyError:
            return Response.error(f"无效的交易类型: {transaction_type}", 400)

    # 时间过滤
    if start_time:
        query = query.filter(AssetTransaction.created_at >= start_time)
    if end_time:
        query = query.filter(AssetTransaction.created_at <= end_time)

    # 分页
    total = query.count()
    items = query.order_by(AssetTransaction.created_at.desc()).offset(
        (page - 1) * per_page
    ).limit(per_page).all()

    # 构建响应数据
    transactions_list = []
    for transaction, asset_type in items:
        transaction_info = {
            "id": transaction.id,
            "asset_type_id": transaction.asset_type_id,
            "asset_name": asset_type.name,
            "amount": str(transaction.amount),
            "balance_before": str(transaction.balance_before),
            "balance_after": str(transaction.balance_after),
            "transaction_type": transaction.transaction_type.value,
            "reference_id": transaction.reference_id,
            "status": transaction.status.value,
            "extend_field": transaction.extend_field,
            "created_at": transaction.created_at.isoformat() if transaction.created_at else None
        }
        transactions_list.append(transaction_info)

    return Response.success({
        "total": total,
        "items": transactions_list,
        "page": page,
        "per_page": per_page
    })


@asset_management_tools_bp.route('/batch-create', methods=['POST'])
@admin_required
@handle_api_errors
def batch_create_user_assets():
    """批量创建用户资产记录

    为用户批量创建多个资产类型的资产记录。

    Args:
        Request Body:
            assets (list): 资产列表，每个资产包含：
                - asset_type_id: 资产类型ID
                - initial_balance: 初始余额，可选

    Returns:
        Response: 批量创建结果

    Example:
        POST /api/asset-tools/batch-create
        {
            "assets": [
                {
                    "asset_type_id": 1,
                    "initial_balance": "1000.00000000"
                },
                {
                    "asset_type_id": 2,
                    "initial_balance": "500.00000000"
                }
            ]
        }
    """
    current_user = get_current_user()

    data = request.get_json()
    if not data or 'assets' not in data:
        return Response.success(data=None, message="请求数据不能为空", code=400)

    assets = data['assets']
    user_id = data.get('user_id', current_user)
    if not isinstance(assets, list):
        return Response.success(data=None, message="assets必须是数组", code=400)

    if len(assets) == 0:
        return Response.success(data=None, message="资产列表不能为空", code=400)

    if len(assets) > 50:  # 限制批量创建的数量
        return Response.success(data=None, message="单次最多创建50个资产记录", code=400)

    results = []
    success_count = 0
    failed_count = 0

    for index, asset_data in enumerate(assets):
        try:
            asset_type_id = asset_data.get('asset_type_id')
            initial_balance = asset_data.get('initial_balance', '0')

            if not asset_type_id:
                raise BusinessException("资产类型ID不能为空", 400)

            initial_balance = Decimal(str(initial_balance))
            if initial_balance < 0:
                raise BusinessException("初始余额不能为负数", 400)

            # 创建用户资产
            with db.session.begin_nested():
                AssetService.add_or_create_user_asset(
                    user_id=user_id,
                    asset_type_id=asset_type_id,
                    amount=initial_balance
                )
            db.session.commit()

            # 获取资产类型信息
            asset_type = db.session.get(AssetType, asset_type_id)

            results.append({
                "index": index,
                "success": True,
                "asset_type_id": asset_type_id,
                "asset_name": asset_type.name if asset_type else "未知资产",
                "initial_balance": str(initial_balance),
                "message": "资产创建成功"
            })
            success_count += 1

        except BusinessException as e:
            results.append({
                "index": index,
                "success": False,
                "asset_type_id": asset_data.get('asset_type_id'),
                "message": e.message
            })
            failed_count += 1

        except Exception as e:
            results.append({
                "index": index,
                "success": False,
                "asset_type_id": asset_data.get('asset_type_id'),
                "message": f"创建失败: {str(e)}"
            })
            failed_count += 1

    return Response.success({
        "success_count": success_count,
        "failed_count": failed_count,
        "total_count": len(assets),
        "results": results
    })


@asset_management_tools_bp.route('/reset-balance', methods=['POST'])
@admin_required
@handle_api_errors
def reset_user_balance():
    """重置用户资产余额

    将用户指定资产的余额重置为指定值，常用于测试环境。

    Args:
        Request Body:
            asset_type_id (int): 资产类型ID
            new_balance (str): 新的余额
            remark (str, optional): 备注

    Returns:
        Response: 重置后的用户资产信息

    Example:
        POST /api/asset-tools/reset-balance
        {
            "asset_type_id": 1,
            "new_balance": "10000.00000000",
            "remark": "测试环境余额重置"
        }
    """
    current_user = get_current_user()

    data = request.get_json()
    if not data:
        return Response.success(data=None, message="请求数据不能为空", code=400)

    asset_type_id = data.get('asset_type_id')
    new_balance = data.get('new_balance')
    remark = data.get('remark', '余额重置')
    user_id = data.get('user_id', current_user.id)

    if not asset_type_id:
        return Response.success(data=None, message="资产类型ID不能为空", code=400)

    if new_balance is None:
        return Response.success(data=None, message="新余额不能为空", code=400)

    try:
        new_balance = Decimal(str(new_balance))
        if new_balance < 0:
            return Response.success(data=None, message="新余额不能为负数", code=400)
    except (ValueError, TypeError):
        return Response.success(data=None, message="新余额格式无效", code=400)

    with db.session.begin_nested():
        # 获取用户资产记录
        asset = db.session.query(UserAsset).filter_by(
            user_id=user_id,
            asset_type_id=asset_type_id
        ).with_for_update().first()

        if not asset:
            # 如果不存在，创建新记录
            asset = AssetService.add_or_create_user_asset(
                user_id=user_id,
                asset_type_id=asset_type_id,
                amount=new_balance,
                remark=remark
            )
        else:
            # 记录变更前余额
            balance_before = asset.available_balance

            # 重置余额（清空冻结和占用余额）
            asset.available_balance = new_balance
            asset.frozen_balance = Decimal('0')
            asset.occupied_balance = Decimal('0')

            # 记录交易流水
            transaction = AssetTransaction(
                user_id=user_id,
                asset_type_id=asset_type_id,
                amount=new_balance - balance_before,
                balance_before=balance_before,
                balance_after=new_balance,
                transaction_type=TransactionTypeEnum.SYSTEM_ADJUST,
                reference_id="BALANCE_RESET",
                status=TransactionStatusEnum.SUCCESS,
                extend_field={"remark": remark}
            )
            db.session.add(transaction)
    db.session.commit()

    # 获取资产类型信息
    asset_type = db.session.get(AssetType, asset_type_id)

    return Response.success({
        "user_id": asset.user_id,
        "asset_type_id": asset.asset_type_id,
        "asset_name": asset_type.name if asset_type else "未知资产",
        "available_balance": str(asset.available_balance),
        "frozen_balance": str(asset.frozen_balance),
        "occupied_balance": str(asset.occupied_balance),
        "total_balance": str(asset.available_balance),
        "new_balance": str(new_balance),
        "updated_at": asset.updated_at.isoformat() if asset.updated_at else None,
        "message": f"余额已重置为 {new_balance} {asset_type.name if asset_type else '资产'}"
    })
