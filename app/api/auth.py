"""用户认证相关的API接口"""
from flask import Blueprint, current_app, request, jsonify, make_response
from flask_cors import cross_origin
from flask_jwt_extended import get_jwt_identity, jwt_required
from flask_jwt_extended.exceptions import NoAuthorizationError, InvalidHeaderError

from app.models.base import db
from app.models.user import User
from app.services.auth_service import AuthService
from app.services.email_service import EmailService
from app.services.captcha_service import CaptchaService
from app.services.points_service import PointsService
from app.services.ubi_points_integration_service import UbiPointsIntegrationService
from app.utils.redis_client import RedisClient
from app.utils.response import Response
from app.models.device import Device
from sqlalchemy import func
from app.models.points import PointRecord
from app.tasks.device_project_validation import validate_device_projects
from app.services.metrics_service import metrics_service

# 新增的服务
auth_service = AuthService()
email_service = EmailService()

auth_bp = Blueprint("auth", __name__, url_prefix="/auth")


@auth_bp.route("/register", methods=["POST"])
@cross_origin()
def register():
    """
    注册用户
    :return:
    """
    try:
        data = request.get_json()
        if not data or not all(k in data for k in ("username", "email", "password")):
            return Response.validation_error("缺少必要字段")

        # 在非测试环境下验证验证码
        if not current_app.config.get('TESTING'):
            if 'code' not in data:
                return Response.validation_error("缺少验证码")
                
            code = data['code']
            redis_key = f"verifycode{data['email']}"
            stored_code = RedisClient.get_instance().get(redis_key)
            if not stored_code or stored_code != code:
                return Response.error("验证码错误或已过期")
            RedisClient.get_instance().delete(redis_key)

        invite_code = data.get('invite_code')

        token, user, error = AuthService.register(
            username=data["username"],
            password=data["password"],
            email=data["email"],
            invite_code=invite_code,  # 这个invite_code用于查询当前用户的邀请者，并填入当前用户的invited_by，而不是生成当前用户的邀请码
            role=data.get("role", "user")
        )

        if error:
            current_app.logger.error(f"Register error: {error}")
            return Response.error(error)

        try:
            inviter = User.query.filter_by(invite_code=invite_code).first()
            if inviter:
                PointsService.add_init_invite_record(inviter.id, user.id)
        except Exception as e:
            current_app.logger.error(e)  # 邀请失败后不影响注册流程

        # 触发异步任务验证设备项目记录
        try:
            validate_device_projects.delay(user.id)
        except Exception as e:
            current_app.logger.warning(f"Failed to queue device project validation task: {str(e)}")
            # Continue with registration even if task queueing fails

        return_dict = user.to_dict()
        return_dict['token'] = token
        return Response.success(return_dict, "注册成功")
    except Exception as e:
        current_app.logger.error(f"Register error: {str(e)}")
        return Response.error("注册失败，请稍后重试", 500)


@auth_bp.route("/login", methods=["POST"])
@cross_origin()
def login():
    """
    用户登录
    :return:
    """
    try:
        data = request.get_json()
        if not data or not all(k in data for k in ("username", "password")):
            return Response.validation_error("缺少必要字段")

        token, error = AuthService.login(
            username=data["username"],
            password=data["password"],
            captcha_id=data.get('verifyToken', ''),
            captcha_text=data.get('verifyCode', '').upper()
        )
        if error:
            return Response.error(error, 401)

        # 获取用户并触发异步任务验证设备项目记录
        user = User.query.filter_by(username=data["username"]).first()
        
        # Try to queue the task, but don't fail login if it fails
        try:
            validate_device_projects.delay(user.id)
        except Exception as e:
            current_app.logger.warning(f"Failed to queue device project validation task: {str(e)}")
            # Continue with login even if task queueing fails

        return Response.success({
            "token": token,
            "user": user.to_dict()
        }, "登录成功")

    except Exception as e:
        current_app.logger.error(f"Login error: {str(e)}")
        return Response.error("登录失败，请稍后重试", 500)


@auth_bp.route("/users", methods=["GET"])
@jwt_required()
@cross_origin()
def get_users():
    """
    获取用户列表
    :return:
    """
    try:
        user_id = get_jwt_identity()
        print(f"JWT identity: {user_id}, type: {type(user_id)}")

        with current_app.app_context():
            current_user = db.session.get(User, int(user_id))
            print(f"Current user: {current_user}, role: {current_user.role if current_user else None}")

            if not current_user or current_user.role != "admin":
                return Response.forbidden()

            page = request.args.get("page", 1, type=int)
            email = request.args.get("email")
            username = request.args.get("username")
            per_page = request.args.get("per_page", 10, type=int)
            users, total = AuthService.get_users(page, per_page, email = email, username = username)

            return Response.success({
                "items": [user.to_dict() for user in users],
                "total": total,
                "page": page,
                "per_page": per_page
            })
    except Exception as e:
        current_app.logger.error(f"Get users error: {str(e)}")
        return Response.error("获取用户列表失败，请稍后重试", 500)


@auth_bp.route("/users/<int:user_id>", methods=["PUT"])
@jwt_required()
@cross_origin()
def update_user(user_id):
    """
    更新用户信息
    :param user_id:
    :return:
    """
    try:
        user_id_str = get_jwt_identity()
        print(f"JWT identity: {user_id_str}, type: {type(user_id_str)}")

        with current_app.app_context():
            current_user = db.session.get(User, int(user_id_str))
            print(f"Current user: {current_user}, role: {current_user.role if current_user else None}")

            if not current_user or current_user.role != "admin":
                return Response.forbidden()

            data = request.get_json()
            if not data:
                return Response.validation_error("没有提供更新数据")

            user, error = AuthService.update_user(user_id, data)
            if error:
                return Response.error(error)

            return Response.success(user.to_dict(), "用户信息更新成功")
    except Exception as e:
        current_app.logger.error(f"Update user error: {str(e)}")
        return Response.error("更新用户信息失败，请稍后重试", 500)


@auth_bp.route("/profile", methods=["GET"])
@jwt_required()
def get_profile():
    """
    获取当前用户资料
    :return:
    """
    try:
        user_id = get_jwt_identity()
        user = db.session.get(User, int(user_id))

        if not user:
            return Response.error("用户不存在", 404)

        return Response.success(user.to_dict())
    except Exception as e:
        current_app.logger.error(f"Get profile error: {str(e)}")
        return Response.error("获取用户资料失败", 500)


@auth_bp.route('/invite_history', methods=['POST'])
@jwt_required()
def get_invite_history():
    """获取当前用户的邀请历史"""
    user_id = get_jwt_identity()

    data = request.get_json()

    if not data or not all(k in data for k in ("page", "per_page")):
        return Response.error("缺少必要字段")

    page = data.get("page")
    per_page = data.get("per_page")

    status = data.get("status")  # 可选参数，可能是 "pending" 或 "success"

    # 基础查询：record_type='invite'
    query = PointRecord.query.filter_by(user_id=user_id, record_type='invite').filter(
        PointRecord.invitee_id.isnot(None))

    # 根据 status 可选参数增加约束
    if status == "pending":
        query = query.filter(PointRecord.points == 0)
    elif status == "success":
        query = query.filter(PointRecord.points > 0)

    # 分页并按创建时间倒序
    pagination = query.order_by(PointRecord.created_at.desc()) \
        .paginate(page=page, per_page=per_page, error_out=False)

    records = pagination.items

    # 组装返回数据
    items = []
    for r in records:
        item = r.to_dict()  # 基础字段

        if r.invitee and r.invitee.email:
            item["account"] = r.invitee.email
        else:
            item["account"] = None

        # 显示邀请日期（如 "21 Dec, 2024"）
        if r.created_at:
            item["invited_date"] = r.created_at
        else:
            item["invited_date"] = None

        if r.invitee and r.invitee.devices is not None and len(r.invitee.devices) > 0:
            item['device_binding_count'] = len(r.invitee.devices)
            item['device_binding_time'] = min(device.created_at for device in r.invitee.devices)
        else:
            item['device_binding_count'] = 0
            item['device_binding_time'] = None

        items.append(item)

    # 返回分页结果
    return Response.success(data={
        "items": items,
        "page": pagination.page,
        "per_page": pagination.per_page,
        "total": pagination.total
    })


@auth_bp.route('/send-code', methods=['POST'])
@jwt_required(optional=True)
def send_verification_code() -> object:
    user_id = get_jwt_identity() or 'guest'
    email = request.json.get('email')
    if not email:
        return Response.error("缺少必要参数")
    if '@' not in email:
        return Response.error("邮箱格式错误")
    return EmailService.send_verification_code(user_id, email)


@auth_bp.route('/get-captcha', methods=['GET'])
@cross_origin()
def get_captcha():
    try:
        img_byte_arr, captcha_id = CaptchaService.generate_captcha()
        response = make_response(img_byte_arr)
        response.headers.set('Content-Type', 'image/png')
        response.headers.set('Captcha-ID', captcha_id)
        return response
    except Exception as e:
        return Response.error("获取验证码图像错误", 500)


@auth_bp.route('/verify-code', methods=['POST'])
@jwt_required(optional=True)
def verify_code():
    user_id = get_jwt_identity() or 'guest'
    code = request.json.get('code')
    if not code:
        return Response.error("缺少必要参数")
    redis_key = f"verifycode{user_id}"
    stored_code = RedisClient.get_instance().get(redis_key)
    if not stored_code or stored_code != code:
        return Response.error("验证码错误或已过期")
    RedisClient.get_instance().delete(redis_key)
    return Response.success(message="验证成功")


@auth_bp.route('/statistics', methods=['GET'])
@jwt_required()
def get_user_statistics():
    """获取用户统计汇总信息"""
    try:
        # 获取当前用户
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        if not user:
            return jsonify({'code': 404, 'message': 'User not found'}), 404

        # 获取用户的所有设备
        devices = Device.query.filter_by(owner_id=user.id).all()
        device_ids = [d.id for d in devices]

        # 获取在线设备数量
        online_devices = Device.query.filter_by(
            owner_id=user.id,
            status=2  # running
        ).count()

        total_running_time = sum(
            metrics_service.get_device_total_running_time(id) for id in device_ids
        )

        total_points = UbiPointsIntegrationService.get_user_ubi_points(user_id=user.id)

        return jsonify({
            'code': 200,
            'data': {
                'total_devices': len(devices),
                'online_devices': int(online_devices),
                'total_running_time': float(total_running_time),
                'total_points': float(total_points)
            }
        })

    except Exception as e:
        current_app.logger.error(f'Error getting user statistics: {str(e)}')
        return jsonify({'code': 400, 'message': 'Failed to get statistics'}), 400


@auth_bp.route('/statistics/daily', methods=['GET'])
@jwt_required()
def get_daily_statistics():
    """获取用户每日统计数据"""
    try:
        # 获取请求参数
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        # 验证日期格式
        if not start_date or not end_date:
            return jsonify({'code': 400, 'message': 'Missing required parameters'}), 400

        # 获取当前用户
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        if not user:
            return jsonify({'code': 404, 'message': 'User not found'}), 404

        result = {}
        # 获取用户的所有设备
        # devices = Device.query.filter_by(owner_id=user.id).all()
        # device_ids = [d.id for d in devices]
        #
        # # 获取每天最晚的一条 ServiceMetricsSnapshot
        # # 使用窗口函数找出每个 device_id, service_name 和 day 下 created_at 和 updated_at 最晚的记录
        # window = func.row_number().over(
        #     partition_by=[ServiceMetricsSnapshot.device_id, ServiceMetricsSnapshot.service_name, ServiceMetricsSnapshot.day],
        #     order_by=[desc(ServiceMetricsSnapshot.created_at), desc(ServiceMetricsSnapshot.updated_at)]
        # )
        #
        # subquery = select(
        #     ServiceMetricsSnapshot,
        #     window.label('row_num')
        # ).where(
        #     ServiceMetricsSnapshot.device_id.in_(device_ids),
        #     ServiceMetricsSnapshot.day.between(start_date, end_date)
        # ).subquery()
        # # 主查询过滤出每个分组的第一条记录
        # latest_records_query = select(subquery).where(subquery.c.row_num == 1)
        #
        # running_time_metrics = db.session.execute(latest_records_query).all()
        #
        # # 格式化结果
        # result = {}
        #
        # # 添加运行时间数据
        # for metric in running_time_metrics:
        #     day = metric.day
        #     if day not in result:
        #         result[day] = []
        #     result[day].append({
        #         'service_name': metric.service_name,
        #         'running_time': metric.running_time or 0,
        #         'ubi_points': metric.ubi_points or 0,
        #         'points': metric.points or 0
        #     })

        # 从 PointRecord 表中获取每日积分数据
        # 使用数据库特定的日期转换函数
        from datetime import datetime

        # 转换日期字符串为 datetime 对象
        start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
        end_datetime = datetime.strptime(end_date, '%Y-%m-%d')
        end_datetime = datetime(end_datetime.year, end_datetime.month, end_datetime.day, 23, 59, 59)

        if db.engine.name == 'mysql':
            date_format_expr = func.DATE_FORMAT(PointRecord.created_at, '%Y-%m-%d')
        elif db.engine.name == 'sqlite':
            date_format_expr = func.strftime('%Y-%m-%d', PointRecord.created_at)
        else:
            raise ValueError(f"Unsupported database: {db.engine.name}")

        points_metrics = db.session.query(
            date_format_expr.label('day'),
            func.sum(PointRecord.points).label('points')
        ).filter(
            PointRecord.user_id == user.id,
            PointRecord.created_at.between(start_datetime, end_datetime)
        ).group_by(
            date_format_expr
        ).all()
        # 添加积分数据
        for metric in points_metrics:
            # 确保 day 是字符串格式
            day_str = metric.day.strftime('%Y-%m-%d') if hasattr(metric.day, 'strftime') else str(metric.day)

            if day_str not in result:
                result[day_str] = []
                result[day_str].append({
                    'service_name': 'all',  # 使用固定的服务名称
                    'running_time': 0,
                    'points': float(metric.points or 0.0)
                })
            else:
                # 添加新记录，不尝试合并
                result[day_str].append({
                    'service_name': 'all',  # 使用固定的服务名称
                    'running_time': 0,
                    'points': float(metric.points or 0.0)
                })
        return Response.success(result)

    except Exception as e:
        current_app.logger.error(f'Error getting daily statistics: {str(e)}')
        return jsonify({'code': 400, 'message': 'Failed to get daily statistics'}), 400


@auth_bp.route('/request_reset_password', methods=['POST'])
def request_reset_password():
    data = request.get_json()

    email = data.get('email')
    if not email:
        return Response.validation_error("缺少必要字段")

    if '@' not in email:
        return Response.error("邮箱格式错误")

    return EmailService.send_reset_password_code(email)


@auth_bp.route('/reset-password/<token>', methods=['POST'])
def reset_password(token):
    try:
        data = request.get_json()
        if not data or "password" not in data:
            return Response.validation_error("缺少必要字段")

        error, code = AuthService.reset_password(
            token=token,
            new_password=data["password"],
        )
        if error:
            return Response.error(error, 401)

        return Response.success(message="密码修改成功")

    except Exception as e:
        current_app.logger.error(f"Reset password error: {str(e)}")
        return Response.error("修改密码失败", 500)


# 错误处理
@auth_bp.errorhandler(NoAuthorizationError)
@auth_bp.errorhandler(InvalidHeaderError)
def handle_unauthorized_error(error):
    """处理未授权错误"""
    return Response.unauthorized("未登录或token已过期")


@auth_bp.errorhandler(404)
def handle_not_found_error(error):
    """处理资源未找到错误"""
    return Response.not_found("资源不存在")
