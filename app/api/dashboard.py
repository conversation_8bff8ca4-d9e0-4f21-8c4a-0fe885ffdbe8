"""仪表盘相关接口"""
from flask import Blueprint, current_app
from flask_jwt_extended import get_jwt_identity, jwt_required

from app.models.base import db
from app.models.user import User
from app.services.dashboard_service import DashboardService
from app.utils.response import Response

dashboard_bp = Blueprint("dashboard", __name__, url_prefix="/api/dashboard")

@dashboard_bp.route("/overview", methods=["GET"])
@jwt_required()
def get_overview():
    """获取仪表盘概览数据"""
    try:
        current_user = db.session.get(User, get_jwt_identity())
        data = DashboardService.get_overview_data(current_user)
        return Response.success(data)
    except Exception as e:
        current_app.logger.error(f"Get overview error: {str(e)}")
        return Response.error("获取概览数据失败", 500)

@dashboard_bp.route("/devices/status", methods=["GET"])
@jwt_required()
def get_device_status():
    """获取设备状态统计"""
    try:
        current_user = db.session.get(User, get_jwt_identity())
        data, error = DashboardService.get_device_status(current_user)
        if error:
            current_app.logger.error(f"Get device status error: {error}")
            return Response.error("获取设备状态统计失败", 500)
        return Response.success(data)
    except Exception as e:
        current_app.logger.error(f"Get device status error: {str(e)}")
        return Response.error("获取设备状态统计失败", 500)

