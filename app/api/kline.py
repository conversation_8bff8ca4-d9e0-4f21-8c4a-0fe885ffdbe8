"""
K线API
支持多种查询方式、统计数据等
"""

from flask import Blueprint, request
from flask_jwt_extended import jwt_required

from app.models.kline import KlineData
from app.services.kline_service import KlineService
from app.utils.decorators import handle_api_errors
from app.utils.response import Response
from app.utils.errors import BusinessException

kline_bp = Blueprint('kline', __name__, url_prefix='/api/kline')


@kline_bp.route('/<int:pair_id>', methods=['GET'])
@jwt_required()
@handle_api_errors
def get_kline_data(pair_id):
    """获取K线数据"""
    try:
        # 参数获取和验证
        interval = request.args.get('interval', 'H1')
        start_time = request.args.get('start', type=int)
        end_time = request.args.get('end', type=int)
        limit = request.args.get('limit', 1000, type=int)

        # 验证交易对ID
        if pair_id <= 0:
            return Response.success(data=None, message="无效的交易对ID", code=400)

        # 验证时间间隔
        if interval not in KlineService.INTERVALS:
            return Response.error(f"不支持的时间间隔: {interval}", 400)

        # 验证limit
        if limit <= 0 or limit > 2000:
            return Response.success(data=None, message="limit 必须在 1-2000 之间", code=400)

        # 获取K线数据
        kline_data = KlineService.get_kline_data(
            pair_id=pair_id,
            interval=interval,
            start_time=start_time,
            end_time=end_time,
            limit=limit
        )

        return Response.success({
            "pair_id": pair_id,
            "interval": interval,
            "data": kline_data,
            "count": len(kline_data)
        })

    except BusinessException as e:
        return Response.error(e.message, e.code)
    except Exception as e:
        return Response.error(f"获取K线数据失败: {str(e)}", 500)


@kline_bp.route('/<int:pair_id>/latest', methods=['GET'])
@jwt_required()
def get_latest_price(pair_id):
    """获取最新价格"""
    try:
        if pair_id <= 0:
            return Response.success(data=None, message="无效的交易对ID", code=400)

        latest_price = KlineService.get_latest_price(pair_id)

        if latest_price is None:
            return Response.success(data=None, message="未找到价格数据", code=200)

        return Response.success({
            "pair_id": pair_id,
            "latest_price": str(latest_price)
        })

    except Exception as e:
        return Response.error(f"获取最新价格失败: {str(e)}", 500)


@kline_bp.route('/<int:pair_id>/stats', methods=['GET'])
@jwt_required()
def get_24h_stats(pair_id):
    """获取24小时统计数据"""
    try:
        if pair_id <= 0:
            return Response.success(data=None, message="无效的交易对ID", code=400)

        stats = KlineService.get_24h_stats(pair_id)

        return Response.success({
            "pair_id": pair_id,
            "stats_24h": stats
        })

    except Exception as e:
        return Response.error(f"获取24小时统计失败: {str(e)}", 500)


@kline_bp.route('/intervals', methods=['GET'])
@jwt_required()
def get_supported_intervals():
    """获取支持的时间间隔"""
    try:
        intervals = [
            {
                "interval": interval,
                "seconds": seconds,
                "description": _get_interval_description(interval)
            }
            for interval, seconds in KlineService.INTERVALS.items()
        ]

        return Response.success({
            "intervals": intervals
        })

    except Exception as e:
        return Response.error(f"获取时间间隔失败: {str(e)}", 500)


def _get_interval_description(interval: str) -> str:
    """获取时间间隔描述"""
    descriptions = {
        'M1': '1分钟',
        'M5': '5分钟',
        'M15': '15分钟',
        'M30': '30分钟',
        'H1': '1小时',
        'H4': '4小时',
        'H6': '6小时',
        'H12': '12小时',
        'D1': '1天',
        'W1': '1周'
    }
    return descriptions.get(interval, interval)