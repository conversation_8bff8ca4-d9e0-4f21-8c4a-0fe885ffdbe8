from decimal import Decimal

from flask import Blueprint, request, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity

from app.models import db, User
from app.models.blockchain import Blockchain, Token
from app.models.wallet import WalletGenerated
from app.services.wallet_service import WalletService
from app.utils.response import Response

wallets_bp = Blueprint('wallets', __name__, url_prefix='/api/wallets')


@wallets_bp.route('/chains', methods=['GET'])
@jwt_required()
def get_chains():
    """获取网络列表接口"""
    chains = []
    blockchain_list = Blockchain.query.all()
    for chain in blockchain_list:
        chains.append({
            'name': chain.chain_name,
            'code': chain.chain_code
        })
    return Response.success(chains)


@wallets_bp.route('/tokens', methods=['GET'])
@jwt_required()
def get_tokens():
    """
    获取链上支持的币种接口

    Query params:
        chain_code: 网络编码
    """
    chain_code = request.args.get('chain_code')
    tokens = WalletService.get_supported_tokens(chain_code)
    return Response.success(tokens)


@wallets_bp.route('', methods=['POST'])
@jwt_required()
def create_wallet_group():
    """
    创建钱包组接口
    """
    user_id = get_jwt_identity()
    user = db.session.get(User, int(user_id))
    if not user:
        return Response.error("用户不存在", 404)
    try:
        data = request.get_json()
        device_id = data.get("device_id", 0)
        response_data, error = WalletService.create_wallet_group(user_id, device_id)
        if error:
            return Response.error(error)
        return Response.success(response_data)
    except Exception as e:
        current_app.logger.error(f'Wallet group creation failed: {str(e)}')
        return Response.error('钱包创建失败', 500)


@wallets_bp.route('', methods=['GET'])
@jwt_required()
def get_wallet_groups():
    """
    查询钱包组列表

    Query params:
        page: Page number (default: 1)
        per_page: Items per page (default: 10, max: 100)
        chain_code: Optional chain code filter
        exist_balance: Only return wallets with balance (default: false)
        token_code: Optional token symbol filter
        include_internal: Include internal asset balances (default: true)
    """
    user_id = get_jwt_identity()

    # 获取分页参数
    page = request.args.get('page', 1, type=int)
    per_page = min(request.args.get('per_page', 10, type=int), 100)

    # 链类型参数校验
    chain_code = request.args.get('chain_code')
    if chain_code:
        chain = Blockchain.query.filter_by(chain_code=chain_code).first()
        if not chain:
            return Response.error(f'无效的链代码: {chain_code}', 400)

    # 代币ID参数校验
    token_code = request.args.get('token_code')
    if token_code:
        token = Token.query.filter_by(token_code=token_code).first()
        if not token:
            return Response.error(f'无效的代币code: {token_code}', 400)

    device_id = request.args.get('device_id', 0, type=int)

    exist_balance_str = request.args.get('exist_balance')
    if exist_balance_str is not None:
        exist_balance = exist_balance_str.lower() == 'true'
    else:
        exist_balance = False

    try:
        # 获取链上钱包余额
        pagination_data = WalletService.get_wallet_groups(
            user_id=user_id,
            device_id=device_id,
            page=page,
            per_page=per_page,
            chain_code=chain_code,
            exist_balance=exist_balance,
            token_code=token_code
        )

        return Response.success(pagination_data)
    except Exception as e:
        current_app.logger.error(f'Query wallet groups failed: {str(e)}')
        return Response.error('查询失败', 500)


@wallets_bp.route("/<wallet_address>/valid", methods=["GET"])
@jwt_required()
def is_valid_address(wallet_address: str):
    """valid wallet address for a specific chain"""
    chain_code = request.args.get("chain_code")
    if not chain_code:
        return Response.error('chain_symbol is required')

    chain = Blockchain.query.filter_by(chain_code=chain_code).first()
    if not chain:
        return Response.error('Invalid chain symbol')

    if not wallet_address:
        return Response.error('wallet_address is required')

    return Response.success(WalletService.is_valid_address(wallet_address, chain))


@wallets_bp.route("/transfer", methods=["GET"])
@jwt_required()
def get_transaction_status():
    """Get transaction status and details"""
    tx_hash = request.args.get("tx_hash")
    if not tx_hash:
        return Response.error("tx_hash is required", 400)

    try:
        # Get transaction details from wallet log and blockchain
        tx_info = WalletService.get_transaction_status(tx_hash)
        if not tx_info:
            return Response.error("Transaction not found", 404)

        return Response.success(tx_info)
    except Exception as e:
        current_app.logger.error(f"Failed to get transaction status: {str(e)}")
        return Response.error("Failed to get transaction status", 500)


@wallets_bp.route("/transfer/estimate", methods=["POST"])
@jwt_required()
def estimate_transfer():
    """
    Estimate gas fees and transfer time for a blockchain transaction

    Request body:
    {
        "chain_code": str,
        "sender_address": str,
        "receiver_address": str,
        "value": str,
        "token_code": str (optional, if not provided native token is used)
    }
    """
    try:
        data = request.get_json()

        # Validate required fields
        required_fields = ["chain_code", "sender_address", "receiver_address", "value"]
        for field in required_fields:
            if field not in data:
                return Response.error(f"{field} is required", 400)

        chain = Blockchain.query.filter_by(chain_code=data["chain_code"]).first()
        if not chain:
            return Response.error("Invalid chain code", 400)

        # Validate token_code if provided
        token_code = data.get("token_code")
        if token_code:
            token = Token.query.filter_by(token_code=token_code).first()
            if not token:
                return Response.error(f"Invalid token code: {token_code}", 400)

        # Convert value to Decimal for precise calculation
        try:
            value = Decimal(data["value"])
        except (TypeError, ValueError):
            return Response.error("Invalid value format", 400)

        estimate_data = WalletService.estimate_transfer(
            chain=chain,
            sender_address=data["sender_address"],
            receiver_address=data["receiver_address"],
            value=value,
            token_code=token_code
        )

        return Response.success(estimate_data)
    except Exception as e:
        current_app.logger.error(f"Transfer estimation failed: {str(e)}")
        return Response.error(str(e), 500)


@wallets_bp.route("/transfer", methods=["POST"])
@jwt_required()
def create_transfer():
    """
    创建转账交易

    Request body:
    {
        "chain_code": str,
        "wallet_id": int,
        "sender_address": str,
        "receiver_address": str,
        "value": str,
        "token_code": str (optional, if not provided native token is used),
        "use_internal_balance": bool (optional, default: true - use internal balance if available)
    }
    """
    try:
        user_id = get_jwt_identity()
        data = request.get_json()

        # Validate required fields
        required_fields = ["chain_code", "wallet_id", "sender_address", "receiver_address", "value", "token_code"]
        for field in required_fields:
            if field not in data:
                return Response.error(f"{field} is required", 400)

        chain = Blockchain.query.filter_by(chain_code=data["chain_code"]).first()
        if not chain:
            return Response.error("Invalid chain code", 400)

        # Validate token_code if provided
        token_code = data.get("token_code")
        if token_code:
            token = Token.query.filter_by(token_code=token_code).first()
            if not token:
                return Response.error(f"Invalid token code: {token_code}", 400)
            if token.chain_id != chain.id:
                return Response.error(f"Token {token_code} is not on chain {chain.chain_code}", 400)

        # Convert value to Decimal for precise calculation
        try:
            value = Decimal(data["value"])
        except (TypeError, ValueError):
            return Response.error("Invalid value format", 400)

        # Verify wallet ownership
        wallet = db.session.get(WalletGenerated, data["wallet_id"])
        if not wallet or wallet.user_id != int(user_id):
            return Response.error("Wallet not found or unauthorized", 404)

        if wallet.address.lower() != data["sender_address"].lower():
            return Response.error("Sender address does not match wallet address", 400)

        # 是否使用内部资产余额
        use_internal_balance = data.get("use_internal_balance", True)

        # 如果使用内部余额，先检查内部资产余额是否足够
        if use_internal_balance:
            from app.models.blockchain import AssetType, AssetTypeEnum
            from app.models.asset import UserAsset, TransactionTypeEnum
            from app.services.asset_service import AssetService

            # 获取代币信息
            token = Token.query.filter_by(token_code=token_code).first()
            if token:
                # 查询对应的资产类型
                asset_type = db.session.query(AssetType).filter(
                    AssetType.type == AssetTypeEnum.TOKEN,
                    AssetType.token_id == token.id
                ).first()

                if asset_type:
                    # 查询用户在该资产类型上的内部余额
                    user_asset = db.session.query(UserAsset).filter(
                        UserAsset.user_id == user_id,
                        UserAsset.asset_type_id == asset_type.id
                    ).first()

                    if user_asset and user_asset.available_balance >= value:
                        # 内部余额足够，扣除内部资产
                        try:

                            # 创建转账交易
                            transfer_result = WalletService.create_transfer(
                                user_id=user_id,
                                chain=chain,
                                wallet_id=data["wallet_id"],
                                sender_address=data["sender_address"],
                                receiver_address=data["receiver_address"],
                                value=value,
                                token_code=token_code
                            )

                            return Response.success({"tx_hash": transfer_result["tx_hash"]})
                        except Exception as e:
                            current_app.logger.error(f"Subtract internal balance failed: {str(e)}")
                            return Response.error(f": {str(e)}", 500)
                    elif user_asset:
                        # 内部余额不足，返回错误
                        return Response.error(f"可用余额不足，当前可用余额: {user_asset.available_balance}")

        # 不使用内部余额或没有对应的资产类型，直接创建转账交易
        transfer_result = WalletService.create_transfer(
            user_id=user_id,
            chain=chain,
            wallet_id=data["wallet_id"],
            sender_address=data["sender_address"],
            receiver_address=data["receiver_address"],
            value=value,
            token_code=token_code
        )

        return Response.success({"tx_hash": transfer_result["tx_hash"]})
    except Exception as e:
        current_app.logger.error(f"Transfer creation failed: {str(e)}")
        return Response.error(str(e), 500)
