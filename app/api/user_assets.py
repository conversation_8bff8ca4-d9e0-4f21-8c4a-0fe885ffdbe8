"""用户资产管理API"""

from flask import Blueprint, request
from flask_jwt_extended import jwt_required, get_jwt_identity
from datetime import datetime

from app.models.asset import UserAsset
from app.models.blockchain import AssetType, AssetTypeEnum
from app.models.user import User
from app.models.base import db
from app.utils.auth import get_current_user
from app.utils.decorators import handle_api_errors
from app.utils.response import Response

user_assets_bp = Blueprint("user_assets", __name__)


@user_assets_bp.route('', methods=['GET'])
@jwt_required()
@handle_api_errors
def list_user_assets():
    """获取用户资产列表
    
    获取当前用户的所有资产信息，包含完整的资产类型、区块链、项目等详细信息。
    
    Args:
        Query Parameters:
            asset_type (str, optional): 资产类型过滤，可选值：POINTS, TOKEN
            has_balance (bool, optional): 是否只显示有余额的资产，默认false
            page (int, optional): 页码，默认为1
            per_page (int, optional): 每页数量，默认为20
    
    Returns:
        Response: 用户资产列表
            - total: 总数量
            - items: 资产数组，每个资产包含：
                - user_id: 用户ID
                - user_name: 用户名称
                - asset_type_id: 资产类型ID
                - asset_name: 资产名称
                - asset_type: 资产类型（POINTS/TOKEN）
                - available_balance: 可用余额
                - frozen_balance: 冻结余额
                - occupied_balance: 占用余额
                - total_balance: 总余额
                - decimals: 小数位数
                - blockchain_info: 区块链信息（如果是加密货币）
                - project_info: 项目信息（如果是积分）
            - page: 当前页码
            - per_page: 每页数量
    
    Example:
        GET /api/user-assets?asset_type=CRYPTO&has_balance=true
    """

    current_user = get_current_user()
    target_user_id = current_user.id

    if current_user.is_admin and request.args.get('user_id'):
        target_user_id = request.args.get('user_id')

    # 获取查询参数
    asset_type_filter = request.args.get('asset_type')
    has_balance = request.args.get('has_balance', 'false').lower() == 'true'
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    
    # 构建查询，包含用户信息
    query = db.session.query(UserAsset, AssetType, User).join(
        AssetType, UserAsset.asset_type_id == AssetType.id
    ).join(
        User, UserAsset.user_id == User.id
    ).filter(UserAsset.user_id == target_user_id)
    
    # 资产类型过滤
    if asset_type_filter:
        try:
            asset_type_enum = AssetTypeEnum[asset_type_filter]
            query = query.filter(AssetType.type == asset_type_enum)
        except KeyError:
            return Response.error(f"无效的资产类型: {asset_type_filter}", 400)
    
    # 余额过滤
    if has_balance:
        query = query.filter(
            (UserAsset.available_balance > 0) |
            (UserAsset.frozen_balance > 0) |
            (UserAsset.occupied_balance > 0)
        )
    
    # 分页
    total = query.count()
    items = query.order_by(AssetType.type, AssetType.name).offset(
        (page - 1) * per_page
    ).limit(per_page).all()
    
    # 构建响应数据
    asset_list = []
    for user_asset, asset_type, user in items:
        asset_info = {
            "user_id": user.id,
            "user_name": user.username,
            "asset_type_id": asset_type.id,
            "asset_name": asset_type.name,
            "asset_type": asset_type.type.name,
            "available_balance": str(user_asset.available_balance),
            "frozen_balance": str(user_asset.frozen_balance),
            "occupied_balance": str(user_asset.occupied_balance),
            "total_balance": str(
                user_asset.available_balance +
                user_asset.frozen_balance +
                user_asset.occupied_balance
            ),
            "decimals": asset_type.decimals,
            "created_at": user_asset.created_at.isoformat() if user_asset.created_at else None,
            "updated_at": user_asset.updated_at.isoformat() if user_asset.updated_at else None
        }
        
        # 添加区块链信息（加密货币）
        if asset_type.type == AssetTypeEnum.TOKEN and asset_type.token:
            blockchain_info = {
                "chain_type": asset_type.chain_type,
                "token_symbol": asset_type.token.token_symbol,
                "contract_address": asset_type.token.contract_address
            }
            
            if asset_type.token.blockchain:
                blockchain_info.update({
                    "blockchain_name": asset_type.token.blockchain.chain_name,
                    "blockchain_code": asset_type.token.blockchain.chain_code
                })
            
            asset_info["blockchain_info"] = blockchain_info
        
        # 添加项目信息（积分）
        elif asset_type.type == AssetTypeEnum.POINTS:
            project_info = {
                "project_id": asset_type.project_id
            }
            # 这里可以添加项目详细信息，等项目关系完善后
            asset_info["project_info"] = project_info
        
        asset_list.append(asset_info)
    
    return Response.success({
        "total": total,
        "items": asset_list,
        "page": page,
        "per_page": per_page
    })


@user_assets_bp.route('/<int:asset_type_id>', methods=['GET'])
@jwt_required()
@handle_api_errors
def get_user_asset(asset_type_id):
    """获取用户特定资产详情
    
    获取用户指定资产类型的详细信息和余额。
    
    Args:
        asset_type_id (int): 资产类型ID
    
    Returns:
        Response: 用户资产详情
    
    Raises:
        404: 资产不存在或用户没有该资产
    
    Example:
        GET /api/user-assets/1
    """
    current_user_id = get_jwt_identity()
    
    # 查询用户资产、资产类型和用户信息
    result = db.session.query(UserAsset, AssetType, User).join(
        AssetType, UserAsset.asset_type_id == AssetType.id
    ).join(
        User, UserAsset.user_id == User.id
    ).filter(
        UserAsset.user_id == current_user_id,
        UserAsset.asset_type_id == asset_type_id
    ).first()

    if not result:
        return Response.error('用户资产不存在', 404)

    user_asset, asset_type, user = result
    
    # 构建详细信息
    asset_info = {
        "user_id": user.id,
        "user_name": user.username,
        "asset_type_id": asset_type.id,
        "asset_name": asset_type.name,
        "asset_type": asset_type.type.name,
        "available_balance": str(user_asset.available_balance),
        "frozen_balance": str(user_asset.frozen_balance),
        "occupied_balance": str(user_asset.occupied_balance),
        "total_balance": str(
            user_asset.available_balance +
            user_asset.frozen_balance +
            user_asset.occupied_balance
        ),
        "decimals": asset_type.decimals,
        "created_at": user_asset.created_at.isoformat() if user_asset.created_at else None,
        "updated_at": user_asset.updated_at.isoformat() if user_asset.updated_at else None
    }
    
    # 添加区块链信息（加密货币）
    if asset_type.type == AssetTypeEnum.TOKEN and asset_type.token:
        blockchain_info = {
            "chain_type": asset_type.chain_type,
            "token_symbol": asset_type.token.token_symbol,
            "contract_address": asset_type.token.contract_address
        }
        
        if asset_type.token.blockchain:
            blockchain_info.update({
                "blockchain_name": asset_type.token.blockchain.chain_name,
                "blockchain_code": asset_type.token.blockchain.chain_code
            })
        
        asset_info["blockchain_info"] = blockchain_info
    
    # 添加项目信息（积分）
    elif asset_type.type == AssetTypeEnum.POINTS:
        project_info = {
            "project_id": asset_type.project_id
        }
        # 这里可以添加项目详细信息，等项目关系完善后
        asset_info["project_info"] = project_info
    
    return Response.success(asset_info)


@user_assets_bp.route('/summary', methods=['GET'])
@jwt_required()
@handle_api_errors
def get_assets_summary():
    """获取用户资产汇总信息
    
    获取用户资产的汇总统计信息。
    
    Returns:
        Response: 资产汇总信息
            - total_assets: 总资产种类数
            - token_assets: 加密货币资产数
            - points_assets: 积分资产数
            - assets_with_balance: 有余额的资产数
            - total_value: 总价值（如果有价格数据）
    
    Example:
        GET /api/user-assets/summary
    """
    current_user_id = get_jwt_identity()
    
    # 查询用户所有资产
    assets = db.session.query(UserAsset, AssetType).join(
        AssetType, UserAsset.asset_type_id == AssetType.id
    ).filter(UserAsset.user_id == current_user_id).all()
    
    # 统计信息
    total_assets = len(assets)
    token_assets = sum(1 for _, asset_type in assets if asset_type.type == AssetTypeEnum.TOKEN)
    points_assets = sum(1 for _, asset_type in assets if asset_type.type == AssetTypeEnum.POINTS)
    assets_with_balance = sum(
        1 for user_asset, _ in assets 
        if (user_asset.available_balance + user_asset.frozen_balance + user_asset.occupied_balance) > 0
    )
    
    return Response.success({
        "total_assets": total_assets,
        "token_assets": token_assets,
        "points_assets": points_assets,
        "assets_with_balance": assets_with_balance,
        "last_updated": datetime.now().isoformat() if assets else None
    })
