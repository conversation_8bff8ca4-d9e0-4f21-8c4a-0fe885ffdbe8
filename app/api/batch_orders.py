"""批量订单操作API - 用于测试工具"""

from flask import Blueprint, request
from flask_jwt_extended import  get_jwt_identity

from app.middlewares.auth import admin_required
from app.models.base import db
from app.utils.decorators import handle_api_errors
from app.utils.response import Response
from app.utils.errors import BusinessException
from app.services.order_service import OrderService

batch_orders_bp = Blueprint("batch_orders", __name__)


@batch_orders_bp.route('/batch', methods=['POST'])
@admin_required
@handle_api_errors
def batch_create_orders():
    """批量创建订单
    
    用于测试工具批量创建多个订单，方便进行压力测试和功能验证。
    
    Args:
        Request Body:
            orders (list): 订单列表，每个订单包含：
                - order_type: 订单类型（LIMIT/MARKET）
                - side: 买卖方向（BUY/SELL）
                - pair_id: 交易对ID
                - price: 价格（限价单必填）
                - amount: 数量
    
    Returns:
        Response: 批量创建结果
            - success_count: 成功创建的订单数
            - failed_count: 创建失败的订单数
            - results: 每个订单的创建结果
    
    Example:
        POST /api/orders/batch
        {
            "orders": [
                {
                    "order_type": "LIMIT",
                    "side": "BUY",
                    "pair_id": 1,
                    "price": "50000.00",
                    "amount": "0.10000000"
                }
            ]
        }
    """
    current_user_id = get_jwt_identity()
    
    data = request.get_json()
    if not data or 'orders' not in data:
        return Response.error("请求数据不能为空", 400)
    
    orders = data['orders']
    if not isinstance(orders, list):
        return Response.error("orders必须是数组", 400)
    
    if len(orders) == 0:
        return Response.error("订单列表不能为空", 400)
    
    if len(orders) > 100:  # 限制批量创建的数量
        return Response.error("单次最多创建100个订单", 400)
    
    results = []
    success_count = 0
    failed_count = 0
    
    for index, order_data in enumerate(orders):
        try:
            # 验证必要字段
            required_fields = ['order_type', 'side', 'pair_id', 'amount']
            for field in required_fields:
                if field not in order_data:
                    raise BusinessException(f"缺少必要参数: {field}", 400)
            
            # 添加用户ID
            create_order_params = {
                "user_id": current_user_id,
                "order_type": order_data['order_type'],
                "side": order_data['side'],
                "pair_id": order_data['pair_id'],
                "price": order_data.get('price'),
                "amount": order_data['amount']
            }
            
            # 创建订单
            order = OrderService.create_order(create_order_params)
            
            results.append({
                "index": index,
                "success": True,
                "order_id": order.id,
                "message": "订单创建成功"
            })
            success_count += 1
            
        except BusinessException as e:
            results.append({
                "index": index,
                "success": False,
                "order_id": None,
                "message": e.message
            })
            failed_count += 1
            
        except Exception as e:
            results.append({
                "index": index,
                "success": False,
                "order_id": None,
                "message": f"创建订单失败: {str(e)}"
            })
            failed_count += 1
    
    return Response.success({
        "success_count": success_count,
        "failed_count": failed_count,
        "total_count": len(orders),
        "results": results
    })


@batch_orders_bp.route('/batch', methods=['DELETE'])
@admin_required
@handle_api_errors
def batch_cancel_orders():
    """批量取消订单
    
    用于测试工具批量取消多个订单，方便清理测试数据。
    
    Args:
        Request Body:
            order_ids (list): 要取消的订单ID列表
    
    Returns:
        Response: 批量取消结果
            - success_count: 成功取消的订单数
            - failed_count: 取消失败的订单数
            - results: 每个订单的取消结果
    
    Example:
        DELETE /api/orders/batch
        {
            "order_ids": [1001, 1002, 1003]
        }
    """
    current_user_id = get_jwt_identity()
    
    data = request.get_json()
    if not data or 'order_ids' not in data:
        return Response.error("请求数据不能为空", 400)
    
    order_ids = data['order_ids']
    if not isinstance(order_ids, list):
        return Response.error("order_ids必须是数组", 400)
    
    if len(order_ids) == 0:
        return Response.error("订单ID列表不能为空", 400)
    
    if len(order_ids) > 100:  # 限制批量取消的数量
        return Response.error("单次最多取消100个订单", 400)
    
    results = []
    success_count = 0
    failed_count = 0
    
    for order_id in order_ids:
        try:
            # 验证订单ID
            if not isinstance(order_id, int) or order_id <= 0:
                raise BusinessException("无效的订单ID", 400)
            
            # 取消订单
            updated_order = OrderService.cancel_order(order_id, current_user_id)
            
            results.append({
                "order_id": order_id,
                "success": True,
                "message": "订单取消成功"
            })
            success_count += 1
            
        except BusinessException as e:
            results.append({
                "order_id": order_id,
                "success": False,
                "message": e.message
            })
            failed_count += 1
            
        except Exception as e:
            results.append({
                "order_id": order_id,
                "success": False,
                "message": f"取消订单失败: {str(e)}"
            })
            failed_count += 1
    
    return Response.success({
        "success_count": success_count,
        "failed_count": failed_count,
        "total_count": len(order_ids),
        "results": results
    })


@batch_orders_bp.route('/cancel-all', methods=['POST'])
@admin_required
@handle_api_errors
def cancel_all_pending_orders():
    """取消所有待成交订单
    
    用于测试工具一键清空所有待成交的订单，方便重置测试环境。
    
    Args:
        Request Body (optional):
            pair_id (int): 可选，只取消指定交易对的订单
    
    Returns:
        Response: 取消结果
            - canceled_count: 取消的订单数
            - failed_count: 取消失败的订单数
            - order_ids: 被取消的订单ID列表
    
    Example:
        POST /api/orders/cancel-all
        {
            "pair_id": 1  // 可选
        }
    """
    current_user_id = get_jwt_identity()
    
    data = request.get_json() or {}
    pair_id = data.get('pair_id')
    
    try:
        from app.models.order import Order, OrderStatusEnum
        
        # 查询用户的待成交和部分成交订单
        query = db.session.query(Order).filter(
            Order.user_id == current_user_id,
            Order.status.in_([OrderStatusEnum.PENDING, OrderStatusEnum.PARTIAL])
        )
        
        # 如果指定了交易对，添加过滤条件
        if pair_id:
            query = query.filter(Order.pair_id == pair_id)
        
        pending_orders = query.all()
        
        if not pending_orders:
            return Response.success({
                "canceled_count": 0,
                "failed_count": 0,
                "total_count": 0,
                "order_ids": [],
                "message": "没有待取消的订单"
            })
        
        canceled_orders = []
        failed_orders = []
        
        for order in pending_orders:
            try:
                OrderService.cancel_order(order.id, current_user_id)
                canceled_orders.append(order.id)
            except Exception as e:
                failed_orders.append({
                    "order_id": order.id,
                    "error": str(e)
                })
        
        return Response.success({
            "canceled_count": len(canceled_orders),
            "failed_count": len(failed_orders),
            "total_count": len(pending_orders),
            "order_ids": canceled_orders,
            "failed_orders": failed_orders,
            "message": f"成功取消 {len(canceled_orders)} 个订单"
        })
        
    except Exception as e:
        return Response.error(f"取消订单失败: {str(e)}", 500)


@batch_orders_bp.route('/generate-test-orders', methods=['POST'])
@admin_required
@handle_api_errors
def generate_test_orders():
    """生成测试订单
    
    用于测试工具快速生成一批测试订单，方便进行功能测试。
    
    Args:
        Request Body:
            pair_id (int): 交易对ID
            count (int): 生成订单数量，最大100
            price_range (dict): 价格范围 {"min": "49000", "max": "51000"}
            amount_range (dict): 数量范围 {"min": "0.01", "max": "1.0"}
            buy_ratio (float): 买单比例，默认0.5
    
    Returns:
        Response: 生成结果
    
    Example:
        POST /api/orders/generate-test-orders
        {
            "pair_id": 1,
            "count": 10,
            "price_range": {"min": "49000", "max": "51000"},
            "amount_range": {"min": "0.01", "max": "1.0"},
            "buy_ratio": 0.6
        }
    """
    current_user_id = get_jwt_identity()
    
    data = request.get_json()
    if not data:
        return Response.error("请求数据不能为空", 400)
    
    # 验证必要参数
    required_fields = ['pair_id', 'count']
    for field in required_fields:
        if field not in data:
            return Response.error(f"缺少必要参数: {field}", 400)
    
    pair_id = data['pair_id']
    count = data['count']
    
    if count <= 0 or count > 100:
        return Response.error("订单数量必须在1-100之间", 400)
    
    # 默认参数
    price_range = data.get('price_range', {"min": "49000", "max": "51000"})
    amount_range = data.get('amount_range', {"min": "0.01", "max": "1.0"})
    buy_ratio = data.get('buy_ratio', 0.5)
    
    try:
        import random
        from decimal import Decimal
        
        orders = []
        
        for i in range(count):
            # 随机生成买卖方向
            side = "BUY" if random.random() < buy_ratio else "SELL"
            
            # 随机生成价格
            min_price = Decimal(str(price_range['min']))
            max_price = Decimal(str(price_range['max']))
            price = min_price + (max_price - min_price) * Decimal(str(random.random()))
            price = price.quantize(Decimal('0.01'))  # 保留2位小数
            
            # 随机生成数量
            min_amount = Decimal(str(amount_range['min']))
            max_amount = Decimal(str(amount_range['max']))
            amount = min_amount + (max_amount - min_amount) * Decimal(str(random.random()))
            amount = amount.quantize(Decimal('0.00000001'))  # 保留8位小数
            
            orders.append({
                "order_type": "LIMIT",
                "side": side,
                "pair_id": pair_id,
                "price": str(price),
                "amount": str(amount)
            })

        results = []
        success_count = 0
        failed_count = 0
        
        for index, order_data in enumerate(orders):
            try:
                create_order_params = {
                    "user_id": current_user_id,
                    "order_type": order_data['order_type'],
                    "side": order_data['side'],
                    "pair_id": order_data['pair_id'],
                    "price": order_data['price'],
                    "amount": order_data['amount']
                }
                
                order = OrderService.create_order(create_order_params)
                
                results.append({
                    "index": index,
                    "success": True,
                    "order_id": order.id,
                    "side": order_data['side'],
                    "price": order_data['price'],
                    "amount": order_data['amount']
                })
                success_count += 1
                
            except Exception as e:
                results.append({
                    "index": index,
                    "success": False,
                    "error": str(e)
                })
                failed_count += 1
        
        return Response.success({
            "success_count": success_count,
            "failed_count": failed_count,
            "total_count": count,
            "results": results,
            "message": f"成功生成 {success_count} 个测试订单"
        })
        
    except Exception as e:
        return Response.error(f"生成测试订单失败: {str(e)}", 500)
