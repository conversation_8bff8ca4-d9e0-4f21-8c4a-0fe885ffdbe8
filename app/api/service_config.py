"""公共服务配置相关的 API

这些 API 用于管理系统中的公共服务配置模板，这些模板可以被多个项目复用。
所有接口都需要管理员权限。
"""
from flask import Blueprint, request

from app.middlewares.auth import admin_required
from app.models.base import db
from app.models.service_config import ServiceConfig
from app.utils.response import Response

bp = Blueprint("service_config", __name__)

@bp.route("", methods=["GET"])
@admin_required
def list_service_configs():
    """获取公共服务配置列表"""
    configs = ServiceConfig.query.all()
    return Response.success([config.to_dict() for config in configs])

@bp.route("", methods=["POST"])
@admin_required
def create_service_config():
    """创建公共服务配置模板"""
    data = request.get_json()

    # 验证必填字段
    if not data.get("name") or not data.get("docker_compose"):
        return Response.error("名称和 docker-compose 配置为必填项", 400)

    # 检查名称是否已存在
    if ServiceConfig.query.filter_by(name=data["name"]).first():
        return Response.error("该名称已存在", 400)

    config = ServiceConfig(
        name=data["name"],
        description=data.get("description"),
        docker_compose=data["docker_compose"],
        default_env=data.get("env", {})
    )

    db.session.add(config)
    db.session.commit()

    return Response.success(config.to_dict())

@bp.route("/<int:config_id>", methods=["PUT"])
@admin_required
def update_service_config(config_id):
    """更新公共服务配置模板"""
    config = ServiceConfig.query.get(config_id)
    if not config:
        return Response.not_found("服务配置不存在")

    data = request.get_json()

    # 如果要更新名称，检查新名称是否已存在
    if "name" in data and data["name"] != config.name:
        if ServiceConfig.query.filter_by(name=data["name"]).first():
            return Response.error("该名称已存在", 400)
        config.name = data["name"]

    if "description" in data:
        config.description = data["description"]
    if "docker_compose" in data:
        config.docker_compose = data["docker_compose"]
    if "env" in data:
        config.default_env = data["env"]

    db.session.commit()
    return Response.success(config.to_dict())

@bp.route("/<int:config_id>", methods=["DELETE"])
@admin_required
def delete_service_config(config_id):
    """删除公共服务配置模板"""
    config = ServiceConfig.query.get(config_id)
    if not config:
        return Response.not_found("服务配置不存在")

    db.session.delete(config)
    db.session.commit()
    return Response.success()

@bp.route("/<int:config_id>", methods=["GET"])
@admin_required
def get_service_config(config_id):
    """获取单个公共服务配置模板详情"""
    config = ServiceConfig.query.get(config_id)
    if not config:
        return Response.not_found("服务配置不存在")

    return Response.success(config.to_dict())
