import os
import requests
from flask import Blueprint, request, redirect
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.services.points_service import PointsService
from app.utils.response import Response
from app.models.points import PointRecord
from sqlalchemy import func
import time

DISCORD_CLIENT_ID = os.getenv('DISCORD_CLIENT_ID')
DISCORD_CLIENT_SECRET = os.getenv('DISCORD_CLIENT_SECRET')
DISCORD_REDIRECT_URI = os.getenv('DISCORD_REDIRECT_URI')
DISCORD_BOT_TOKEN = os.getenv('DISCORD_BOT_TOKEN')
DISCORD_GUILD_ID = os.getenv('DISCORD_GUILD_ID')

BOT_TOKEN = os.getenv('BOT_TOKEN')
TARGET_CHAT_ID = os.getenv('TARGET_CHAT_ID')
CREATE_INVITE_URL = f"https://api.telegram.org/bot{BOT_TOKEN}/createChatInviteLink"

bp = Blueprint('points', __name__)

@bp.route('/task', methods=['POST'])
@jwt_required()
def complete_task():
    user_id = get_jwt_identity()
    data = request.get_json()
    return PointsService.add_task_points(user_id, data.get('task_type'))

@bp.route('/task', methods=['GET'])
@jwt_required()
def get_completed_task():
    user_id = get_jwt_identity()
    return PointsService.get_task_status(user_id)

@bp.route('/discord', methods=['POST'])
@jwt_required()
def add_discord():
    user_id = get_jwt_identity()
    data = request.get_json()

    # 注意 scope 可以根据需要进行调整，比如需要检查公会列表则加 guilds，
    # 需要识别用户基本信息则加 identify 等
    scope = "identify guilds"
    discord_authorize_url = (
        "https://discord.com/api/oauth2/authorize"
        f"?client_id={DISCORD_CLIENT_ID}"
        f"&redirect_uri={DISCORD_REDIRECT_URI}"
        f"&response_type=code"
        f"&scope={scope}"
    )
    return redirect(discord_authorize_url)

@bp.route("/discord_callback", methods=['POST'])
def discord_callback():
    """
    用户授权后，Discord 会携带 code 参数重定向到此回调 URL。
    后端使用 code 换取 access_token，然后根据 token 查询用户信息和加群状态。
    """
    code = request.args.get("code")
    if not code:
        return Response.error("没有从 Discord 返回有效的 code 参数。", code=401)

    # ---- 3.1 用 code 换取 access_token ----
    token_url = "https://discord.com/api/oauth2/token"
    data = {
        "client_id": DISCORD_CLIENT_ID,
        "client_secret": DISCORD_CLIENT_SECRET,
        "grant_type": "authorization_code",
        "code": code,
        "redirect_uri": DISCORD_REDIRECT_URI,
    }
    headers = {"Content-Type": "application/x-www-form-urlencoded"}

    token_resp = requests.post(token_url, data=data, headers=headers)
    if token_resp.status_code != 200:
        return Response.error(f"获取 Discord access_token 失败: {token_resp.text}", code=401)

    token_json = token_resp.json()
    access_token = token_json.get("access_token")
    if not access_token:
        return Response.error("未能获取到 access_token", code=401)

    # ---- 3.2 获取用户信息（可用于记录用户ID、用户名等）----
    user_info_url = "https://discord.com/api/users/@me"
    user_headers = {"Authorization": f"Bearer {access_token}"}
    user_resp = requests.get(user_info_url, headers=user_headers)

    if user_resp.status_code != 200:
        return Response.error(f"获取用户信息失败: {user_resp.text}", code=401)

    user_data = user_resp.json()
    user_id = user_data["id"]  # Discord 用户的唯一标识

    # ---- 3.3 检查用户是否已加入目标服务器 ----
    # 这里使用 Bot Token 查询服务器成员信息，判断用户是否已在目标服务器中。
    # 注意：此操作需要你的 Bot 在该服务器中，并拥有相应的权限（查看成员列表）。
    guild_member_url = f"https://discord.com/api/guilds/{DISCORD_GUILD_ID}/members/{user_id}"
    guild_member_headers = {
        "Authorization": f"Bot {DISCORD_BOT_TOKEN}"
    }
    guild_member_resp = requests.get(guild_member_url, headers=guild_member_headers)

    if guild_member_resp.status_code == 200:
        # 如果能查到成员信息，说明用户已加入服务器
        # 在这里执行“给用户发放积分”等逻辑
        return PointsService.add_task_points(user_id, 'Discord')
    else:
        # 用户未在服务器中，提示未完成加入
        return Response.error("检测到你尚未加入服务器，请先加入我们的 Discord！", code=402)


@bp.route('/telegram', methods=['POST'])
@jwt_required()
def add_telegram():
    user_id = get_jwt_identity()

    # 生成邀请链接时，将平台用户 ID 嵌入名称
    invite_name = f"invite_user_{user_id}"
    params = {
        "chat_id": TARGET_CHAT_ID,
        "name": invite_name,  # 自定义名称
        "expire_date": int(time.time()) + 86400,  # 链接有效期 1 天（86400 秒）
        "creates_join_request": True  # 需要审核，这样 Telegram 会发送入群申请更新，并带上 invite_link 信息
    }

    # 调用 Telegram API 生成邀请链接
    response = requests.post(CREATE_INVITE_URL, data=params)
    result = response.json()
    if not result.get("ok"):
        return Response.error("生成邀请链接失败", data=result)

    invite_link_info = result.get("result")
    invite_link = invite_link_info.get("invite_link")

    # 重定向用户到专属邀请链接
    return redirect(invite_link, code=302)

@bp.route('/telegram_webhook', methods=['POST'])
def webhook():
    """
    Telegram Bot Webhook 接口：
    当用户使用邀请链接申请入群时，Telegram 会发送一条 chat_join_request 更新。
    更新数据中包含 invite_link 字段，name 就是我们生成邀请链接时设置的标识（如 "invite_user_1001"）。
    根据此名称，后台可找到对应的用户记录，标记用户已申请加入并发放积分。
    """
    data = request.json

    chat_join_request = data.get("chat_join_request")
    if chat_join_request:
        invite_link_info = chat_join_request.get("invite_link", {})
        invite_name = invite_link_info.get("name")  # 例如 "invite_user_1001"
        # 从名称中提取平台用户 ID
        user_id = invite_name.split("invite_user_")[1]
        return PointsService.add_task_points(user_id, 'Telegram')

    # 其它类型更新暂不处理
    return Response.success(message="更新已接收，但未处理", code=201)


@bp.route('/invite-code', methods=['GET'])
@jwt_required()
def get_invite_code():
    user_id = get_jwt_identity()
    return PointsService.generate_invite_code(user_id)

@bp.route('/records', methods=['GET'])
@jwt_required()
def get_records():
    user_id = get_jwt_identity()
    
    # Get the record_type from the query parameter
    record_type = request.args.get('record_type')
    
    # Start with base query filtering by user_id
    query = PointRecord.query.filter_by(user_id=user_id)
    
    # If record_type is provided and valid, add additional filter
    if record_type in ['task', 'invite', 'project']:
        query = query.filter_by(record_type=record_type)
    
    records = query.all()
    
    return Response.success(data=[r.to_dict() for r in records])

@bp.route('/records/rank', methods=['GET'])
@jwt_required()
def get_points_ranking():
    """
    Get the ranking list of users by total points and the current user's position.
    Returns:
        A dictionary with two keys:
        - ranking_list: Up to 30 users sorted by points from higher to lower
        - myself: The current user's email, points, and rank
    """
    current_user_id = get_jwt_identity()
    # Format the ranking list
    ranking_list = PointsService.get_rank_list()
    myself = PointsService.get_user_rank(current_user_id)

    return Response.success(data={
        "ranking_list": ranking_list,
        "myself": myself
    })

@bp.route('/records', methods=['POST'])
@jwt_required()
def get_records_post():
    user_id = get_jwt_identity()

    data = request.get_json()
    page = data.get("page", 1)
    per_page = data.get("per_page", 10)

    # 按 created_at 倒序排序，并使用 paginate 进行分页
    # 注：paginate 是 Flask-SQLAlchemy 的方法，需确保 db 为 SQLAlchemy 实例
    # 如果使用纯 SQLAlchemy，需要自行实现分页逻辑
    pagination = PointRecord.query \
        .filter_by(user_id=user_id) \
        .order_by(PointRecord.created_at.desc()) \
        .paginate(page=page, per_page=per_page, error_out=False)

    records = pagination.items

    data = [r.to_dict() for r in records]

    total_points = PointRecord.query.with_entities(func.sum(PointRecord.points)) \
                       .filter(PointRecord.user_id == user_id) \
                       .scalar() or 0

    return Response.success(data={
        "total_points": total_points,
        "items": data,
        "page": pagination.page,
        "per_page": pagination.per_page,
        "total": pagination.total
    })

