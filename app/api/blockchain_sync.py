"""
区块链同步管理API
用于管理区块链监听服务的状态和配置
"""

from flask import Blueprint, request

from app.models import db
from app.models.blockchain_sync import BlockchainSyncStatus, TransactionSyncLog
from app.models.wallet import AddressMapping
from app.models.blockchain import Blockchain
from app.services.blockchain_listener_service import blockchain_listener
from app.middlewares.auth import admin_required
from app.utils.decorators import handle_api_errors
from app.utils.response import Response


blockchain_sync_bp = Blueprint('blockchain_sync', __name__, url_prefix='/api/blockchain-sync')


@blockchain_sync_bp.route('/status', methods=['GET'])
@admin_required
@handle_api_errors
def get_sync_status():
    """获取所有链的同步状态"""
    try:
        # 获取所有区块链
        blockchains = Blockchain.query.all()
        
        status_list = []
        for blockchain in blockchains:
            sync_status = BlockchainSyncStatus.query.filter_by(
                chain_id=blockchain.id
            ).first()
            
            if sync_status:
                status_data = sync_status.to_dict()
                status_data['chain_name'] = blockchain.chain_name
                status_data['chain_code'] = blockchain.chain_code
            else:
                status_data = {
                    'chain_id': blockchain.id,
                    'chain_name': blockchain.chain_name,
                    'chain_code': blockchain.chain_code,
                    'last_processed_block': 0,
                    'status': 'NOT_STARTED',
                    'error_count': 0
                }
            
            status_list.append(status_data)
        
        return Response.success({
            'sync_status': status_list,
            'listener_running': blockchain_listener.is_running
        })
        
    except Exception as e:
        return Response.error(f"获取同步状态失败: {str(e)}", 500)


@blockchain_sync_bp.route('/status/<int:chain_id>', methods=['GET'])
@admin_required
@handle_api_errors
def get_chain_sync_status(chain_id):
    """获取特定链的详细同步状态"""
    try:
        blockchain = Blockchain.query.get(chain_id)
        if not blockchain:
            return Response.error("区块链不存在", 404)
        
        sync_status = BlockchainSyncStatus.query.filter_by(chain_id=chain_id).first()
        
        # 获取最近的交易日志
        recent_logs = TransactionSyncLog.query.filter_by(
            chain_id=chain_id
        ).order_by(TransactionSyncLog.sync_time.desc()).limit(10).all()
        
        # 获取监听的地址
        monitored_addresses = AddressMapping.query.filter_by(
            chain_type=blockchain.chain_code.lower(),
            is_active=True
        ).count()
        
        return Response.success({
            'chain_info': blockchain.to_dict(),
            'sync_status': sync_status.to_dict() if sync_status else None,
            'recent_transactions': [log.to_dict() for log in recent_logs],
            'monitored_addresses_count': monitored_addresses
        })
        
    except Exception as e:
        return Response.error(f"获取链同步状态失败: {str(e)}", 500)


@blockchain_sync_bp.route('/transactions', methods=['GET'])
@admin_required
@handle_api_errors
def get_sync_transactions():
    """获取同步的交易记录"""
    try:
        chain_id = request.args.get('chain_id', type=int)
        status = request.args.get('status')  # PENDING, PROCESSED, IGNORED, FAILED
        limit = request.args.get('limit', 50, type=int)
        
        if limit > 200:
            limit = 200
        
        query = TransactionSyncLog.query
        
        if chain_id:
            query = query.filter_by(chain_id=chain_id)
        
        if status:
            query = query.filter_by(sync_status=status)
        
        transactions = query.order_by(
            TransactionSyncLog.sync_time.desc()
        ).limit(limit).all()
        
        return Response.success({
            'transactions': [tx.to_dict() for tx in transactions],
            'count': len(transactions)
        })
        
    except Exception as e:
        return Response.error(f"获取交易记录失败: {str(e)}", 500)


@blockchain_sync_bp.route('/addresses', methods=['GET'])
@admin_required
@handle_api_errors
def get_monitored_addresses():
    """获取监听的地址列表"""
    try:
        chain_type = request.args.get('chain_type')
        user_id = request.args.get('user_id', type=int)
        limit = request.args.get('limit', 100, type=int)
        
        if limit > 500:
            limit = 500
        
        query = AddressMapping.query.filter_by(is_active=True)
        
        if chain_type:
            query = query.filter_by(chain_type=chain_type.lower())
        
        if user_id:
            query = query.filter_by(user_id=user_id)
        
        addresses = query.order_by(
            AddressMapping.created_at.desc()
        ).limit(limit).all()
        
        return Response.success({
            'addresses': [addr.to_dict() for addr in addresses],
            'count': len(addresses)
        })
        
    except Exception as e:
        return Response.error(f"获取监听地址失败: {str(e)}", 500)


@blockchain_sync_bp.route('/addresses', methods=['POST'])
@admin_required
@handle_api_errors
def add_monitored_address():
    """添加监听地址"""
    try:
        data = request.get_json()
        
        required_fields = ['user_id', 'address', 'chain_type']
        for field in required_fields:
            if field not in data:
                return Response.error(f"缺少必要字段: {field}", 400)
        
        # 检查地址是否已存在
        existing = AddressMapping.query.filter_by(
            address=data['address'].lower(),
            chain_type=data['chain_type'].lower()
        ).first()
        
        if existing:
            return Response.error("地址已存在", 400)
        
        # 创建地址映射
        from app.models.wallet import AddressMappingTypeEnum
        from app.models import db
        
        address_mapping = AddressMapping(
            user_id=data['user_id'],
            address=data['address'].lower(),
            chain_type=data['chain_type'].lower(),
            address_type=AddressMappingTypeEnum(data.get('address_type', 'BOTH')),
            is_active=True
        )
        
        db.session.add(address_mapping)
        db.session.commit()
        
        return Response.success({
            'message': '地址添加成功',
            'address_mapping': address_mapping.to_dict()
        })
        
    except Exception as e:
        from app.models import db
        db.session.rollback()
        return Response.error(f"添加监听地址失败: {str(e)}", 500)


@blockchain_sync_bp.route('/addresses/<int:address_id>', methods=['DELETE'])
@admin_required
@handle_api_errors
def remove_monitored_address(address_id):
    """移除监听地址"""
    try:
        address_mapping = AddressMapping.query.get(address_id)
        if not address_mapping:
            return Response.error("地址不存在", 404)
        
        from app.models import db
        
        # 软删除：设置为不活跃
        address_mapping.is_active = False
        db.session.commit()
        
        return Response.success({
            'message': '地址已移除监听'
        })
        
    except Exception as e:
        from app.models import db
        db.session.rollback()
        return Response.error(f"移除监听地址失败: {str(e)}", 500)


@blockchain_sync_bp.route('/reset/<int:chain_id>', methods=['POST'])
@admin_required
@handle_api_errors
def reset_chain_sync(chain_id):
    """重置链的同步状态"""
    try:
        data = request.get_json() or {}
        start_block = data.get('start_block', 0)
        
        blockchain = Blockchain.query.get(chain_id)
        if not blockchain:
            return Response.error("区块链不存在", 404)
        
        # 更新同步状态
        BlockchainSyncStatus.update_processed_block(chain_id, start_block)
        
        return Response.success({
            'message': f'{blockchain.chain_name} 同步状态已重置',
            'start_block': start_block
        })
        
    except Exception as e:
        return Response.error(f"重置同步状态失败: {str(e)}", 500)


@blockchain_sync_bp.route('/statistics', methods=['GET'])
@admin_required
@handle_api_errors
def get_sync_statistics():
    """获取同步统计信息"""
    try:
        from sqlalchemy import func
        
        # 统计各状态的交易数量
        status_stats = db.session.query(
            TransactionSyncLog.sync_status,
            func.count(TransactionSyncLog.id).label('count')
        ).group_by(TransactionSyncLog.sync_status).all()
        
        # 统计各链的交易数量
        chain_stats = db.session.query(
            TransactionSyncLog.chain_id,
            func.count(TransactionSyncLog.id).label('count')
        ).group_by(TransactionSyncLog.chain_id).all()
        
        # 统计监听地址数量
        address_count = AddressMapping.query.filter_by(is_active=True).count()
        
        # 统计活跃链数量
        active_chains = BlockchainSyncStatus.query.filter_by(
            status='RUNNING'
        ).count()
        
        return Response.success({
            'transaction_status_stats': [
                {'status': stat[0], 'count': stat[1]} for stat in status_stats
            ],
            'chain_transaction_stats': [
                {'chain_id': stat[0], 'count': stat[1]} for stat in chain_stats
            ],
            'monitored_addresses_count': address_count,
            'active_chains_count': active_chains,
            'listener_running': blockchain_listener.is_running
        })
        
    except Exception as e:
        return Response.error(f"获取统计信息失败: {str(e)}", 500)
