from flask import Blueprint

from .asset_management_tools import asset_management_tools_bp  # 新增资产管理工具接口
from .auth import auth_bp
from .batch_orders import batch_orders_bp  # 新增批量订单接口
from .blockchain import blockchain_bp  # 新增导入
from .dashboard import dashboard_bp
from .device import device_bp
from .enhanced_orders import enhanced_orders_bp  # 新增增强订单接口
from .instant_trading import instant_trading_bp  # 新增即时交易接口
from .kline import kline_bp  # 新增K线接口
from .notify import notify_bp
from .order import order_bp
from .points import bp as points_bp
from .project import bp as project_bp
from .remote_config import bp as remote_bp
from .service_config import bp as service_config_bp
from .system_metrics import metrics_bp
from .tools import tools_bp
from .user_assets import user_assets_bp  # 新增用户资产管理接口
from .user_notify import user_notify_bp
from .wallets import wallets_bp

api_bp = Blueprint("api", __name__)

# 注册所有API蓝图，统一在这里管理URL前缀
api_bp.register_blueprint(auth_bp, url_prefix="/auth")
api_bp.register_blueprint(device_bp, url_prefix="/devices")
api_bp.register_blueprint(dashboard_bp, url_prefix="/dashboard")
api_bp.register_blueprint(project_bp, url_prefix="/projects")
api_bp.register_blueprint(service_config_bp, url_prefix="/service-configs")
api_bp.register_blueprint(remote_bp, url_prefix="/remote")
api_bp.register_blueprint(points_bp, url_prefix="/points")
api_bp.register_blueprint(wallets_bp, url_prefix="/wallets")
api_bp.register_blueprint(tools_bp, url_prefix="/tools")
api_bp.register_blueprint(notify_bp, url_prefix="/notify")
api_bp.register_blueprint(user_notify_bp, url_prefix="/user-notify")
api_bp.register_blueprint(metrics_bp, url_prefix="/system/metrics")
api_bp.register_blueprint(blockchain_bp, url_prefix="/blockchain")
api_bp.register_blueprint(user_assets_bp, url_prefix="/user-assets")
api_bp.register_blueprint(asset_management_tools_bp, url_prefix="/asset-tools")
api_bp.register_blueprint(enhanced_orders_bp, url_prefix="/enhanced-orders")
api_bp.register_blueprint(instant_trading_bp, url_prefix="/instant-trading")
api_bp.register_blueprint(kline_bp, url_prefix="/kline")
api_bp.register_blueprint(batch_orders_bp, url_prefix="/orders")
api_bp.register_blueprint(order_bp, url_prefix="/orders")