"""
即时交易接口
提供直接买卖功能，不通过订单匹配系统
"""
from decimal import Decimal

from flask import Blueprint, request
from flask_jwt_extended import jwt_required, get_jwt_identity

from app.models import db
from app.models.order import OrderTypeEnum, Order, OrderStatusEnum
from app.services.order_service import OrderService
from app.utils.auth import get_current_user
from app.utils.decorators import handle_api_errors
from app.utils.response import Response

instant_trading_bp = Blueprint('instant_trading', __name__)


@instant_trading_bp.route('', methods=['POST'])
@jwt_required()
@handle_api_errors
def instant_trade():
    """
    即时买入/卖出
    
    指定订单买入/卖出指定数量的资产，不通过订单匹配
    
    Args:
        Request Body:
            target_order_id (int): 交易订单ID
            amount (str): 交易数量（基础资产数量）
    
    Returns:
        Response: 交易结果
    
    Example:
        POST /api/instant-trading
        {
            "target_order_id": 1,
            "amount": "0.10000000"
        }
    """

    data = request.get_json()
    if not data:
        return Response.success(data=None, message="请求数据不能为空", code=400)

    current_user = get_current_user()
    user_id = data.get('user_id', get_jwt_identity()) if current_user.is_admin else get_jwt_identity()

    # 参数验证
    required_fields = ['target_order_id', 'amount']
    for field in required_fields:
        if field not in data:
            return Response.error(data=None, message=f"缺少必要参数: {field}", code=400)

    target_order_id = data['target_order_id']
    target_order = db.session.get(Order, target_order_id)
    if not target_order:
        return Response.success(data=None, message="目标订单不存在", code=400)
    if (target_order.status not in [OrderStatusEnum.PARTIAL.value, OrderStatusEnum.PENDING.value]
            or (target_order.original_amount - target_order.executed_amount < Decimal(data['amount']))):
        return Response.success(data=None, message="目标订单可交易数量不足", code=400)

    order_type = OrderTypeEnum.LIMIT
    side = 'SELL' if target_order.side == 'BUY' else 'BUY'

    create_order_params = {
        "user_id": user_id,
        "order_type": order_type,
        "side": side,
        "pair_id": target_order.pair_id,
        "price": target_order.price,
        "amount": data['amount'],
        "target_order_id": target_order_id,
        "order_source": "INSTANT_TRADE"
    }

    order = OrderService.create_order(create_order_params)

    return Response.success({
        'order_id': order.id,
        'status': order.status.value,
        'order_type': order.order_type.value,
        'side': order.side.value,
        'pair_id': order.pair_id,
        'price': str(order.price) if order.price is not None else None,
        'amount': str(order.original_amount)
    })
