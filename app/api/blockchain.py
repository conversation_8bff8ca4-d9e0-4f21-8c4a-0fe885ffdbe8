from flask import Blueprint, request
from flask_jwt_extended import jwt_required

from app.middlewares.auth import admin_required
from app.models.blockchain import TokenType, AssetTypeEnum
from app.services.asset_type_service import AssetTypeService
from app.services.blockchain_service import BlockchainService
from app.services.trading_pair_service import TradingPairService
from app.utils.decorators import handle_api_errors
from app.utils.response import Response

blockchain_bp = Blueprint("blockchain", __name__)


@blockchain_bp.route("", methods=["POST"])
@admin_required
@handle_api_errors
def create_blockchain():
    """创建新的区块链

    创建一个新的区块链记录，用于支持新的区块链网络。

    Args:
        JSON Body:
            chain_name (str): 区块链名称，如 "Ethereum"
            chain_code (str): 区块链代码，如 "ETH"，必须唯一
            coin_type (int): BIP44 标准硬币类型，如 60
            extend_field (dict, optional): 扩展字段

    Returns:
        Response: 包含创建的区块链信息
            - id: 区块链ID
            - chain_name: 区块链名称
            - chain_code: 区块链代码
            - coin_type: BIP44 硬币类型

    Raises:
        400: 缺少必要字段
        409: 区块链代码已存在

    Example:
        POST /api/blockchain
        {
            "chain_name": "Ethereum",
            "chain_code": "ETH",
            "coin_type": 60
        }
    """
    data = request.get_json()
    if not data or "chain_name" not in data or "chain_code" not in data or "coin_type" not in data:
        return Response.error("Missing required fields", 400)

    # 构建 extend_field，将 rpc_url 和 explorer 合并进去
    extend_field = data.get("extend_field", {}) or {}
    if data.get("rpc_url"):
        extend_field["rpc_url"] = data["rpc_url"]
    if data.get("explorer"):
        extend_field["explorer"] = data["explorer"]

    blockchain = BlockchainService.create(
        chain_name=data["chain_name"],
        chain_code=data["chain_code"],
        coin_type=data["coin_type"],
        extend_field=extend_field if extend_field else None
    )

    return Response.success(blockchain.to_dict())


@blockchain_bp.route("/<chain_code>", methods=["PUT"])
@admin_required
@handle_api_errors
def update_blockchain(chain_code):
    """更新区块链信息

    更新指定区块链的信息，某些字段不可修改。

    Args:
        chain_code (str): 区块链代码，如 "ETH"
        JSON Body:
            chain_name (str, optional): 区块链名称
            coin_type (int, optional): BIP44 硬币类型
            extend_field (dict, optional): 扩展字段

    Returns:
        Response: 更新后的区块链信息

    Raises:
        400: 未提供更新数据或尝试修改不可变字段
        404: 区块链不存在

    Note:
        不可修改字段：chain_code

    Example:
        PUT /api/blockchain/ETH
        {
            "chain_name": "Ethereum Mainnet",
            "extend_field": {
                "rpc_url": "https://mainnet.infura.io/v3/your-key",
                "explorer": "https://etherscan.io",
                "network_id": 1
            }
        }
    """
    data = request.get_json()
    if not data:
        return Response.error("未提供更新数据", 400)


    # 处理 rpc_url 和 explorer 字段
    update_data = {}
    chain_name = data.get('chain_name', '')
    if chain_name:
        update_data.update({"chain_name": chain_name})
    coin_type = data.get('coin_type', '')
    if coin_type:
        update_data.update({"coin_type": coin_type})

    if "rpc_url" in data or "explorer" in data:
        # 获取当前区块链信息
        current_blockchain = BlockchainService.get_by_code(chain_code)
        if not current_blockchain:
            return Response.error("Blockchain not found", 404)

        # 获取当前的 extend_field
        current_extend_field = current_blockchain.extend_field or {}

        # 更新 rpc_url 和 explorer
        if "rpc_url" in data:
            current_extend_field["rpc_url"] = data["rpc_url"]
        if "explorer" in data:
            current_extend_field["explorer"] = data["explorer"]

        # 如果原本就有 extend_field 数据，则合并
        if "extend_field" in data:
            extend_field = data["extend_field"] or {}
            # 先用当前的 extend_field 作为基础，再用新的数据覆盖
            current_extend_field.update(extend_field)
            update_data["extend_field"] = current_extend_field
        else:
            update_data["extend_field"] = current_extend_field

    blockchain = BlockchainService.update(chain_code, update_data)
    if not blockchain:
        return Response.error("Blockchain not found", 404)

    return Response.success(blockchain.to_dict())


@blockchain_bp.route("/<chain_code>", methods=["DELETE"])
@admin_required
@handle_api_errors
def delete_blockchain(chain_code):
    """软删除区块链

    软删除指定的区块链记录，不会物理删除数据。

    Args:
        chain_code (str): 区块链代码，如 "ETH"

    Returns:
        Response: 删除操作结果

    Raises:
        404: 区块链不存在

    Example:
        DELETE /api/blockchain/ETH
    """
    success = BlockchainService.delete(chain_code)
    if not success:
        return Response.error("Blockchain not found", 404)
    return Response.success()


@blockchain_bp.route("/<chain_code>", methods=["GET"])
@jwt_required()
@handle_api_errors
def get_blockchain(chain_code):
    """获取区块链信息

    根据区块链代码获取特定区块链的详细信息。

    Args:
        chain_code (str): 区块链代码，如 "ETH"

    Returns:
        Response: 区块链详细信息
            - id: 区块链ID
            - chain_name: 区块链名称
            - chain_code: 区块链代码
            - coin_type: BIP44 硬币类型

    Raises:
        404: 区块链不存在

    Example:
        GET /api/blockchain/ETH
    """
    blockchain = BlockchainService.get_by_code(chain_code)
    if not blockchain:
        return Response.error("Blockchain not found", 404)
    return Response.success(blockchain.to_dict())


@blockchain_bp.route("", methods=["GET"])
@jwt_required()
@handle_api_errors
def list_blockchains():
    """获取所有支持的区块链列表

    获取系统中所有未删除的区块链记录列表。

    Returns:
        Response: 区块链列表
            - data: 区块链数组，每个元素包含：
                - id: 区块链ID
                - chain_name: 区块链名称
                - chain_code: 区块链代码
                - coin_type: BIP44 硬币类型

    Example:
        GET /api/blockchain
    """
    blockchains = BlockchainService.get_all()
    return Response.success(data=[b.to_dict() for b in blockchains])


@blockchain_bp.route("/tokens", methods=["POST"])
@admin_required
@handle_api_errors
def create_token():
    """创建代币

    创建一个新的代币记录，支持原生代币和合约代币。

    Args:
        JSON Body:
            token_code (str): 代币唯一标识符，如 "eth_native"
            token_symbol (str): 代币符号，如 "ETH"
            token_name (str): 代币名称，如 "Ethereum"
            chain_id (int): 所属区块链ID
            decimals (int): 小数位数，如 18
            token_type (str): 代币类型，可选值：
                - "NATIVE": 原生代币
                - "erc20": ERC20代币
                - "spl": Solana SPL代币
            contract_address (str, optional): 合约地址，非原生代币必填

    Returns:
        Response: 包含创建的代币信息
            - id: 代币ID
            - token_code: 代币标识符
            - token_symbol: 代币符号

    Raises:
        400: 缺少必要字段或非原生代币未指定合约地址
        409: 代币代码已存在

    Example:
        POST /api/blockchain/tokens
        {
            "token_code": "usdt_erc20",
            "token_symbol": "USDT",
            "token_name": "Tether",
            "chain_id": 1,
            "decimals": 6,
            "token_type": "erc20",
            "contract_address": "******************************************"
        }
    """
    data = request.get_json()
    if not data:
        return Response.error("Missing request data", 400)

    # 手动实现数据验证
    required_fields = ["token_code", "token_symbol", "token_name",
                       "chain_id", "decimals", "token_type"]
    if not all(field in data for field in required_fields):
        return Response.error("缺少必要字段", 400)

    if data["token_type"] != TokenType.NATIVE.value and not data.get("contract_address"):
        return Response.error("非原生代币必须指定合约地址", 400)

    # 创建代币
    token = BlockchainService.create_token(
        token_code=data["token_code"],
        token_symbol=data["token_symbol"],
        token_name=data["token_name"],
        chain_id=data["chain_id"],
        decimals=data["decimals"],
        token_type=TokenType(data["token_type"]),
        contract_address=data.get("contract_address")
    )

    return Response.success({
        "id": token.id,
        "token_code": token.token_code,
        "token_symbol": token.token_symbol
    })


@blockchain_bp.route("/tokens/<int:token_id>", methods=["GET"])
@jwt_required()
@handle_api_errors
def get_token(token_id):
    """获取代币详情

    根据代币ID获取代币的详细信息。

    Args:
        token_id (int): 代币ID

    Returns:
        Response: 代币详细信息
            - id: 代币ID
            - token_code: 代币标识符
            - token_symbol: 代币符号
            - token_name: 代币名称
            - chain_id: 所属区块链ID
            - decimals: 小数位数
            - token_type: 代币类型
            - contract_address: 合约地址（如果有）

    Raises:
        404: 代币不存在

    Example:
        GET /api/blockchain/tokens/1
    """
    token = BlockchainService.get_token(token_id)
    return Response.success(token.to_dict())


@blockchain_bp.route("/tokens", methods=["GET"])
@jwt_required()
@handle_api_errors
def list_tokens():
    """分页查询代币列表

    获取系统中所有代币的分页列表。

    Args:
        Query Parameters:
            page (int, optional): 页码，默认为1
            per_page (int, optional): 每页数量，默认为10

    Returns:
        Response: 分页的代币列表
            - total: 总数量
            - items: 代币数组
            - page: 当前页码
            - per_page: 每页数量

    Example:
        GET /api/blockchain/tokens?page=1&per_page=20
    """
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    result = BlockchainService.list_tokens(page=page, per_page=per_page)
    return Response.success(result)


@blockchain_bp.route("/tokens/<int:token_id>", methods=["PUT"])
@admin_required
@handle_api_errors
def update_token(token_id):
    """更新代币信息

    更新指定代币的信息，某些字段不可修改。

    Args:
        token_id (int): 代币ID
        JSON Body:
            token_symbol (str, optional): 代币符号
            token_name (str, optional): 代币名称
            decimals (int, optional): 小数位数
            contract_address (str, optional): 合约地址（仅非原生代币）

    Returns:
        Response: 更新后的代币信息

    Raises:
        400: 尝试修改不可变字段或未提供更新数据
        404: 代币不存在

    Note:
        不可修改字段：token_code, chain_id, token_type

    Example:
        PUT /api/blockchain/tokens/1
        {
            "token_name": "Updated Token Name",
            "decimals": 18
        }
    """
    data = request.get_json()
    if not data:
        return Response.error("未提供更新数据", 400)

    token = BlockchainService.update_token(token_id, data)
    return Response.success(token.to_dict())


@blockchain_bp.route("/tokens/<int:token_id>", methods=["DELETE"])
@admin_required
@handle_api_errors
def delete_token(token_id):
    """删除代币

    物理删除指定的代币记录。

    Args:
        token_id (int): 代币ID

    Returns:
        Response: 删除操作结果

    Raises:
        404: 代币不存在

    Example:
        DELETE /api/blockchain/tokens/1
    """
    BlockchainService.delete_token(token_id)
    return Response.success()


@blockchain_bp.route('/asset-types', methods=['POST'])
@admin_required
@handle_api_errors
def create_asset_type():
    """创建资产类型

    创建一个新的资产类型，支持积分和加密货币两种类型。

    Args:
        JSON Body:
            name (str): 资产名称，如 "用户积分" 或 "以太坊"
            type (str): 资产类型，可选值：
                - "POINTS": 积分类型
                - "TOKEN": 加密货币类型
            project_id (int, optional): 项目ID，积分类型必填
            token_id (int, optional): 代币ID，加密货币类型必填
            chain_type (str, optional): 链类型，加密货币类型必填，如 "ETH"

    Returns:
        Response: 包含创建的资产类型信息
            - id: 资产类型ID
            - name: 资产名称
            - type: 资产类型
            - project_id: 项目ID（如果有）
            - token_id: 代币ID（如果有）
            - chain_type: 链类型（如果有）
            - decimals: 小数位数
            - created_at: 创建时间
            - updated_at: 更新时间

    Raises:
        400: 缺少必要参数或参数验证失败

    Example:
        POST /api/blockchain/asset-types
        {
            "name": "以太坊",
            "type": "TOKEN",
            "token_id": 1,
            "chain_type": "ETH"
        }
    """
    data = request.get_json()

    required_fields = ['name', 'type']
    if not all(field in data for field in required_fields):
        return Response.error('缺少必要参数', 400)

    asset_type = AssetTypeService.create_asset_type(
        name=data['name'],
        asset_type=data['type'],
        decimals=data.get('decimals', 0),
        project_id=data.get('project_id'),
        token_id=data.get('token_id'),
        chain_type=data.get('chain_type')
    )
    return Response.success(asset_type.to_dict())


@blockchain_bp.route('/asset-types', methods=['GET'])
@jwt_required()
@handle_api_errors
def list_asset_types():
    """分页查询资产类型列表

    获取系统中所有资产类型的分页列表。

    Args:
        Query Parameters:
            page (int, optional): 页码，默认为1
            per_page (int, optional): 每页数量，默认为10
            type (str, optional): 资产类型过滤，可选值：POINTS, TOKEN

    Returns:
        Response: 分页的资产类型列表
            - total: 总数量
            - items: 资产类型数组
            - page: 当前页码
            - per_page: 每页数量

    Example:
        GET /api/blockchain/asset-types?page=1&per_page=20&type=TOKEN
    """
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    asset_type_filter = request.args.get('type')

    result = AssetTypeService.list_asset_types(
        page=page,
        per_page=per_page,
        asset_type_filter=asset_type_filter
    )
    return Response.success(result)


@blockchain_bp.route('/asset-types/<int:type_id>', methods=['GET'])
@jwt_required()
@handle_api_errors
def get_asset_type(type_id):
    """获取资产类型

    根据资产类型ID获取资产类型的详细信息。

    Args:
        type_id (int): 资产类型ID

    Returns:
        Response: 资产类型详细信息
            - id: 资产类型ID
            - name: 资产名称
            - type: 资产类型
            - project_id: 项目ID（如果有）
            - token_id: 代币ID（如果有）
            - chain_type: 链类型（如果有）
            - decimals: 小数位数
            - created_at: 创建时间
            - updated_at: 更新时间

    Raises:
        404: 资产类型不存在

    Example:
        GET /api/blockchain/asset-types/1
    """
    asset_type = AssetTypeService.get_asset_type(type_id)
    if not asset_type:
        return Response.error('资产类型不存在', 404)
    return Response.success(asset_type.to_dict())


@blockchain_bp.route('/asset-types/<int:type_id>', methods=['DELETE'])
@admin_required
@handle_api_errors
def delete_asset_type(type_id):
    """删除资产类型

    删除指定的资产类型，如果存在关联的用户资产则无法删除。

    Args:
        type_id (int): 资产类型ID

    Returns:
        Response: 删除操作结果

    Raises:
        400: 存在关联资产，无法删除
        404: 资产类型不存在

    Example:
        DELETE /api/blockchain/asset-types/1
    """
    success = AssetTypeService.delete_asset_type(type_id)
    if not success:
        return Response.error('资产类型不存在', 404)
    return Response.success()


@blockchain_bp.route("/trading-pairs", methods=["POST"])
@admin_required
@handle_api_errors
def create_trading_pair():
    """创建交易对

    创建一个新的交易对，用于定义两种资产之间的交易关系。

    Args:
        JSON Body:
            base_asset_id (int): 基础资产ID
            quote_asset_id (int): 计价资产ID
            pair_name (str): 交易对名称，如 "BTC/USDT"
            min_price (decimal): 最小价格
            max_price (decimal): 最大价格
            price_precision (int): 价格精度（小数位数）
            amount_precision (int): 数量精度（小数位数）

    Returns:
        Response: 包含创建的交易对信息
            - id: 交易对ID
            - base_asset_id: 基础资产ID
            - quote_asset_id: 计价资产ID
            - pair_name: 交易对名称
            - min_price: 最小价格
            - max_price: 最大价格
            - price_precision: 价格精度
            - amount_precision: 数量精度
            - status: 状态
            - created_at: 创建时间
            - updated_at: 更新时间

    Raises:
        400: 缺少必要字段、资产不存在或交易对已存在
        404: 指定的资产不存在

    Example:
        POST /api/blockchain/trading-pairs
        {
            "base_asset_id": 1,
            "quote_asset_id": 2,
            "pair_name": "BTC/USDT",
            "min_price": "0.01",
            "max_price": "100000.00",
            "price_precision": 2,
            "amount_precision": 6
        }
    """
    data = request.get_json()
    # 必填字段校验
    required_fields = ['base_asset_id', 'quote_asset_id', 'pair_name',
                       'min_price', 'max_price', 'price_precision', 'amount_precision']
    for field in required_fields:
        if field not in data:
            return Response.error(f"Missing required field: {field}", code=400)

    result = TradingPairService.create_trading_pair(data)
    return Response.success(result.to_dict())  # 添加to_dict调用


@blockchain_bp.route("/trading-pairs/<int:pair_id>", methods=["PUT"])
@admin_required
@handle_api_errors
def update_trading_pair(pair_id):
    """更新交易对

    更新指定交易对的信息，某些字段不可修改。

    Args:
        pair_id (int): 交易对ID
        JSON Body:
            pair_name (str, optional): 交易对名称
            min_price (decimal, optional): 最小价格
            max_price (decimal, optional): 最大价格
            price_precision (int, optional): 价格精度
            amount_precision (int, optional): 数量精度
            status (str, optional): 状态

    Returns:
        Response: 更新后的交易对信息

    Raises:
        400: 未提供更新数据或尝试修改不可变字段
        404: 交易对不存在

    Note:
        不可修改字段：base_asset_id, quote_asset_id

    Example:
        PUT /api/blockchain/trading-pairs/1
        {
            "min_price": "0.02",
            "max_price": "200000.00"
        }
    """
    data = request.get_json()
    # 至少需要更新一个字段
    if not data:
        return Response.error("No update data provided", code=400)

    # 检查禁止修改的字段
    immutable_fields = ['base_asset_id', 'quote_asset_id']
    for field in immutable_fields:
        if field in data:
            return Response.error(f"Cannot modify {field}", code=400)

    result = TradingPairService.update_trading_pair(pair_id, data)
    return Response.success(result.to_dict())


@blockchain_bp.route("/trading-pairs/<int:pair_id>", methods=["DELETE"])
@admin_required
@handle_api_errors
def delete_trading_pair(pair_id):
    """删除交易对

    删除指定的交易对记录。

    Args:
        pair_id (int): 交易对ID

    Returns:
        Response: 删除操作结果

    Raises:
        404: 交易对不存在

    Example:
        DELETE /api/blockchain/trading-pairs/1
    """
    TradingPairService.delete_trading_pair(pair_id)
    return Response.success(message="Trading pair deleted")


@blockchain_bp.route("/trading-pairs/<int:pair_id>", methods=["GET"])
@jwt_required()
@handle_api_errors
def get_trading_pair(pair_id):
    """获取交易对详情

    根据交易对ID获取交易对的详细信息。

    Args:
        pair_id (int): 交易对ID

    Returns:
        Response: 交易对详细信息
            - id: 交易对ID
            - base_asset_id: 基础资产ID
            - quote_asset_id: 计价资产ID
            - pair_name: 交易对名称
            - min_price: 最小价格
            - max_price: 最大价格
            - price_precision: 价格精度
            - amount_precision: 数量精度
            - status: 状态
            - created_at: 创建时间
            - updated_at: 更新时间

    Raises:
        404: 交易对不存在

    Example:
        GET /api/blockchain/trading-pairs/1
    """
    result = TradingPairService.get_trading_pair(pair_id)
    return Response.success(result.to_dict())  # 添加to_dict


@blockchain_bp.route("/trading-pairs", methods=["GET"])
@jwt_required()
@handle_api_errors
def list_trading_pairs():
    """分页查询交易对列表

    获取系统中所有交易对的分页列表。

    Args:
        Query Parameters:
            page (int, optional): 页码，默认为1
            per_page (int, optional): 每页数量，默认为10

    Returns:
        Response: 分页的交易对列表
            - total: 总数量
            - items: 交易对数组
            - page: 当前页码
            - per_page: 每页数量

    Example:
        GET /api/blockchain/trading-pairs?page=1&per_page=20
    """
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)

    service = TradingPairService()
    result = service.list_trading_pairs(page=page, per_page=per_page)
    # 修改列表结果序列化
    result['items'] = [item.to_dict() for item in result['items']]
    return Response.success(result)


@blockchain_bp.route('/enhanced-trading-pairs', methods=['GET'])
@jwt_required()
def list_enhanced_trading_pairs():
    """获取增强的交易对列表，包含最后一单价格、24小时涨跌幅和交易量"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    base_asset_type = request.args.get('base_asset_type')

    result = TradingPairService.get_enhanced_trading_pairs(
        page=page,
        per_page=per_page,
        base_asset_type=base_asset_type
    )
    return Response.success(result)