from flask import Blueprint, request, current_app

from app.middlewares.auth import admin_required
from app.services.points_service import PointsService
from app.utils.response import Response
from app.tasks.device_project_validation import validate_device_projects
from app.utils.redis_client import RedisClient

tools_bp = Blueprint("tools", __name__, url_prefix="/tools")


@tools_bp.route("/add_init_invite_record", methods=["POST"])
@admin_required
def add_init_invite_record():
    data = request.get_json()
    inviter_id = data['inviter_id']
    invitee_id = data['invitee_id']
    if inviter_id is None or invitee_id is None:
        return Response.error('inviter_id is None or invitee_id is None')
    if inviter_id == invitee_id:
        return Response.error('inviter_id == invitee_id')
    response = PointsService.add_init_invite_record(inviter_id, invitee_id)
    # 触发异步任务验证设备项目记录
    try:
        validate_device_projects.delay(invitee_id)
    except Exception as e:
        current_app.logger.warning(f"Failed to queue device project validation task: {str(e)}")
        # Continue with registration even if task queueing fails
    return response


@tools_bp.route("/blacklist", methods=["POST"])
@admin_required
def add_to_blacklist():
    """手动添加黑名单（支持user和ip维度）"""
    try:
        # 获取请求参数
        data = request.get_json()
        if not data or "type" not in data or "target" not in data:
            return Response.validation_error("缺少必要参数")

        # 校验类型
        if data["type"] not in ["user", "ip"]:
            return Response.validation_error("type参数必须为user或ip")

        # 添加黑名单
        redis = RedisClient.get_instance()
        key = f"blacklist:{data['type']}:{data['target']}"
        redis.set(key, 1)

        return Response.success(message="操作成功")
    except Exception as e:
        return Response.error(f"操作失败: {str(e)}", 500)


@tools_bp.route("/blacklist", methods=["DELETE"])
@admin_required
def delete_blacklist():
    """手动删除黑名单（支持user和ip维度）"""
    try:
        # 获取请求参数
        data = request.get_json()
        if not data or "type" not in data or "target" not in data:
            return Response.validation_error("缺少必要参数")

        # 校验类型
        if data["type"] not in ["user", "ip"]:
            return Response.validation_error("type参数必须为user或ip")

        redis = RedisClient.get_instance()
        key = f"blacklist:{data['type']}:{data['target']}"
        redis.delete(key)

        return Response.success(message="操作成功")
    except Exception as e:
        return Response.error(f"操作失败: {str(e)}", 500)


@tools_bp.route("/blacklist", methods=["GET"])
@admin_required
def get_blacklist():
    """查看黑名单"""
    try:
        redis = RedisClient.get_instance()
        # 获取所有黑名单键
        user_keys = redis.keys("blacklist:user:*")
        ip_keys = redis.keys("blacklist:ip:*")
        
        # 获取黑名单内容
        blacklist = {
            "users": [key.split(":")[2] for key in user_keys],
            "ips": [key.split(":")[2] for key in ip_keys]
        }
        return Response.success(blacklist)
    except Exception as e:
        return Response.error(f"获取黑名单失败: {str(e)}", 500)

