"""增强的订单管理API - 包含完整资产信息"""

from flask import Blueprint, request
from flask_jwt_extended import jwt_required, get_jwt_identity
from decimal import Decimal

from app.models.order import Order, OrderStatusEnum, OrderSideEnum, OrderTypeEnum
from app.models.blockchain import TradingPair, AssetType, AssetTypeEnum
from app.models.base import db
from app.utils.auth import get_current_user
from app.utils.decorators import handle_api_errors
from app.utils.response import Response
from app.services.order_service import OrderService

enhanced_orders_bp = Blueprint("enhanced_orders", __name__)


@enhanced_orders_bp.route('', methods=['GET'])
@jwt_required()
@handle_api_errors
def list_user_orders_enhanced():
    """获取用户订单列表（增强版）
    
    获取当前用户的订单列表，包含完整的交易对和资产信息。
    
    Args:
        Query Parameters:
            status (str, optional): 订单状态过滤，可选值：PENDING, PARTIAL, FILLED, CANCELED, FAILED
            side (str, optional): 买卖方向过滤，可选值：BUY, SELL
            pair_id (int, optional): 交易对ID过滤
            start_time (str, optional): 开始时间过滤
            end_time (str, optional): 结束时间过滤
            page (int, optional): 页码，默认为1
            per_page (int, optional): 每页数量，默认为20
    
    Returns:
        Response: 订单列表，每个订单包含：
            - 基础订单信息
            - 交易对信息（包含来源和目标资产详情）
            - 资产流向信息
            - 订单进度信息
    
    Example:
        GET /api/enhanced-orders?status=PENDING&side=BUY
    """

    current_user = get_current_user()
    target_user_id = current_user.id

    if current_user.is_admin and request.args.get('user_id'):
        target_user_id = request.args.get('user_id')

    # 获取查询参数
    status = request.args.get('status')
    side = request.args.get('side')
    pair_id = request.args.get('pair_id', type=int)
    start_time = request.args.get('start_time')
    end_time = request.args.get('end_time')
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    asset_type = request.args.get('asset_type', '')
    order_source = request.args.get('order_source', '')

    # 构建查询
    query = db.session.query(Order, TradingPair).join(
        TradingPair, Order.pair_id == TradingPair.id
    ).filter(Order.user_id == target_user_id)

    if asset_type == 'POINTS':
        query = query.join(AssetType, TradingPair.base_asset_id == AssetType.id)
        query = query.filter(AssetType.type == AssetTypeEnum.POINTS)
    elif asset_type == 'TOKEN':
        query = query.join(AssetType, TradingPair.base_asset_id == AssetType.id)
        query = query.filter(AssetType.type == AssetTypeEnum.TOKEN)

    if order_source:
        query = query.filter(Order.order_source == order_source)
    # 状态过滤
    if status:
        try:
            status_enum = OrderStatusEnum[status]
            query = query.filter(Order.status == status_enum)
        except KeyError:
            return Response.error(f"无效的订单状态: {status}", 400)

    # 买卖方向过滤
    if side:
        try:
            side_enum = OrderSideEnum[side]
            query = query.filter(Order.side == side_enum)
        except KeyError:
            return Response.error(f"无效的买卖方向: {side}", 400)

    # 交易对过滤
    if pair_id:
        query = query.filter(Order.pair_id == pair_id)

    # 时间过滤
    if start_time:
        query = query.filter(Order.created_at >= start_time)
    if end_time:
        query = query.filter(Order.created_at <= end_time)

    # 分页
    total = query.count()
    items = query.order_by(Order.created_at.desc()).offset(
        (page - 1) * per_page
    ).limit(per_page).all()

    # 构建响应数据
    orders_list = []
    for order, trading_pair in items:
        order_info = OrderService.build_enhanced_order_info(order)
        orders_list.append(order_info)

    return Response.success({
        "total": total,
        "items": orders_list,
        "page": page,
        "per_page": per_page
    })


@enhanced_orders_bp.route('/<int:order_id>', methods=['GET'])
@jwt_required()
@handle_api_errors
def get_order_detail_enhanced(order_id):
    """获取订单详情（增强版）
    
    获取指定订单的详细信息，包含完整的资产信息和成交记录。
    
    Args:
        order_id (int): 订单ID
    
    Returns:
        Response: 订单详情
    
    Raises:
        404: 订单不存在或无权限访问
    
    Example:
        GET /api/enhanced-orders/123
    """
    current_user = get_current_user()
    target_user_id = current_user.id

    if current_user.is_admin and request.args.get('user_id'):
        target_user_id = request.args.get('user_id')

    # 查询订单和交易对信息
    result = db.session.query(Order).filter(
        Order.id == order_id,
        Order.user_id == target_user_id
    ).first()

    if not result:
        return Response.error('订单不存在或无权限访问', 404)

    order, trading_pair = result

    # 构建详细信息
    order_detail = OrderService.build_enhanced_order_info(order)

    # 添加成交记录
    from app.models.order import OrderMatch
    matches = db.session.query(OrderMatch).filter(
        (OrderMatch.maker_order_id == order_id) |
        (OrderMatch.taker_order_id == order_id)
    ).order_by(OrderMatch.created_at.desc()).all()

    order_detail["matches"] = [match.to_dict(include_username=True) for match in matches]
    order_detail["match_count"] = len(matches)

    return Response.success(order_detail)


@enhanced_orders_bp.route('/summary', methods=['GET'])
@jwt_required()
@handle_api_errors
def get_orders_summary():
    """获取用户订单汇总信息
    
    获取用户订单的统计信息。
    
    Returns:
        Response: 订单汇总信息
            - total_orders: 总订单数
            - pending_orders: 待成交订单数
            - filled_orders: 已完成订单数
            - canceled_orders: 已取消订单数
            - buy_orders: 买单数量
            - sell_orders: 卖单数量
    
    Example:
        GET /api/enhanced-orders/summary
    """
    current_user = get_current_user()
    target_user_id = current_user.id

    if current_user.is_admin and request.args.get('user_id'):
        target_user_id = request.args.get('user_id')

    # 查询用户所有订单
    orders = db.session.query(Order).filter(Order.user_id == target_user_id).all()

    # 统计信息
    total_orders = len(orders)
    pending_orders = sum(1 for order in orders if
                         order.status == OrderStatusEnum.PENDING.value or order.status == OrderStatusEnum.PARTIAL.value)
    filled_orders = sum(1 for order in orders if order.status == OrderStatusEnum.FILLED.value)
    canceled_orders = sum(1 for order in orders if order.status == OrderStatusEnum.CANCELED.value)
    buy_orders = sum(1 for order in orders if order.side == OrderSideEnum.BUY.value)
    sell_orders = sum(1 for order in orders if order.side == OrderSideEnum.SELL.value)

    return Response.success({
        "total_orders": total_orders,
        "pending_orders": pending_orders,
        "filled_orders": filled_orders,
        "canceled_orders": canceled_orders,
        "buy_orders": buy_orders,
        "sell_orders": sell_orders,
        #       "last_updated": db.func.now().isoformat() if orders else None
    })
