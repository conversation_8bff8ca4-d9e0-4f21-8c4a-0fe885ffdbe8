from flask import Blueprint, request
from app.middlewares.auth import admin_required
from app.middlewares.request_metrics_hooks import refresh_switches
from app.utils.redis_client import RedisClient
from app.utils.response import Response
import datetime

metrics_bp = Blueprint("system_metrics", __name__)

@metrics_bp.route("/statistics", methods=["GET"])
@admin_required
def get_metrics_statistics():
    """获取多维度的metrics统计数据"""
    try:

        # 获取查询参数
        dimension = request.args.get("dimension", "user")  # user/ip/api
        limit = request.args.get("limit", 100, type=int)

        redis = RedisClient.get_instance()
        results = []

        # 根据维度查询数据
        if dimension == "user":
            pattern = f"api_metrics_count:user:*"
            keys = redis.keys(pattern)
            for key in keys:
                user_id = key.split(":")[2]
                count = int(redis.get(key) or 0)
                results.append({"user_id": user_id, "count": count})
        
        elif dimension == "ip":
            pattern = f"api_metrics_count:ip:*"
            keys = redis.keys(pattern)
            for key in keys:
                ip = key.split(":")[2]
                count = int(redis.get(key) or 0)
                results.append({"ip": ip, "count": count})
        
        elif dimension == "api":
            pattern = f"api_metrics_count:api:*"
            keys = redis.keys(pattern)
            for key in keys:
                api_path = key.split(":")[2]
                count = int(redis.get(key) or 0)
                results.append({"api_path": api_path, "count": count})

        elif dimension == "user_api_ip":
            pattern = f"api_metrics_count:user_api_ip:*"
            keys = redis.keys(pattern)
            for key in keys:
                user_id = key.split(":")[2]
                api_path = key.split(":")[3]
                ip = key.split(":")[4]
                count = int(redis.get(key) or 0)
                results.append({"user_id": user_id, "ip": ip, "api_path": api_path, "count": count})

        # 按count倒序排序并限制数量
        results.sort(key=lambda x: x["count"], reverse=True)
        return Response.success(results[:limit])

    except Exception as e:
        return Response.error(f"获取统计失败: {str(e)}", 500)

@metrics_bp.route("/user/<int:user_id>", methods=["GET"])
@admin_required
def get_user_metrics_detail(user_id):
    """获取单用户维度的详细metrics数据"""
    try:

        # 获取查询参数
        hour = request.args.get("hour")
        hour = hour if hour else datetime.datetime.now().strftime("%Y%m%d%H")

        if not user_id:
            return Response.validation_error("缺少user_id参数")

        redis = RedisClient.get_instance()
        
        # 1. 用户总访问量
        total_count = int(redis.get(f"api_metrics_count:user_hour:{user_id}:{hour}") or 0)
        
        # 2. 用户API访问明细
        api_pattern = f"api_metrics_count:user_api_hour:{user_id}:*:{hour}"
        api_keys = redis.keys(api_pattern)
        api_details = {}
        for key in api_keys:
            api_path = key.split(":")[3]
            api_details[api_path] = int(redis.get(key) or 0)

        # 3. 用户API+IP访问明细
        api_ip_pattern = f"api_metrics_count:user_api_ip_hour:{user_id}:*:*:{hour}"
        api_ip_keys = redis.keys(api_ip_pattern)
        api_ip_details = {}
        for key in api_ip_keys:
            parts = key.split(":")
            api_path = parts[3]
            ip = parts[4]
            if api_path not in api_ip_details:
                api_ip_details[api_path] = {}
            api_ip_details[api_path][ip] = int(redis.get(key) or 0)

        return Response.success({
            "user_id": user_id,
            "hour": hour,
            "total_count": total_count,
            "api_details": api_details,
            "api_ip_details": api_ip_details
        })

    except Exception as e:
        return Response.error(f"获取用户详情失败: {str(e)}", 500)

@metrics_bp.route("", methods=["DELETE"])
@admin_required
def clear_metrics():
    """清空 metrics 数据"""
    try:
        redis = RedisClient.get_instance()
        # 删除所有 metrics 相关的键
        metrics_keys = redis.keys("api_metrics_count:*")
        for key in metrics_keys:
            redis.delete(key)
        return Response.success(message="Metrics 数据已清空")
    except Exception as e:
        return Response.error(f"清空 metrics 失败: {str(e)}", 500)


@metrics_bp.route("/switch", methods=["GET"])
@admin_required
def get_switch():
    """获取开关状态"""
    try:
        redis = RedisClient.get_instance()
        metrics_enabled = redis.get("switch:metrics") != "false"
        blacklist_enabled = redis.get("switch:blacklist") != "false"
        return Response.success({
            "metrics_enabled": metrics_enabled,
            "blacklist_enabled": blacklist_enabled
        })
    except Exception as e:
        return Response.error(f"获取开关状态失败: {str(e)}", 500)

@metrics_bp.route("/switch", methods=["POST"])
@admin_required
def update_switch():
    """更新开关状态"""
    try:
        data = request.get_json()
        if not data or "type" not in data or "enabled" not in data:
            return Response.validation_error("缺少必要参数")

        # 校验类型
        if data["type"] not in ["metrics", "blacklist"]:
            return Response.validation_error("type参数必须为metrics或blacklist")

        # 更新开关状态
        redis = RedisClient.get_instance()
        key = f"switch:{data['type']}"
        redis.set(key, "true" if data["enabled"] else "false")

        # 发布更新消息
        redis.publish('switch_update', 'refresh')
        
        return Response.success(message="操作成功")
    except Exception as e:
        return Response.error(f"操作失败: {str(e)}", 500)
