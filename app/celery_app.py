from celery import Celery
from flask import current_app

# 创建基础 Celery 实例
celery = Celery('box-backend')

def create_celery_app(app):
    """
    创建 Celery 应用实例
    
    Args:
        app: Flask 应用实例
    
    Returns:
        Celery: Celery 应用实例
    """
    class ContextTask(celery.Task):
        def __call__(self, *args, **kwargs):
            with app.app_context():
                return self.run(*args, **kwargs)

    celery.Task = ContextTask
    
    # 直接使用 CELERY_CONFIG
    celery.conf.update(app.config['CELERY_CONFIG'])
    
    return celery
