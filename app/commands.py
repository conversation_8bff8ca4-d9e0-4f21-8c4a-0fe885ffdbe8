"""命令行模块"""
import os
import traceback

import click
import yaml
from flask import current_app
from flask.cli import with_appcontext

from app.models.base import db
from app.models.device import Device
from app.models.device_project import DeviceProject
from app.models.project import Project
from app.models.project_file import ProjectFile
from app.models.service_config import ServiceConfig
from app.models.user import User
from app.services.project_service import ProjectService
from app.services.metrics_service import metrics_service
from scripts.generate_snapshot import generate_snapshot
from scripts.migrate_ubi_points_to_assets import migrate_ubi_points


def register_commands(app):
    """注册命令"""
    app.cli.add_command(init_db_command)
    app.cli.add_command(create_admin)
    app.cli.add_command(init_demo_data)
    app.cli.add_command(reset_db_command)
    app.cli.add_command(aggregate_command)
    app.cli.add_command(sync_blockchain_command)
    app.cli.add_command(sync_chain_command)
    app.cli.add_command(process_settlements_command)
    app.cli.add_command(blockchain_status_command)
    app.cli.add_command(migrate_ubi_points_command)


@click.command("init-db")
@with_appcontext
def init_db_command():
    """初始化数据库"""
    db.create_all()
    click.echo("数据库初始化完成。")


@click.command("reset-db")
@click.confirmation_option(prompt="确定要重置数据库吗？这将删除所有数据！")
@with_appcontext
def reset_db_command():
    """重置数据库（删除所有数据并重新创建表）"""
    db.drop_all()
    db.create_all()
    click.echo("数据库已重置。")


@click.command("create-admin")
@click.option("--username", prompt="管理员用户名", help="管理员用户名")
@click.option("--password", prompt=True, hide_input=True, confirmation_prompt=True, help="管理员密码")
@click.option("--email", prompt="管理员邮箱", help="管理员邮箱")
@with_appcontext
def create_admin(username, password, email):
    """创建管理员用户"""
    try:
        admin = User(
            username=username,
            email=email,
            role="admin"
        )
        admin.set_password(password)
        db.session.add(admin)
        db.session.commit()
        click.echo(f"管理员 {username} 创建成功。")
    except Exception as e:
        click.echo(f"创建管理员用户失败: {str(e)}")


@click.command("init-demo-data")
@click.option("--data-file", default="demo_data/demo.yaml", help="演示数据 YAML 文件路径")
@with_appcontext
def init_demo_data(data_file):
    """初始化演示数据"""
    try:
        # 获取项目根目录
        root_dir = current_app.root_path
        project_root = os.path.dirname(root_dir)
        yaml_path = os.path.join(project_root, data_file)

        # 检查文件是否存在
        if not os.path.exists(yaml_path):
            click.echo(f"演示数据文件不存在: {yaml_path}")
            return

        # 加载 YAML 数据
        with open(yaml_path, "r", encoding="utf-8") as f:
            demo_data = yaml.safe_load(f)

        # 1. 创建用户
        users = {}
        for user_data in demo_data.get("users", []):
            user = User(
                username=user_data["username"],
                email=user_data["email"],
                role=user_data.get("role", "user")
            )
            user.set_password(user_data["password"])
            db.session.add(user)
            users[user.username] = user

        # 2. 创建服务配置
        service_configs = {}
        for config_data in demo_data.get("service_configs", []):
            # 如果 docker_compose 是字符串，解析为 JSON 对象
            docker_compose = config_data["docker_compose"]
            if isinstance(docker_compose, str):
                docker_compose = yaml.safe_load(docker_compose)

            service_config = ServiceConfig(
                name=config_data["name"],
                description=config_data["description"],
                docker_compose=docker_compose,
                default_env=config_data.get("default_env", {})
            )
            db.session.add(service_config)
            service_configs[service_config.name] = service_config

        db.session.flush()

        # 3. 创建项目
        projects = {}
        for project_data in demo_data.get("projects", []):
            project = Project(
                name=project_data["name"],
                description=project_data["description"],
                service_config_id=project_data["service_config_id"],
                form_schema=project_data["form_schema"],
                service_compose=project_data.get("service_compose", {})
            )
            db.session.add(project)
            projects[project.name] = project

            # 创建项目文件
            for file_data in project_data.get("files", []):
                project_file = ProjectFile(
                    project=project,
                    name=file_data["name"],
                    content=file_data["content"]
                )
                db.session.add(project_file)

        db.session.flush()

        # 4. 创建设备
        devices = {}
        for device_data in demo_data.get("devices", []):
            device = Device(
                name=device_data["name"],
                ip_address=device_data["ip_address"],
                mac_address=device_data["mac_address"],
                description=device_data["description"],
                owner_id=users[device_data["owner"]].id,
                tags=device_data.get("tags", ""),
                system_app_configs=device_data.get("system_app_configs", {}),
                service_configs=device_data.get("service_configs", {})
            )
            db.session.add(device)
            devices[device.name] = device

        db.session.flush()

        # 5. 创建设备项目关联
        for link_data in demo_data.get("device_projects", []):
            device_project = DeviceProject(
                device_id=devices[link_data["device"]].id,
                project_id=projects[link_data["project"]].id,
                data=link_data.get("data", {})
            )
            db.session.add(device_project)

        db.session.commit()
        click.echo("演示数据初始化完成！")

        # 输出创建的用户信息
        click.echo("\n创建的用户:")
        for user_data in demo_data.get("users", []):
            click.echo(f"用户名: {user_data['username']}, 密码: {user_data['password']}")

        # 测试生成服务配置
        click.echo("\n测试生成服务配置:")
        # 选择一个测试设备和其关联的项目
        test_device = Device.query.filter_by(name="depin-bot-server-1").first()
        if test_device:
            click.echo(f"\n设备: {test_device.name}")
            # 获取设备的所有项目配置
            device_projects = DeviceProject.query.filter_by(device_id=test_device.id).all()
            project_service = ProjectService()

            for device_project in device_projects:
                project = Project.query.get(device_project.project_id)
                click.echo(f"\n项目: {project.name}")

                # 生成服务配置
                config = project_service.generate_device_project_config(device_project)

                if config:
                    # 格式化输出配置
                    click.echo("\nDocker Compose 配置:")
                    click.echo(yaml.dump(config["docker_compose"], default_flow_style=False))

                    if config.get("files"):
                        click.echo("\n项目文件:")
                        for file_name, content in config["files"].items():
                            click.echo(f"\n{file_name}:")
                            click.echo(content)

    except Exception:
        db.session.rollback()
        click.echo(f"初始化演示数据失败: {traceback.format_exc()}")


@click.command("aggregate")
@click.option('--schedule', is_flag=True, help='是否将任务调度到 Celery 队列中（异步执行）')
@with_appcontext
def aggregate_command(schedule):
    """从 ServiceMetricsDetail 聚合数据到 ServiceMetrics 表"""
    try:
        if schedule:
            # 调度为 Celery 任务
            from app.tasks.metrics_tasks import aggregate_service_metrics
            result = aggregate_service_metrics.delay()
            click.echo(f"已将聚合任务调度到 Celery 队列中，任务 ID: {result.id}")
        else:
            # 同步执行聚合
            count = metrics_service.aggregate_metrics()
            click.echo(f"已成功聚合 {count} 个服务的指标")
            click.echo("开始生成服务指标快照...")
            generate_snapshot()
            click.echo("服务指标快照生成完成。")
    except Exception as e:
        click.echo(f"聚合指标时发生错误: {str(e)}")
        raise


@click.command("sync-blockchain")
@with_appcontext
def sync_blockchain_command():
    """手动触发区块链同步"""
    from app.tasks.blockchain_tasks import sync_all_blockchains
    result = sync_all_blockchains.delay()
    click.echo(f"已触发区块链同步任务: {result.id}")


@click.command("sync-chain")
@click.argument('chain_id', type=int)
@with_appcontext
def sync_chain_command(chain_id):
    """同步指定的区块链"""
    from app.tasks.blockchain_tasks import process_blockchain_sync
    result = process_blockchain_sync.delay(chain_id)
    click.echo(f"已触发链 {chain_id} 同步任务: {result.id}")


@click.command("process-settlements")
@with_appcontext
def process_settlements_command():
    """手动处理待处理的结算"""
    from app.tasks.blockchain_tasks import process_pending_settlements
    result = process_pending_settlements.delay()
    click.echo(f"已触发结算处理任务: {result.id}")


@click.command("blockchain-status")
@with_appcontext
def blockchain_status_command():
    """查看区块链同步状态"""
    from app.models.blockchain_sync import BlockchainSyncStatus
    from app.models.blockchain import Blockchain
    from app.services.blockchain_listener_manager import get_listener_status, get_health_status

    click.echo("区块链监听服务状态:")
    click.echo("=" * 80)

    # 监听服务状态
    listener_status = get_listener_status()
    health_status = get_health_status()

    click.echo(f"监听服务运行: {'✅ 是' if listener_status['is_running'] else '❌ 否'}")
    click.echo(f"监听线程存活: {'✅ 是' if listener_status['thread_alive'] else '❌ 否'}")
    click.echo(f"监听器激活: {'✅ 是' if listener_status['listener_running'] else '❌ 否'}")
    click.echo(f"健康状态: {'✅ 健康' if health_status['healthy'] else '❌ 异常'}")

    if health_status['issues']:
        click.echo(f"问题: {', '.join(health_status['issues'])}")

    click.echo("\n区块链同步状态:")
    click.echo("-" * 80)

    blockchains = Blockchain.query.all()
    for blockchain in blockchains:
        sync_status = BlockchainSyncStatus.query.filter_by(
            chain_id=blockchain.id
        ).first()

        if sync_status:
            click.echo(f"{blockchain.chain_name:15} | "
                       f"区块: {sync_status.last_processed_block:>10} | "
                       f"状态: {sync_status.status.value:>10} | "
                       f"错误: {sync_status.error_count:>3}")
        else:
            click.echo(f"{blockchain.chain_name:15} | "
                       f"区块: {'未开始':>10} | "
                       f"状态: {'未知':>10} | "
                       f"错误: {0:>3}")


@click.command("migrate-ubi-points")
@with_appcontext
def migrate_ubi_points_command():
    """同步 UBI 积分"""
    migrate_ubi_points()
    click.echo("已触发积分同步任务: migrate_ubi_points")
