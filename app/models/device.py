"""设备模型模块"""

import secrets

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String, Text
from sqlalchemy.orm import relationship

from app.models.base import BaseModel, db


class Device(BaseModel):
    """设备模型"""

    __tablename__ = "devices"

    id = Column(Integer, primary_key=True)
    name = Column(String(64), nullable=False)
    ip_address = Column(String(15), nullable=False)  # IPv4 地址
    mac_address = Column(
        String(17), nullable=False, unique=True
    )  # MAC 地址 (XX:XX:XX:XX:XX:XX)
    description = Column(Text)
    # status values:
    # 0: initializing
    # 1: wait to configure (default)
    # 2: running
    # 3: failure
    # 4: offline
    status = Column(Integer, default=1)
    token = Column(String(64), unique=True)
    tags = Column(String(256), default="")
    hardware_info = Column(String(256), default="4CPU, 4GB RAM, 128GB, ARM")
    owner_id = Column(Integer, ForeignKey("users.id"), nullable=True)  # 设备所有者
    # 设备配置（JSON 格式）
    system_app_configs = Column(db.JSON, default=dict)  # 系统应用配置
    service_configs = Column(db.JSON, default=dict)  # 服务配置

    # 关联关系
    owner = relationship("User", back_populates="devices")
    device_projects = relationship(
        "DeviceProject", back_populates="device", cascade="all, delete-orphan"
    )

    __mapper_args__ = {"confirm_deleted_rows": False}

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        if not self.token:
            self.token = secrets.token_hex(32)

    def to_dict(self, include_sensitive: bool = False) -> dict:
        """转换为字典

        Args:
            include_sensitive (bool): 是否包含敏感字段，默认为 False

        Returns:
            dict: 设备信息字典
        """
        data = {
            "id": self.id,
            "name": self.name,
            "ip_address": self.ip_address,
            "mac_address": self.mac_address,
            "description": self.description,
            "status": self.status,
            "tags": self.tags.split(",") if self.tags else [],
            "hardware_info": self.hardware_info,
            "owner_id": self.owner_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

        if include_sensitive:
            data.update(
                {
                    "service_configs": self.service_configs,
                    "token": self.token,
                    "system_app_configs": self.system_app_configs,
                }
            )

        return data

    def has_user_permission(self, user_id: int, permission: str = "read") -> bool:
        """检查用户是否有设备权限

        Args:
            user_id: 用户ID
            permission: 权限类型，可选值：read, write

        Returns:
            bool: 是否有权限
        """
        # 设备所有者拥有所有权限
        if self.owner_id == user_id:
            return True
        return False
