from datetime import datetime, timezone
from app.models.base import db, BaseModel

class ServiceMetrics(BaseModel):
    """服务指标数据模型"""
    __tablename__ = 'service_metrics'

    id = db.Column(db.Integer, primary_key=True)
    device_id = db.Column(db.Integer, db.<PERSON>ey('devices.id'), nullable=False)
    project_id = db.Column(db.Integer, nullable=True)
    service_name = db.Column(db.String(100), nullable=False)
    points = db.Column(db.Integer, nullable=False, default=0)
    running_time = db.Column(db.Integer, nullable=False, default=0)  # 以秒为单位
    updated_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    ubi_points = db.Column(db.Float, nullable=True)

    def __init__(self, device_id, service_name, points=0, running_time=0, updated_at=None, project_id=None, ubi_points=None):
        self.device_id = device_id
        self.service_name = service_name
        self.points = points
        self.running_time = running_time
        self.project_id = project_id
        self.updated_at = updated_at or datetime.utcnow()
        self.ubi_points = ubi_points


    def to_dict(self):
        return {
            'id': self.id,
            'project_id': self.project_id,
            'device_id': self.device_id,
            'service_name': self.service_name,
            'points': self.points,
            'ubi_points': self.ubi_points,
            'running_time': self.running_time,
            'updated_at': self.updated_at.isoformat() + 'Z'
        }

    def to_metrics_dict(self) -> dict:
        """Convert only the metrics data to a dictionary."""
        return {
            "running_time": float(self.running_time),
            "ubi_points": self.ubi_points,
            "points": self._calculate_points()
        }

    def _calculate_points(self) -> float:
        """Calculate points based on decimals.
        
        If no decimals are provided, return the original points.
        If decimals are provided (e.g. 4), divide points by 10^decimals.
        """
        if hasattr(self, 'decimals') and self.decimals is not None:
            return float(self.points) / (10 ** self.decimals)
        return float(self.points)


class ServiceMetricsDetail(BaseModel):
    """服务指标数据模型"""
    __tablename__ = 'service_metric_details'
    __table_args__ = (
        db.UniqueConstraint('device_id', 'service_name', 'started_at', name='uix_device_service_start'),
        {'mysql_engine': 'InnoDB', 'mysql_auto_increment': '1'}
    )

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    device_id = db.Column(db.Integer, db.ForeignKey('devices.id'), nullable=False)
    project_id = db.Column(db.Integer, nullable=True)
    service_name = db.Column(db.String(100), nullable=False)
    started_at = db.Column(db.DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc))
    points = db.Column(db.Integer, nullable=False, default=0)
    running_time = db.Column(db.Integer, nullable=False, default=0)  # 以秒为单位
    updated_at = db.Column(db.DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc))
    status_code = db.Column(db.Integer)
    status_msg = db.Column(db.String(1024))

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        if not kwargs.get('updated_at'):
            self.updated_at = datetime.now(timezone.utc)
        if not kwargs.get('started_at'):
            self.started_at = datetime.now(timezone.utc)

    def to_dict(self):
        return {
            'id': self.id,
            'device_id': self.device_id,
            'project_id': self.project_id,
            'service_name': self.service_name,
            'points': self.points,
            'running_time': self.running_time,
            'started_at': self.started_at.isoformat() + 'Z',
            'updated_at': self.updated_at.isoformat() + 'Z'
        }


class ServiceMetricsSnapshot(BaseModel):
    """服务指标快照"""
    __tablename__ = 'service_metrics_snapshots'
    __table_args__ = (
        db.Index('idx_service_name_day', 'day', 'service_name'),
        db.Index('idx_device_service_day', 'device_id', 'day', 'service_name'),
    )

    id = db.Column(db.Integer, primary_key=True)
    device_id = db.Column(db.Integer, db.ForeignKey('devices.id'), nullable=False)
    project_id = db.Column(db.Integer, nullable=True)
    service_name = db.Column(db.String(100), nullable=False)
    day = db.Column(db.String(10), nullable=False)
    points = db.Column(db.Integer, default=0)
    running_time = db.Column(db.Integer, default=0)
    points_increase = db.Column(db.Integer, default=0)
    running_time_increase = db.Column(db.Integer, default=0)
    yesterday_points = db.Column(db.Integer, default=0)
    yesterday_running_time = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    ubi_points = db.Column(db.Float, nullable=True)

    def to_dict(self):
        return {
            'id': self.id,
            'device_id': self.device_id,
            'service_name': self.service_name,
            'day': self.day,
            'points': self.points,
            'ubi_points': self.ubi_points,
            'running_time': self.running_time,
            'points_increase': self.points_increase,
            'running_time_increase': self.running_time_increase,
            'yesterday_points': self.yesterday_points,
            'yesterday_running_time': self.yesterday_running_time,
            'created_at': self.created_at.isoformat() + 'Z'
        }
