"""
区块链同步相关模型
用于记录区块链监听和同步状态
"""

from enum import Enum
from sqlalchemy import Column, Index, UniqueConstraint
from app.models.base import BaseModel, db


class SyncStatusEnum(Enum):
    """同步状态枚举"""
    RUNNING = 'RUNNING'      # 运行中（同步中）
    PAUSED = 'PAUSED'        # 暂停
    ERROR = 'ERROR'          # 错误状态
    STOPPED = 'STOPPED'      # 已停止


class BlockchainSyncStatus(BaseModel):
    """区块链同步状态表"""
    __tablename__ = "blockchain_sync_status"
    __table_args__ = (
        UniqueConstraint('chain_id', name='uk_chain_sync'),
        Index('idx_chain_status', 'chain_id', 'status'),
    )

    id = Column(db.Integer, primary_key=True, autoincrement=True, comment='同步状态ID')
    chain_id = Column(db.Integer, nullable=False, comment='区块链ID')

    # 同步进度
    last_processed_block = Column(db.BigInteger, server_default='0', comment='最后处理的区块号')
    latest_block = Column(db.BigInteger, server_default='0', comment='链上最新区块号')
    sync_progress = Column(db.DECIMAL(5, 2), server_default='0.00', comment='同步进度百分比')

    # 状态信息
    status = Column(db.Enum(SyncStatusEnum), server_default='STOPPED', comment='同步状态')
    last_sync_time = Column(db.DateTime, nullable=True, comment='最后同步时间')
    error_message = Column(db.Text, nullable=True, comment='错误信息')
    error_count = Column(db.Integer, server_default='0', comment='错误次数')

    # 统计信息
    total_transactions_processed = Column(db.BigInteger, server_default='0', comment='已处理交易总数')
    deposits_processed = Column(db.BigInteger, server_default='0', comment='已处理充值数')
    withdrawals_processed = Column(db.BigInteger, server_default='0', comment='已处理提现数')

    # 配置信息
    sync_config = Column(db.JSON, comment='同步配置')

    def to_dict(self):
        """序列化方法"""
        return {
            "id": self.id,
            "chain_id": self.chain_id,
            "last_processed_block": self.last_processed_block,
            "latest_block": self.latest_block,
            "sync_progress": str(self.sync_progress),
            "status": self.status.value,
            "last_sync_time": self.last_sync_time.isoformat() if self.last_sync_time else None,
            "error_message": self.error_message,
            "error_count": self.error_count,
            "total_transactions_processed": self.total_transactions_processed,
            "deposits_processed": self.deposits_processed,
            "withdrawals_processed": self.withdrawals_processed,
            "sync_config": self.sync_config,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

    @classmethod
    def get_or_create_sync_status(cls, chain_id: int) -> 'BlockchainSyncStatus':
        """获取或创建同步状态记录"""
        sync_status = cls.query.filter_by(chain_id=chain_id).first()

        if not sync_status:
            from app.models import db
            sync_status = cls(
                chain_id=chain_id,
                last_processed_block=0,
                status=SyncStatusEnum.RUNNING,
                error_count=0
            )
            db.session.add(sync_status)
            db.session.commit()

        return sync_status

    @classmethod
    def update_processed_block(cls, chain_id: int, block_number: int):
        """更新已处理的区块号"""
        from app.models import db
        from datetime import datetime

        sync_status = cls.get_or_create_sync_status(chain_id)
        sync_status.last_processed_block = block_number
        sync_status.last_sync_time = datetime.utcnow()
        sync_status.status = SyncStatusEnum.RUNNING

        db.session.commit()

    @classmethod
    def record_error(cls, chain_id: int, error_message: str):
        """记录错误"""
        from app.models import db

        sync_status = cls.get_or_create_sync_status(chain_id)
        sync_status.error_count += 1
        sync_status.error_message = error_message
        sync_status.status = SyncStatusEnum.ERROR

        db.session.commit()


class TransactionSyncLog(BaseModel):
    """交易同步日志表"""
    __tablename__ = "transaction_sync_logs"
    __table_args__ = (
        Index('idx_tx_hash', 'tx_hash'),
        Index('idx_chain_block', 'chain_id', 'block_number'),
        Index('idx_sync_time', 'sync_time'),
    )

    id = Column(db.Integer, primary_key=True, autoincrement=True, comment='日志ID')
    chain_id = Column(db.Integer, nullable=False, comment='区块链ID')
    tx_hash = Column(db.String(128), nullable=False, comment='交易哈希')
    block_number = Column(db.BigInteger, nullable=False, comment='区块号')

    # 交易信息
    from_address = Column(db.String(128), nullable=False, comment='发送地址')
    to_address = Column(db.String(128), nullable=False, comment='接收地址')
    amount = Column(db.DECIMAL(18, 8), nullable=False, comment='交易金额')
    token_address = Column(db.String(128), nullable=True, comment='代币合约地址')
    gas_fee = Column(db.DECIMAL(18, 8), server_default='0', comment='Gas费用')

    # 处理信息
    user_id = Column(db.Integer, nullable=True, comment='关联用户ID')
    flow_id = Column(db.Integer, nullable=True, comment='关联流水ID')
    settlement_id = Column(db.Integer, nullable=True, comment='关联结算ID')

    # 状态信息
    sync_status = Column(db.Enum('PENDING', 'PROCESSED', 'IGNORED', 'FAILED'),
                        server_default='PENDING', comment='同步状态')
    sync_time = Column(db.DateTime, nullable=False, comment='同步时间')
    process_time = Column(db.DateTime, nullable=True, comment='处理时间')
    error_message = Column(db.Text, nullable=True, comment='错误信息')

    # 扩展字段
    extend_field = Column(db.JSON, comment='扩展字段')

    def to_dict(self):
        """序列化方法"""
        return {
            "id": self.id,
            "chain_id": self.chain_id,
            "tx_hash": self.tx_hash,
            "block_number": self.block_number,
            "from_address": self.from_address,
            "to_address": self.to_address,
            "amount": str(self.amount),
            "token_address": self.token_address,
            "gas_fee": str(self.gas_fee),
            "user_id": self.user_id,
            "flow_id": self.flow_id,
            "settlement_id": self.settlement_id,
            "sync_status": self.sync_status,
            "sync_time": self.sync_time.isoformat() if self.sync_time else None,
            "process_time": self.process_time.isoformat() if self.process_time else None,
            "error_message": self.error_message,
            "extend_field": self.extend_field
        }



class AddressMonitor(BaseModel):
    """地址监控表"""
    __tablename__ = "address_monitors"
    __table_args__ = (
        Index('idx_chain_address', 'chain_id', 'address'),
        Index('idx_user_chain', 'user_id', 'chain_id'),
    )

    id = Column(db.Integer, primary_key=True, autoincrement=True, comment='监控ID')
    user_id = Column(db.Integer, nullable=False, comment='用户ID')
    chain_id = Column(db.Integer, nullable=False, comment='区块链ID')
    address = Column(db.String(128), nullable=False, comment='监控地址')
    address_type = Column(db.Enum('DEPOSIT', 'WITHDRAW', 'BOTH'),
                         server_default='BOTH', comment='地址类型')

    # 监控状态
    is_active = Column(db.Boolean, server_default='1', comment='是否启用监控')
    last_activity_block = Column(db.BigInteger, server_default='0', comment='最后活动区块')
    last_activity_time = Column(db.DateTime, nullable=True, comment='最后活动时间')

    # 统计信息
    total_deposits = Column(db.Integer, server_default='0', comment='总充值次数')
    total_withdrawals = Column(db.Integer, server_default='0', comment='总提现次数')
    total_deposit_amount = Column(db.DECIMAL(18, 8), server_default='0', comment='总充值金额')
    total_withdrawal_amount = Column(db.DECIMAL(18, 8), server_default='0', comment='总提现金额')

    # 扩展字段
    extend_field = Column(db.JSON, comment='扩展字段')

    def to_dict(self):
        """序列化方法"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "chain_id": self.chain_id,
            "address": self.address,
            "address_type": self.address_type,
            "is_active": self.is_active,
            "last_activity_block": self.last_activity_block,
            "last_activity_time": self.last_activity_time.isoformat() if self.last_activity_time else None,
            "total_deposits": self.total_deposits,
            "total_withdrawals": self.total_withdrawals,
            "total_deposit_amount": str(self.total_deposit_amount),
            "total_withdrawal_amount": str(self.total_withdrawal_amount),
            "extend_field": self.extend_field,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

