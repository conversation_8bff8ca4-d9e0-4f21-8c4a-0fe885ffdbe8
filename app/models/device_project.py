"""设备项目关联模型"""
import jinja2
from flask import current_app
from sqlalchemy import JSON, Column, ForeignKey, Integer, String
from sqlalchemy.orm import relationship

from app.models.base import BaseModel, db


class DeviceProject(BaseModel):
    """设备项目关联模型，存储设备特定的项目数据"""
    __tablename__ = "device_projects"

    id = Column(Integer, primary_key=True)
    device_id = Column(Integer, ForeignKey("devices.id"), nullable=False)
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False)
    state = Column(String(32), nullable=False, default="created")  # 状态：created, updated, running, stopped
    data = Column(JSON, nullable=False, default=dict)  # 存储用户录入的数据
    continuous_running_checks = Column(Integer, default=0)  # 连续运行检查次数，用于计算积分

    # 唯一性约束
    __table_args__ = (
        db.UniqueConstraint("device_id", "project_id", name="uix_device_project"),
    )

    # 关联关系
    device = relationship("Device", back_populates="device_projects")
    project = relationship("Project", back_populates="device_projects")

    __mapper_args__ = {
        "confirm_deleted_rows": False
    }

    def render_file_content(self, file_content):
        """渲染文件内容，使用用户输入的数据"""
        try:
            template = jinja2.Template(file_content)
            # 确保 data 是字典类型
            data = self.data if isinstance(self.data, dict) else {}
            return template.render(**data)
        except Exception as e:
            current_app.logger.error(f"渲染文件内容失败: {str(e)}")
            return file_content

    def generate_service_config(self):
        """生成设备的服务配置

        将用户输入的数据渲染到项目文件模板中，然后与服务配置模板合并
        """
        if not self.project.service_config:
            return {}

        # 获取基础服务配置
        base_config = self.project.service_config.to_dict()

        # 创建配置文件字典
        config_files = {}

        # 渲染项目文件
        for project_file in self.project.files:
            rendered_content = self.render_file_content(project_file.content)
            config_files[project_file.name] = rendered_content

        # 合并配置
        service_config = {
            "docker_compose": base_config.get("docker_compose", ""),
            "env": base_config.get("env", {}),
            "configs": config_files,
            "projects": [{
                "name": self.project.name,
                "files": [f.name for f in self.project.files]
            }]
        }

        return service_config

    def update_device_service_config(self):
        """更新设备的服务配置"""
        service_config = self.generate_service_config()
        if service_config:
            # 更新设备的服务配置
            if not self.device.service_configs:
                self.device.service_configs = {}
            # 更新设备项目状态
            self.state = "updated"
            db.session.commit()

    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "device_id": self.device_id,
            "project_id": self.project_id,
            "state": self.state,
            "data": self.data,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }
