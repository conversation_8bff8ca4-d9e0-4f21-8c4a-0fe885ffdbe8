"""公共服务配置模块，用于管理可复用的基础服务配置模板"""
from app.models.base import BaseModel, db


class ServiceConfig(BaseModel):
    """公共服务配置模板

    用于存储和管理可被多个项目复用的基础服务配置，包括：
    - docker-compose 基础模板（JSON格式）
    - 默认环境变量
    - 配置说明等

    只有管理员可以管理这些配置
    """
    __tablename__ = "service_configs"

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), unique=True, nullable=False)
    description = db.Column(db.String(200))
    docker_compose = db.Column(db.JSON, nullable=False)  # 基础 docker-compose 模板（JSON格式）
    default_env = db.Column(db.JSON)  # 默认环境变量

    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "docker_compose": self.docker_compose,
            "env": self.default_env
        }
