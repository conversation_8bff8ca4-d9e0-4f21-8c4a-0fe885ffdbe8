"""
结算相关模型
用于延迟结算系统
"""
from enum import Enum
from sqlalchemy import Column, Index, ForeignKey
from sqlalchemy.orm import relationship
from app.models.base import BaseModel, db


class SettlementStatusEnum(Enum):
    """结算状态枚举"""
    PENDING = 'PENDING'      # 待结算
    PROCESSING = 'PROCESSING'  # 结算中
    COMPLETED = 'COMPLETED'   # 已完成
    FAILED = 'FAILED'        # 失败


class TradeFlowTypeEnum(Enum):
    """交易流水类型枚举"""
    BUY = 'BUY'              # 买入
    SELL = 'SELL'            # 卖出
    FEE = 'FEE'              # 手续费
    DEPOSIT = 'DEPOSIT'       # 充值
    WITHDRAW = 'WITHDRAW'     # 提现


class TradeFlow(BaseModel):
    """交易流水表 - 记录所有交易行为"""
    __tablename__ = "trade_flows"
    __table_args__ = (
        Index('idx_user_asset_time', 'user_id', 'asset_type_id', 'created_at'),
        Index('idx_trade_order_id', 'order_id'),
        Index('idx_settlement_status', 'settlement_status'),
    )

    id = Column(db.Integer, primary_key=True, autoincrement=True, comment='流水ID')
    user_id = Column(db.Integer, nullable=False, comment='用户ID')
    asset_type_id = Column(db.Integer, nullable=False, comment='资产类型ID')
    flow_type = Column(db.Enum(TradeFlowTypeEnum), nullable=False, comment='流水类型')
    
    # 交易相关
    order_id = Column(db.Integer, nullable=True, comment='关联订单ID')
    trading_pair_id = Column(db.Integer, nullable=True, comment='交易对ID')
    
    # 金额相关
    amount = Column(db.DECIMAL(18, 8), nullable=False, comment='交易数量')
    price = Column(db.DECIMAL(18, 8), nullable=True, comment='交易价格')
    total_value = Column(db.DECIMAL(18, 8), nullable=True, comment='交易总价值')
    fee_amount = Column(db.DECIMAL(18, 8), server_default='0', comment='手续费金额')
    
    # 结算相关
    settlement_status = Column(db.Enum(SettlementStatusEnum), 
                             server_default='PENDING', comment='结算状态')
    settlement_id = Column(db.Integer, nullable=True, comment='结算批次ID')
    
    # 扩展字段
    reference_id = Column(db.String(64), nullable=True, comment='外部引用ID')
    remark = Column(db.String(255), nullable=True, comment='备注')
    extend_field = Column(db.JSON, comment='扩展字段')

    def to_dict(self):
        """序列化方法"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "asset_type_id": self.asset_type_id,
            "flow_type": self.flow_type.value,
            "order_id": self.order_id,
            "trading_pair_id": self.trading_pair_id,
            "amount": str(self.amount),
            "price": str(self.price) if self.price else None,
            "total_value": str(self.total_value) if self.total_value else None,
            "fee_amount": str(self.fee_amount),
            "settlement_status": self.settlement_status.value,
            "settlement_id": self.settlement_id,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }


class UserSettlement(BaseModel):
    """用户结算记录表 - 记录每次提现时的结算结果"""
    __tablename__ = "user_settlements"
    __table_args__ = (
        Index('idx_user_asset_type', 'user_id', 'asset_type_id'),
        Index('idx_status', 'status'),
    )

    id = Column(db.Integer, primary_key=True, autoincrement=True, comment='结算ID')
    user_id = Column(db.Integer, nullable=False, comment='用户ID')
    asset_type_id = Column(db.Integer, nullable=False, comment='资产类型ID')
    
    # 结算范围
    settlement_from = Column(db.DateTime, nullable=False, comment='结算开始时间')
    settlement_to = Column(db.DateTime, nullable=False, comment='结算结束时间')
    
    # 结算结果
    total_buy_amount = Column(db.DECIMAL(18, 8), server_default='0', comment='总买入数量')
    total_buy_value = Column(db.DECIMAL(18, 8), server_default='0', comment='总买入价值')
    total_sell_amount = Column(db.DECIMAL(18, 8), server_default='0', comment='总卖出数量')
    total_sell_value = Column(db.DECIMAL(18, 8), server_default='0', comment='总卖出价值')
    total_fee = Column(db.DECIMAL(18, 8), server_default='0', comment='总手续费')
    
    # 盈亏计算
    net_amount = Column(db.DECIMAL(18, 8), nullable=False, comment='净持仓数量')
    net_value = Column(db.DECIMAL(18, 8), nullable=False, comment='净价值')
    realized_pnl = Column(db.DECIMAL(18, 8), server_default='0', comment='已实现盈亏')
    
    # 提现相关
    withdraw_amount = Column(db.DECIMAL(18, 8), nullable=False, comment='提现数量')
    withdraw_address = Column(db.String(255), nullable=True, comment='提现地址')
    
    # 状态
    status = Column(db.Enum(SettlementStatusEnum), 
                   server_default='PENDING', comment='结算状态')
    
    # 扩展字段
    calculation_details = Column(db.JSON, comment='计算详情')
    remark = Column(db.String(255), nullable=True, comment='备注')

    def to_dict(self):
        """序列化方法"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "asset_type_id": self.asset_type_id,
            "settlement_from": self.settlement_from.isoformat() if self.settlement_from else None,
            "settlement_to": self.settlement_to.isoformat() if self.settlement_to else None,
            "total_buy_amount": str(self.total_buy_amount),
            "total_buy_value": str(self.total_buy_value),
            "total_sell_amount": str(self.total_sell_amount),
            "total_sell_value": str(self.total_sell_value),
            "total_fee": str(self.total_fee),
            "net_amount": str(self.net_amount),
            "net_value": str(self.net_value),
            "realized_pnl": str(self.realized_pnl),
            "withdraw_amount": str(self.withdraw_amount),
            "withdraw_address": self.withdraw_address,
            "status": self.status.value,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }


class SettlementFlowMapping(BaseModel):
    """结算流水映射表 - 记录哪些流水参与了哪次结算"""
    __tablename__ = "settlement_flow_mappings"
    __table_args__ = (
        Index('idx_settlement_id', 'settlement_id'),
        Index('idx_flow_id', 'flow_id'),
    )

    id = Column(db.Integer, primary_key=True, autoincrement=True)
    settlement_id = Column(db.Integer, ForeignKey('user_settlements.id'), 
                          nullable=False, comment='结算ID')
    flow_id = Column(db.Integer, ForeignKey('trade_flows.id'), 
                    nullable=False, comment='流水ID')
    
    # 关系定义
    settlement = relationship('UserSettlement')
    flow = relationship('TradeFlow')
