"""项目文件模型"""
import jinja2
from flask import current_app
from sqlalchemy import Column, Foreign<PERSON>ey, Integer, String, Text
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class ProjectFile(BaseModel):
    """项目文件模型"""
    __tablename__ = "project_files"

    id = Column(Integer, primary_key=True)
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False)
    name = Column(String(255), nullable=False)
    content = Column(Text, nullable=False)

    # 关联关系
    project = relationship("Project", back_populates="files")

    def render_content(self, data):
        """渲染文件内容

        Args:
            data: 渲染数据，用于替换模板中的变量

        Returns:
            str: 渲染后的内容，如果内容为空则返回 None
        """
        if not self.content:
            return None

        try:
            # 创建 Jinja2 环境
            env = jinja2.Environment(
                loader=jinja2.BaseLoader(),
                undefined=jinja2.StrictUndefined  # 严格模式，未定义变量会抛出异常
            )

            # 创建模板
            template = env.from_string(self.content)

            # 渲染模板
            return template.render(**data if data else {})

        except jinja2.exceptions.UndefinedError as e:
            current_app.logger.error(f"Template rendering error - missing variable: {str(e)}")
            return ""
        except Exception as e:
            current_app.logger.error(f"Template rendering error: {str(e)}")
            return ""

    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "project_id": self.project_id,
            "name": self.name,
            "content": self.content,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }
