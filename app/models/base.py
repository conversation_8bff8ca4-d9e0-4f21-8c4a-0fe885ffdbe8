"""数据库基础模块"""

from datetime import datetime

from flask_migrate import Migrate
from flask_sqlalchemy import SQLAlchemy

# 初始化数据库
db = SQLAlchemy()
migrate = Migrate()


def init_db(app):
    """初始化数据库"""
    db.init_app(app)
    migrate.init_app(app, db)


class BaseModel(db.Model):
    """模型基类"""

    __abstract__ = True

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(
        db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow
    )

    def to_dict(self):
        """基础的字典转换方法"""
        result = {}
        for col in self.__table__.columns:
            value = getattr(self, col.name)
            if isinstance(value, datetime):
                value = value.isoformat()
            result[col.name] = value
        return result
