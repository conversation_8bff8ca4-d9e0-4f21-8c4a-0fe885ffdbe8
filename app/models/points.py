from datetime import datetime
from sqlalchemy import UniqueConstraint
from app.models.base import BaseModel, db

class TaskType(BaseModel):
    __tablename__ = 'task_types'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), unique=True, nullable=False)
    points = db.Column(db.Float, nullable=False)
    description = db.Column(db.String(255))

    point_records = db.relationship('PointRecord', back_populates='task_type')

class PointRecord(BaseModel):
    __tablename__ = 'point_records'
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    points = db.Column(db.Float, nullable=False)
    record_type = db.Column(db.String(20), nullable=False)  # 'task', 'invite', 'project'
    task_type_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>ey('task_types.id'), nullable=True)
    related_id = db.Column(db.Integer, nullable=True)
    invitee_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    __table_args__ = (
        UniqueConstraint('user_id', 'task_type_id', name='uix_user_task'),
    )

    # 关系定义
    user = db.relationship('User', foreign_keys=[user_id], back_populates='point_records')
    task_type = db.relationship('TaskType', back_populates='point_records')

    invitee = db.relationship(
        'User',
        foreign_keys=[invitee_id],
        overlaps="invite_records,inviter",
        back_populates='invited_records'
    )
    inviter = db.relationship(
        'User',
        foreign_keys=[invitee_id],
        back_populates='invite_records',
        overlaps="invited_records"
    )

    def to_dict(self, include_sensitive: bool = False) -> dict:
        """转换为字典表示
        Args:
            include_sensitive (bool): 是否包含敏感的关联信息，默认为 False
        Returns:
            dict: 当前积分记录的字典表示
        """
        data = {
            "id": self.id,
            "user_id": self.user_id,
            "points": float(self.points),
            "record_type": self.record_type,
            "task_type_id": self.task_type_id,
            "task_type": self.task_type.name if self.task_type else None,
            "invitee_id": self.invitee_id,
            "invitee_email": self.invitee.email if self.invitee else None,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

        return data
