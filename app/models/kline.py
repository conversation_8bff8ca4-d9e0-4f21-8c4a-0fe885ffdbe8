from app.models.base import BaseModel, db
from sqlalchemy import Index, UniqueConstraint
from sqlalchemy.orm import relationship


class KlineData(BaseModel):
    """K线数据模型"""
    __tablename__ = "kline_data"
    __table_args__ = (
        # 主键约束
        UniqueConstraint('pair_id', 'interval_type', 'open_time', name='uk_kline_unique'),
        # 查询优化索引
        Index('idx_pair_interval_time', 'pair_id', 'interval_type', 'open_time'),
        Index('idx_close_time', 'close_time'),
        Index('idx_pair_interval_close', 'pair_id', 'interval_type', 'close_time'),
    )

    id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='K线ID')
    pair_id = db.Column(db.Integer, db.ForeignKey('trading_pairs.id'), nullable=False, comment='交易对ID')
    interval_type = db.Column(db.String(10), nullable=False, comment='时间间隔类型')
    open_time = db.Column(db.BigInteger, nullable=False, comment='开盘时间(毫秒时间戳)')
    close_time = db.Column(db.BigInteger, nullable=False, comment='收盘时间(毫秒时间戳)')

    # OHLC 数据
    open_price = db.Column(db.DECIMAL(18, 8), nullable=False, comment='开盘价')
    high_price = db.Column(db.DECIMAL(18, 8), nullable=False, comment='最高价')
    low_price = db.Column(db.DECIMAL(18, 8), nullable=False, comment='最低价')
    close_price = db.Column(db.DECIMAL(18, 8), nullable=False, comment='收盘价')

    # 成交量数据
    volume = db.Column(db.DECIMAL(18, 8), nullable=False, server_default='0', comment='成交量(基础货币)')
    quote_volume = db.Column(db.DECIMAL(18, 8), nullable=False, server_default='0', comment='成交额(计价货币)')
    trade_count = db.Column(db.Integer, nullable=False, server_default='0', comment='成交笔数')

    # 技术指标相关字段（可选）
    vwap = db.Column(db.DECIMAL(18, 8), comment='成交量加权平均价格')


    def to_dict(self):
        """序列化为字典"""
        return {
            'id': self.id,
            'pair_id': self.pair_id,
            'interval_type': self.interval_type,
            'open_time': self.open_time,
            'close_time': self.close_time,
            'open': str(self.open_price),
            'high': str(self.high_price),
            'low': str(self.low_price),
            'close': str(self.close_price),
            'volume': str(self.volume),
            'quote_volume': str(self.quote_volume),
            'trade_count': self.trade_count,
            'vwap': str(self.vwap) if self.vwap else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

    def to_ohlcv_array(self):
        """转换为 OHLCV 数组格式（常用于图表库）"""
        return [
            self.open_time,
            float(self.open_price),
            float(self.high_price),
            float(self.low_price),
            float(self.close_price),
            float(self.volume)
        ]

    @classmethod
    def get_supported_intervals(cls):
        """获取支持的时间间隔"""
        return ['M1', 'M5', 'M15', 'M30', 'H1', 'H4', 'H6', 'H12', 'D1', 'W1']

    def __repr__(self):
        return f"<KlineData(pair_id={self.pair_id}, interval={self.interval_type}, " \
               f"open_time={self.open_time}, close={self.close_price})>"