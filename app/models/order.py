from enum import Enum

from sqlalchemy import Column, Index
from sqlalchemy.orm import relationship

from app.models.base import BaseModel, db


class OrderTypeEnum(str, Enum):
    LIMIT = 'LIMIT'
    MARKET = 'MARKET'


class OrderSideEnum(str, Enum):
    BUY = 'BUY'
    SELL = 'SELL'


class OrderStatusEnum(str, Enum):
    PENDING = 'PENDING'
    PARTIAL = 'PARTIAL'
    FILLED = 'FILLED'
    CANCELED = 'CANCELED'
    FAILED = 'FAILED'


class OrderSourceEnum(str, Enum):
    CREATE = 'CREATE'  # 通过create接口创建的订单
    INSTANT_TRADE = 'INSTANT_TRADE'  # 通过instant_trade接口创建的订单


class Order(BaseModel):
    """订单模型"""
    __tablename__ = "orders"
    __table_args__ = (
        Index('idx_user_time', 'user_id', 'created_at'),
        Index('idx_pair_status', 'pair_id', 'status'),
    )

    id = Column(db.Integer, primary_key=True, comment='订单ID(UUID)')
    user_id = Column(db.Integer, db.ForeignKey('users.id'), nullable=False, comment='用户ID')
    order_type = Column(db.Enum(OrderTypeEnum), nullable=False, comment='订单类型')
    side = Column(db.Enum(OrderSideEnum), nullable=False, comment='买卖方向')
    pair_id = Column(db.Integer, db.ForeignKey('trading_pairs.id'), nullable=False, comment='交易对ID')
    price = Column(db.DECIMAL(18, 8), nullable=True, comment='委托价格')
    original_amount = Column(db.DECIMAL(18, 8), nullable=False, comment='委托数量')
    executed_amount = Column(db.DECIMAL(18, 8), server_default='0', comment='已成交数量')
    executed_value = Column(db.DECIMAL(18, 8), server_default='0', comment='已成交金额')
    fee = Column(db.DECIMAL(18, 8), server_default='0', comment='手续费')
    status = Column(db.Enum(OrderStatusEnum), server_default='PENDING', comment='订单状态')
    order_source = Column(db.Enum(OrderSourceEnum), server_default='CREATE', comment='订单来源')

    # 关系定义
    user = relationship('User', back_populates='orders')
    trading_pair = relationship('TradingPair', back_populates='orders')
    # order_matches = relationship('OrderMatch', back_populates='order')

    taker_order_matches = relationship('OrderMatch', back_populates='taker_order',
                                       foreign_keys='OrderMatch.taker_order_id')
    maker_order_matches = relationship('OrderMatch', back_populates='maker_order',
                                       foreign_keys='OrderMatch.maker_order_id')

    # order_logs = relationship('OrderLog', back_populates='order')

    def to_dict(self, include_trading_pair=False):
        """订单序列化方法"""
        result = {
            "id": self.id,
            "user_id": self.user_id,
            "order_type": self.order_type.value if self.order_type is not None else None,
            "side": self.side.value if self.side is not None else None,
            "pair_id": self.pair_id,
            "price": str(self.price) if self.price is not None else None,
            "original_amount": str(self.original_amount),
            "executed_amount": str(self.executed_amount),
            "executed_value": str(self.executed_value),
            "fee": str(self.fee),
            "status": self.status.value if self.status is not None else None,
            "order_source": self.order_source.value if self.order_source is not None else None,
            "created_at": self.created_at.isoformat() if self.created_at is not None else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at is not None else None
        }

        # 如果需要包含交易对信息，添加完整的资产信息
        if include_trading_pair:
            from app.models.blockchain import TradingPair
            trading_pair = TradingPair.query.get(self.pair_id)
            if trading_pair:
                result["trading_pair"] = trading_pair.to_dict()
                result["pair_name"] = trading_pair.pair_name

        return result


class OrderMatch(BaseModel):
    """订单成交记录模型"""
    __tablename__ = "order_matches"
    __table_args__ = (
        Index('idx_order_id', 'order_id'),
    )

    id = Column(db.Integer, primary_key=True, comment='成交ID(UUID)')
    order_id = Column(db.Integer, db.ForeignKey('orders.id'), nullable=False, comment='订单ID')
    maker_order_id = Column(db.Integer, db.ForeignKey('orders.id'), nullable=False, comment='挂单方订单ID')
    taker_order_id = Column(db.Integer, db.ForeignKey('orders.id'), nullable=False, comment='吃单方订单ID')
    price = Column(db.DECIMAL(18, 8), nullable=False, comment='成交价格')
    amount = Column(db.DECIMAL(18, 8), nullable=False, comment='成交数量')

    # 关系定义
    taker_order = relationship('Order', back_populates='taker_order_matches', foreign_keys=[taker_order_id], )
    maker_order = relationship('Order', back_populates='maker_order_matches', foreign_keys=[maker_order_id], )

    def to_dict(self, include_username: bool = False):
        """成交记录序列化方法"""
        result = {
            "id": self.id,
            "order_id": self.order_id,
            "maker_order_id": self.maker_order_id,
            "taker_order_id": self.taker_order_id,
            "price": str(self.price),
            "amount": str(self.amount),
            "created_at": self.created_at.isoformat() if self.created_at is not None else None
        }
        if include_username:
            result.update(
                {
                    "maker_user_name": self.maker_order.user.username,
                    "taker_user_name": self.taker_order.user.username,
                }
            )
        return result


class OrderLog(BaseModel):
    """订单日志模型"""
    __tablename__ = "order_logs"
    __table_args__ = (
        Index('idx_log_order_id', 'order_id'),
    )

    id = Column(db.Integer, primary_key=True, autoincrement=True)
    order_id = Column(db.Integer, nullable=False, comment='订单ID')
    status_before = Column(db.Enum(OrderStatusEnum), nullable=True, comment='变更前状态')
    status_after = Column(db.Enum(OrderStatusEnum), nullable=False, comment='变更后状态')
    operator = Column(db.String(50), nullable=True, comment='操作人')
    remark = Column(db.String(255), nullable=True, comment='备注')

    # 关系定义
    # order = relationship('Order', back_populates='order_logs')

    @classmethod
    def create(cls, order_id, status_before, status_after, operator, remark=None):
        """创建订单日志"""
        log = cls(
            order_id=order_id,
            status_before=status_before,
            status_after=status_after,
            operator=str(operator),
            remark=remark
        )
        from app.models import db
        db.session.add(log)
        return log

    def to_dict(self):
        """日志序列化方法"""
        return {
            "id": self.id,
            "order_id": self.order_id,
            "status_before": self.status_before.value if self.status_before is not None else None,
            "status_after": self.status_after.value if self.status_after is not None else None,
            "operator": self.operator,
            "remark": self.remark,
            "created_at": self.created_at.isoformat() if self.created_at is not None else None
        }
