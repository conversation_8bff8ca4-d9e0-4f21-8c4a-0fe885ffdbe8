from datetime import datetime, timezone
from enum import Enum
from decimal import Decimal

from sqlalchemy import Integer, Index, UniqueConstraint

from app.models.base import BaseModel, db


class AddressMappingTypeEnum(Enum):
    """地址映射类型枚举"""
    DEPOSIT = 'DEPOSIT'      # 充值地址
    WITHDRAW = 'WITHDRAW'    # 提现地址
    BOTH = 'BOTH'           # 充值和提现地址


class WalletGroup(BaseModel):
    __tablename__ = 'wallet_group'
    __table_args__ = (db.Index('idx_wallet_group_user_id', 'user_id'),)

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255))
    user_id = db.Column(Integer, nullable=False)
    device_id = db.Column(Integer, default=0)
    nonce = db.Column(db.String(255), nullable=False)


class WalletGenerated(BaseModel):
    __tablename__ = 'wallet_generated'
    __table_args__ = (db.Index('idx_wallet_generated_user_id', 'user_id'), db.Index('idx_wallet_generated_group_user_id', 'group_id', 'user_id'))

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, nullable=False)
    address = db.Column(db.String(255), nullable=False)
    encrypt_key = db.Column(db.String(2048), nullable=False)
    chain_code = db.Column(db.String(50), nullable=False)
    status = db.Column(db.Integer, default=0)
    group_id = db.Column(db.Integer, nullable=False)


class WalletLog(BaseModel):
    __tablename__ = 'wallet_log'
    __table_args__ = (db.Index('idx_wallet_log_wallet_id', 'wallet_id'),)

    id = db.Column(db.Integer, primary_key=True)
    wallet_id = db.Column(db.Integer, nullable=False)
    wallet_address = db.Column(db.String(255), nullable=False)
    data = db.Column(db.Text, nullable=False)
    chain_code = db.Column(db.String(50), nullable=False)
    tx_hash = db.Column(db.String(255), default='')
    op_time = db.Column(db.DateTime, default=datetime.now(timezone.utc))
    operation = db.Column(db.String(50), nullable=False)
    error = db.Column(db.String(255), default='')


class AddressMapping(BaseModel):
    """用户地址映射表 - 用于区块链监听"""
    __tablename__ = 'address_mappings'
    __table_args__ = (
        Index('idx_address_chain', 'address', 'chain_type'),
        Index('idx_user_chain', 'user_id', 'chain_type'),
        UniqueConstraint('address', 'chain_type', name='uk_address_chain'),
    )

    id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='映射ID')
    user_id = db.Column(db.Integer, nullable=False, comment='用户ID')
    address = db.Column(db.String(128), nullable=False, comment='区块链地址')
    chain_type = db.Column(db.String(20), nullable=False, comment='链类型(如eth,btc)')
    address_type = db.Column(db.Enum(AddressMappingTypeEnum),
                           server_default='BOTH', comment='地址类型')
    is_active = db.Column(db.Boolean, server_default='1', comment='是否激活')


    last_activity_block = db.Column(db.BigInteger, server_default='0', comment='最后活动区块')
    last_activity_time = db.Column(db.DateTime, nullable=True, comment='最后活动时间')
    total_deposits = db.Column(db.Integer, server_default='0', comment='总充值次数')
    total_withdrawals = db.Column(db.Integer, server_default='0', comment='总提现次数')
    total_deposit_amount = db.Column(db.DECIMAL(18, 8), server_default='0', comment='总充值金额')
    total_withdrawal_amount = db.Column(db.DECIMAL(18, 8), server_default='0', comment='总提现金额')

    def to_dict(self):
        """序列化为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'address': self.address,
            'chain_type': self.chain_type,
            'address_type': self.address_type.value if self.address_type else None,
            'is_active': self.is_active,
            'last_activity_block': self.last_activity_block,
            'last_activity_time': self.last_activity_time.isoformat() if self.last_activity_time else None,
            'total_deposits': self.total_deposits,
            'total_withdrawals': self.total_withdrawals,
            'total_deposit_amount': str(self.total_deposit_amount),
            'total_withdrawal_amount': str(self.total_withdrawal_amount),
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

    def update_activity(self, block_number: int, activity_type: str, amount: Decimal = None):
        """更新地址活动统计"""
        from datetime import datetime, timezone

        self.last_activity_block = max(self.last_activity_block or 0, block_number)
        self.last_activity_time = datetime.now(timezone.utc)

        if activity_type == 'DEPOSIT':
            self.total_deposits += 1
            if amount:
                self.total_deposit_amount += amount
        elif activity_type == 'WITHDRAW':
            self.total_withdrawals += 1
            if amount:
                self.total_withdrawal_amount += amount
