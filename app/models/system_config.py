"""系统配置模块"""
from app.models.base import BaseModel, db


class SystemApp(BaseModel):
    """系统应用配置模板"""
    __tablename__ = "system_apps"

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), unique=True, nullable=False)
    version = db.Column(db.String(50), nullable=False)
    download_url = db.Column(db.String(200), nullable=False)
    command = db.Column(db.String(100), nullable=False)
    args = db.Column(db.JSON)
    default_configs = db.Column(db.JSON)  # 默认配置模板
