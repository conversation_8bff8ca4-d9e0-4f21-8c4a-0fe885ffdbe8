from enum import Enum
from sqlalchemy import Column, PrimaryKeyConstraint
from app.models.base import BaseModel, db


class TransactionTypeEnum(Enum):
    DEPOSIT = 'DEPOSIT'
    WITHDRAW = 'WITHDRAW'
    TRADE = 'TRADE'
    MINING = 'MINING'
    TRANSFER = 'TRANSFER'
    SYSTEM_ADJUST = 'SYSTEM_ADJUST'


class TransactionStatusEnum(Enum):
    PENDING = 'PENDING'
    SUCCESS = 'SUCCESS'
    FAILED = 'FAILED'


class UserAsset(BaseModel):
    """用户资产模型"""
    __tablename__ = "user_asset"
    __table_args__ = (
        PrimaryKeyConstraint('user_id', 'asset_type_id'),
    )

    user_id = Column(db.Integer, comment='用户ID')
    asset_type_id = Column(db.Integer, comment='资产类型ID')
    available_balance = Column(db.DECIMAL(18, 8), nullable=False, server_default='0', comment='可用余额')
    frozen_balance = Column(db.DECIMAL(18, 8), nullable=False, server_default='0', comment='冻结余额')
    occupied_balance = Column(db.DECIMAL(18, 8), nullable=False, server_default='0', comment='占用余额')
    extend_field = Column(db.JSON, comment='扩展字段')


class AssetTransaction(BaseModel):
    """资产流水"""
    __tablename__ = "asset_transaction"

    id = Column(db.Integer, primary_key=True, autoincrement=True, comment='交易ID')
    user_id = Column(db.Integer, nullable=False, comment='用户ID')
    asset_type_id = Column(db.Integer, nullable=False, comment='资产类型ID')
    amount = Column(db.DECIMAL(18, 8), nullable=False, comment='交易金额')
    balance_before = Column(db.DECIMAL(18, 8), nullable=False, comment='交易前余额')
    balance_after = Column(db.DECIMAL(18, 8), nullable=False, comment='交易后余额')
    transaction_type = Column(db.Enum(TransactionTypeEnum), nullable=False, comment='交易类型')
    reference_id = Column(db.String(64), nullable=True, comment='关联ID')
    status = Column(db.Enum(TransactionStatusEnum), nullable=False, server_default='SUCCESS', comment='交易状态')
    extend_field = Column(db.JSON, comment='扩展字段')


class AssetFreezeLog(BaseModel):
    """资产冻结解冻记录"""
    __tablename__ = "asset_freeze_logs"

    id = Column(db.Integer, primary_key=True, autoincrement=True)
    user_id = Column(db.Integer, index=True, nullable=False, comment='用户ID')
    asset_type_id = Column(db.Integer, index=True, nullable=False, comment='资产类型ID')
    operation_type = Column(db.Enum('FREEZE', 'UNFREEZE', 'OCCUPY', 'RELEASE'),
                            nullable=False, comment='操作类型')
    amount = Column(db.DECIMAL(18, 8), nullable=False, comment='操作金额')
    related_order_id = Column(db.String(64), comment='关联订单ID')
    remark = Column(db.String(255), comment='操作备注')
