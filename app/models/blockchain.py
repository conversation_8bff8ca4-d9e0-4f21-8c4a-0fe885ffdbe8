from enum import Enum

from sqlalchemy import ForeignKey
from sqlalchemy.orm import relationship

from app.models.base import BaseModel, db


class ChainProtocol(str, Enum):
    """链协议类型"""
    EVM = "EVM"  # EVM
    SOL = "SOL"  # SOL


class Blockchain(BaseModel):
    """区块链类型模型（替代原枚举）"""
    __tablename__ = "blockchains"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    # 链协议类型 - EVM/SOL
    chain_protocol = db.Column(db.Enum(ChainProtocol), nullable=False, comment="链协议类型")
    # 链名称（如 "Ethereum"）
    chain_name = db.Column(db.String(255), nullable=False, comment="链名称")
    # 链代码（如 "ETH"，唯一标识）
    chain_code = db.Column(db.String(50), nullable=False, unique=True, comment="链代码")
    # BIP44 BIP类型（如 60）
    coin_type = db.Column(db.Integer, nullable=False, comment="BIP类型")
    # 删除时间
    deleted_at = db.Column(db.DateTime, nullable=True)
    # 扩展字段
    extend_field = db.Column(db.JSON, comment="扩展字段")
    tokens = relationship(
        "Token", back_populates="blockchain", foreign_keys="Token.chain_id", cascade="all, delete-orphan"
    )

    def to_dict(self):
        # 从 extend_field 中提取业务字段
        rpc_url = None
        explorer = None
        if self.extend_field:
            rpc_url = self.extend_field.get("rpc_url")
            explorer = self.extend_field.get("explorer")

        return {
            "id": self.id,
            "chain_name": self.chain_name,
            "chain_code": self.chain_code,
            "coin_type": self.coin_type,
            "rpc_url": rpc_url,
            "explorer": explorer,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }


class TokenType(str, Enum):
    """代币类型枚举（与数据库保持一致）"""
    NATIVE = "NATIVE"  # 原生代币
    ERC20 = "ERC20"  # ERC20标准代币
    SPL = "SPL"  # Solana SPL代币


class Token(BaseModel):
    """代币模型（替代原枚举）"""
    __tablename__ = "tokens"

    # 唯一标识符（如 "eth_native"）
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    # 唯一标识符（如 "eth_native"）
    token_code = db.Column(db.String(255), nullable=False, unique=True)
    # 代币符号（如 "ETH"）
    token_symbol = db.Column(db.String(50), nullable=False)
    # 代币名称（如 "Ethereum"）
    token_name = db.Column(db.String(255), nullable=False)
    # 所属区块链（外键关联）
    chain_id = db.Column(db.Integer, ForeignKey("blockchains.id"), nullable=False)
    # 小数位数（如 18）
    decimals = db.Column(db.Integer, nullable=False)
    # 代币类型（使用枚举）
    token_type = db.Column(db.Enum(TokenType), nullable=False)
    # 合约地址（非原生代币必填）
    contract_address = db.Column(db.String(255))

    blockchain = relationship("Blockchain", back_populates="tokens")
    asset_types = relationship(
        "AssetType", back_populates="token", foreign_keys="AssetType.token_id", cascade="all, delete-orphan"
    )

    def to_dict(self):
        return {
            "id": self.id,
            "token_code": self.token_code,
            "token_symbol": self.token_symbol,
            "token_name": self.token_name,
            "chain_id": self.chain_id,
            "decimals": self.decimals,
            "token_type": self.token_type.value if self.token_type else None,
            "contract_address": self.contract_address,
            "chain_code": self.blockchain.chain_code,
        }


class AssetTypeEnum(str, Enum):
    POINTS = 'POINTS'
    TOKEN = 'TOKEN'


class AssetType(BaseModel):
    """资产类型模型"""
    __tablename__ = "asset_types"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='资产类型ID')
    name = db.Column(db.String(50), nullable=False, comment='资产名称')
    type = db.Column(db.Enum(AssetTypeEnum), nullable=False, comment='资产类型：积分、加密货币')
    decimals = db.Column(db.Integer, nullable=False)
    project_id = db.Column(db.Integer, ForeignKey("projects.id"), nullable=True, comment='项目ID（积分类型必填）')
    token_id = db.Column(db.Integer, ForeignKey("tokens.id"), nullable=True, comment='加密币ID（加密货币类型必填）')
    chain_type = db.Column(db.String(20), nullable=True, comment='链类型（加密货币类型必填）')
    status = db.Column(db.SmallInteger, server_default='1', comment='是否启用')

    trading_pair_base_asset = relationship(
        "TradingPair", back_populates="base_asset", foreign_keys="TradingPair.base_asset_id",
        cascade="all, delete-orphan"
    )
    trading_pair_quote_asset = relationship(
        "TradingPair", back_populates="quote_asset", foreign_keys="TradingPair.quote_asset_id",
        cascade="all, delete-orphan"
    )
    token = relationship("Token", foreign_keys=[token_id], back_populates="asset_types")
    project = relationship("Project", foreign_keys=[project_id], back_populates="asset_types")

    def to_dict(self):
        """资产类型序列化方法"""
        result = {
            "id": self.id,
            "name": self.name,
            "type": self.type.name,  # 枚举转字符串
            "project_id": self.project_id,
            "token_id": self.token_id,
            "chain_type": self.chain_type,
            "decimals": self.decimals,
            "status": self.status,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

        # 如果有关联的代币，添加代币信息
        if self.token:
            result["token_name"] = self.token.token_name
            result["token_symbol"] = self.token.token_symbol
            if self.token.blockchain:
                result["blockchain_name"] = self.token.blockchain.chain_name

        return result


class TradingPairStatusEnum(str, Enum):
    ACTIVE = 'ACTIVE'
    INACTIVE = 'INACTIVE'


class TradingPair(BaseModel):
    """交易对模型"""
    __tablename__ = "trading_pairs"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    base_asset_id = db.Column(db.Integer, db.ForeignKey('asset_types.id'), nullable=False, comment='基础资产ID')
    quote_asset_id = db.Column(db.Integer, db.ForeignKey('asset_types.id'), nullable=False, comment='计价资产ID')
    pair_name = db.Column(db.String(50), nullable=False, unique=True, comment='交易对名称(如GRASS/USDT)')
    min_price = db.Column(db.DECIMAL(18, 8), nullable=False, comment='最小价格')
    max_price = db.Column(db.DECIMAL(18, 8), nullable=False, comment='最大价格')
    price_precision = db.Column(db.SmallInteger, nullable=False, comment='价格精度')
    amount_precision = db.Column(db.SmallInteger, nullable=False, comment='数量精度')
    status = db.Column(db.Enum(TradingPairStatusEnum), server_default='ACTIVE', comment='状态')

    # 关系定义
    base_asset = relationship('AssetType', foreign_keys=[base_asset_id], back_populates="trading_pair_base_asset")
    quote_asset = relationship('AssetType', foreign_keys=[quote_asset_id], back_populates="trading_pair_quote_asset")
    orders = relationship('Order', back_populates='trading_pair', cascade='all, delete-orphan')

    def to_dict(self):
        """交易对序列化方法，包含完整的资产信息"""
        result = {
            "id": self.id,
            "base_asset_id": self.base_asset_id,
            "quote_asset_id": self.quote_asset_id,
            "pair_name": self.pair_name,
            "min_price": str(self.min_price),  # DECIMAL转字符串避免精度丢失
            "max_price": str(self.max_price),
            "price_precision": self.price_precision,
            "amount_precision": self.amount_precision,
            "status": self.status.name if self.status else None,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }

        # 添加基础资产（来源资产）详细信息
        if self.base_asset:
            base_asset_info = {
                "id": self.base_asset.id,
                "name": self.base_asset.name,
                "type": self.base_asset.type.name,
                "decimals": self.base_asset.decimals
            }

            # 如果是加密货币，添加区块链信息
            if self.base_asset.type == AssetTypeEnum.TOKEN and self.base_asset.token:
                if self.base_asset.token.blockchain:
                    base_asset_info["blockchain_name"] = self.base_asset.token.blockchain.chain_name
                    base_asset_info["blockchain_code"] = self.base_asset.token.blockchain.chain_code
                base_asset_info["token_symbol"] = self.base_asset.token.token_symbol
                base_asset_info["contract_address"] = self.base_asset.token.contract_address

            # 如果是积分，添加项目信息（暂时跳过，等项目关系完善后添加）
            if self.base_asset.type == AssetTypeEnum.POINTS:
                base_asset_info["project_id"] = self.base_asset.project_id

            result["base_asset"] = base_asset_info

        # 添加计价资产（目标资产）详细信息
        if self.quote_asset:
            quote_asset_info = {
                "id": self.quote_asset.id,
                "name": self.quote_asset.name,
                "type": self.quote_asset.type.name,
                "decimals": self.quote_asset.decimals
            }

            # 如果是加密货币，添加区块链信息
            if self.quote_asset.type == AssetTypeEnum.TOKEN and self.quote_asset.token:
                if self.quote_asset.token.blockchain:
                    quote_asset_info["blockchain_name"] = self.quote_asset.token.blockchain.chain_name
                    quote_asset_info["blockchain_code"] = self.quote_asset.token.blockchain.chain_code
                quote_asset_info["token_symbol"] = self.quote_asset.token.token_symbol
                quote_asset_info["contract_address"] = self.quote_asset.token.contract_address

            # 如果是积分，添加项目信息（暂时跳过，等项目关系完善后添加）
            if self.quote_asset.type == AssetTypeEnum.POINTS:
                quote_asset_info["project_id"] = self.quote_asset.project_id

            result["quote_asset"] = quote_asset_info

        return result
