"""用户模型"""
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Inte<PERSON>, <PERSON>, ForeignKey
from sqlalchemy.orm import relationship
from werkzeug.security import check_password_hash, generate_password_hash

from app.models.base import BaseModel


class User(BaseModel):
    """用户模型"""
    __tablename__ = "users"

    id = Column(Integer, primary_key=True)
    username = Column(String(64), unique=True, nullable=False)
    email = Column(String(120), unique=True, nullable=False)
    password_hash = Column(String(256))
    role = Column(String(32), default="user")  # admin or user
    is_active = Column(Boolean, default=True)
    invite_code = Column(String(10), unique=True, index=True)
    invited_by = Column(Integer, ForeignKey('users.id'))

    # 关联关系
    devices = relationship("Device", back_populates="owner", cascade="all, delete-orphan")
    point_records = relationship('PointRecord', back_populates='user', foreign_keys='PointRecord.user_id')
    invite_records = relationship('PointRecord', back_populates='inviter', foreign_keys='PointRecord.invitee_id')
    invited_records = relationship('PointRecord', back_populates='invitee', foreign_keys='PointRecord.invitee_id', overlaps="invite_records")

    # 建立与 UserNotify 的一对多关系
    user_notifies = relationship('UserNotify', back_populates='user', cascade='all, delete-orphan')
    orders = relationship('Order', back_populates='user', cascade='all, delete-orphan')
    __mapper_args__ = {
        "confirm_deleted_rows": False
    }

    @property
    def is_admin(self):
        """是否是管理员"""
        return self.role == "admin"

    def has_device_permission(self, device_id: int, permission: str = "read") -> bool:
        """检查用户是否有设备权限
        Args:
            device_id: 设备ID
            permission: 权限类型，可选值：read, write
        Returns:
            bool: 是否有权限
        """
        if self.is_admin:
            return True
        # 检查是否是设备所有者
        device = next((d for d in self.devices if d.id == device_id), None)
        return device is not None

    def has_project_permission(self, project_id: int, permission: str = "read") -> bool:
        """检查用户是否有项目权限
        Args:
            project_id: 项目ID
            permission: 权限类型，可选值：read, write
        Returns:
            bool: 是否有权限
        """
        if self.is_admin:
            return True
        # 检查用户是否有关联的设备使用了该项目
        for device in self.devices:
            for device_project in device.device_projects:
                if device_project.project_id == project_id:
                    return True
        return False

    def set_password(self, password):
        """设置密码"""
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        """检查密码"""
        return check_password_hash(self.password_hash, password)

    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "username": self.username,
            "email": self.email,
            "role": self.role,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }
