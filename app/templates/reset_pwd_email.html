{#<!DOCTYPE html>#}
{#<html>#}
{#<head>#}
{#    <style>#}
{#        .container { #}
{#            max-width: 600px; #}
{#            margin: 20px auto; #}
{#            padding: 30px; #}
{#            border: 1px solid #eee; #}
{#            background-color: #f9f9f9;#}
{#            font-family: Arial, sans-serif;#}
{#        }#}
{#        .heading {#}
{#            color: #333;#}
{#            font-size: 24px;#}
{#            text-align: center;#}
{#        }#}
{#        .reset-link {#}
{#            font-size: 20px;#}
{#            color: #1890ff;#}
{#            text-align: center;#}
{#            display: block;#}
{#            margin: 30px 0;#}
{#            padding: 12px 20px;#}
{#            text-decoration: none;#}
{#            border: 2px solid #1890ff;#}
{#            border-radius: 4px;#}
{#        }#}
{#        .reset-link:hover {#}
{#            background-color: #1890ff;#}
{#            color: white;#}
{#        }#}
{#        .footer { #}
{#            margin-top: 30px; #}
{#            text-align: center;#}
{#            font-size: 12px;#}
{#            color: #999;#}
{#        }#}
{#    </style>#}
{#</head>#}
{#<body>#}
{#    <div class="container">#}
{#        <h2 class="heading">密码重置请求</h2>#}
{#        <p>您好，</p>#}
{#        <p>我们收到您重置密码的请求。如果是您本人操作，请点击以下链接来重置您的密码：</p>#}
{#        #}
{#        <!-- 重置密码链接 -->#}
{#        <a href="{{ reset_url }}" class="reset-link">点击这里重置您的密码</a>#}
{#        #}
{#        <p>此链接将在 1 小时内过期。如果您没有请求重置密码，请忽略此邮件。</p>#}
{#        #}
{#        <div class="footer">#}
{#            <p>© Box System</p>#}
{#        </div>#}
{#    </div>#}
{#</body>#}
{#</html>#}
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>Reset Password</title>
    <style>
        /* 基础设置，保证在各邮件客户端中有相对一致的表现 */
        body {
            margin: 0;
            padding: 0;
            background-color: #ffffff; /* 邮件整体背景色 */
            font-family: Arial, sans-serif;
        }
        .container {
            max-width: 600px;
            margin: 40px auto;
            padding: 40px;
            border: 1px solid #eee;
            background-color: #fff;
            text-align: center; /* 让内容居中 */
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 30px;
        }
        .greeting {
            font-size: 20px;
            margin-bottom: 20px;
            color: #333;
        }
        .message {
            font-size: 16px;
            color: #666;
            line-height: 1.5;
            margin-bottom: 30px;
        }
        .reset-button {
            display: inline-block;
            background-color: #000; /* 按钮背景色（黑色） */
            color: #fff;           /* 按钮文字颜色（白色） */
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 4px;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 40px;
        }
        .footer {
            font-size: 12px;
            color: #999;
        }
        /* 针对移动端的自适应 */
        @media only screen and (max-width: 600px) {
            .container {
                width: 90%;
                padding: 20px;
            }
            .greeting {
                font-size: 18px;
            }
            .message {
                font-size: 14px;
            }
            .reset-button {
                font-size: 14px;
                padding: 10px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 顶部Logo/标题 -->
        <div class="logo">UBI network</div>
        
        <!-- 问候语 -->
        <div class="greeting">Hi</div>
        
        <!-- 文本提示 -->
        <div class="message">
            A request has been made to reset your password. If you made this request, 
            please click on the button below.
        </div>
        
        <!-- 重置密码按钮 -->
        <a href="{{ reset_url }}" class="reset-button">RESET PASSWORD</a>
        
        <!-- 页脚 -->
        <div class="footer">
            © 2025 by the love from UBI Net work
        </div>
    </div>
</body>
</html>
