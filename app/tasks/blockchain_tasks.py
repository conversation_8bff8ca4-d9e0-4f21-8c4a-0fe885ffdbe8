"""
区块链监听和结算相关的 Celery 任务
将区块链监听服务融合到现有的 Celery 任务系统中
"""

import asyncio
from datetime import datetime, timezone, timedelta
from typing import Dict, Any

from celery import shared_task
from celery.utils.log import get_task_logger

from app.models import db
from app.models.blockchain import Blockchain
from app.models.blockchain_sync import BlockchainSyncStatus, TransactionSyncLog
from app.models.settlement import UserSettlement, SettlementStatusEnum
from app.models.wallet import AddressMapping
from app.services.blockchain_listener_service import BlockchainListenerService

logger = get_task_logger(__name__)


@shared_task(bind=True, max_retries=3)
def process_blockchain_sync(self, chain_id: int) -> Dict[str, Any]:
    """
    处理单个区块链的同步任务
    
    Args:
        chain_id: 区块链ID
        
    Returns:
        Dict[str, Any]: 处理结果
    """
    try:
        chain = Blockchain.query.get(chain_id)
        if not chain:
            return {"status": "error", "message": f"区块链 {chain_id} 不存在"}
        
        logger.info(f"开始同步 {chain.chain_name} (ID: {chain_id})")
        
        # 获取监听服务实例
        listener_service = BlockchainListenerService()
        
        # 获取区块链策略
        strategy = listener_service.factory.get_strategy(chain)
        
        # 获取最新区块号
        latest_block = asyncio.run(listener_service._get_latest_block(strategy))
        
        # 获取上次处理的区块号
        last_processed_block = listener_service._get_last_processed_block(chain.id)
        
        processed_blocks = 0
        processed_transactions = 0
        
        # 处理新区块（限制每次处理的区块数量，避免任务超时）
        max_blocks_per_task = 50
        if latest_block > last_processed_block:
            end_block = min(latest_block, last_processed_block + max_blocks_per_task)
            
            logger.info(f"处理 {chain.chain_name} 区块 {last_processed_block + 1} - {end_block}")
            
            # 处理区块
            for block_number in range(last_processed_block + 1, end_block + 1):
                try:
                    transactions = asyncio.run(
                        listener_service._get_block_transactions(strategy, block_number)
                    )
                    
                    for tx in transactions:
                        asyncio.run(listener_service._process_transaction(chain, tx))
                        processed_transactions += 1
                    
                    processed_blocks += 1
                    
                    # 每处理10个区块更新一次进度
                    if processed_blocks % 10 == 0:
                        listener_service._update_last_processed_block(chain.id, block_number)
                        
                except Exception as e:
                    logger.error(f"处理区块 {block_number} 失败: {e}")
                    continue
            
            # 更新最后处理的区块号
            listener_service._update_last_processed_block(chain.id, end_block)
            
            # 如果还有更多区块需要处理，调度下一个任务
            if end_block < latest_block:
                process_blockchain_sync.apply_async(args=[chain_id], countdown=5)
        
        return {
            "status": "success",
            "chain_id": chain_id,
            "chain_name": chain.chain_name,
            "latest_block": latest_block,
            "last_processed_block": last_processed_block,
            "processed_blocks": processed_blocks,
            "processed_transactions": processed_transactions,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
    except Exception as e:
        logger.error(f"区块链同步任务失败 (chain_id: {chain_id}): {e}")
        
        # 记录错误到数据库
        try:
            BlockchainSyncStatus.record_error(chain_id, str(e))
        except:
            pass
        
        # 重试机制
        if self.request.retries < self.max_retries:
            logger.info(f"重试区块链同步任务 (chain_id: {chain_id}), 重试次数: {self.request.retries + 1}")
            raise self.retry(countdown=60 * (self.request.retries + 1))  # 指数退避
        
        return {
            "status": "error",
            "chain_id": chain_id,
            "error": str(e),
            "retries": self.request.retries
        }


@shared_task
def sync_all_blockchains() -> Dict[str, Any]:
    """
    同步所有活跃的区块链
    这是一个调度任务，会为每个链创建独立的处理任务
    """
    try:
        # 获取所有活跃的区块链
        active_chains = Blockchain.query.filter_by(is_active=True).all()
        
        scheduled_tasks = []
        
        for chain in active_chains:
            # 为每个链调度一个独立的同步任务
            task = process_blockchain_sync.apply_async(args=[chain.id])
            scheduled_tasks.append({
                "chain_id": chain.id,
                "chain_name": chain.chain_name,
                "task_id": task.id
            })
            
            logger.info(f"已调度 {chain.chain_name} 同步任务: {task.id}")
        
        return {
            "status": "success",
            "scheduled_tasks": scheduled_tasks,
            "total_chains": len(active_chains),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
    except Exception as e:
        logger.error(f"调度区块链同步任务失败: {e}")
        return {
            "status": "error",
            "error": str(e)
        }


@shared_task
def process_pending_settlements() -> Dict[str, Any]:
    """
    处理待处理的结算任务
    """
    try:
        # 查找所有待处理的结算
        pending_settlements = UserSettlement.query.filter_by(
            status=SettlementStatusEnum.PROCESSING
        ).all()
        
        processed_count = 0
        error_count = 0
        
        for settlement in pending_settlements:
            try:
                # 检查结算是否超时（例如超过24小时）
                if settlement.created_at < datetime.now(timezone.utc) - timedelta(hours=24):
                    logger.warning(f"结算 {settlement.id} 超时，标记为失败")
                    settlement.status = SettlementStatusEnum.FAILED
                    settlement.error_message = "结算超时"
                    db.session.add(settlement)
                    error_count += 1
                    continue
                
                # 这里可以添加其他结算处理逻辑
                # 例如检查区块链交易状态、更新结算进度等
                
                processed_count += 1
                
            except Exception as e:
                logger.error(f"处理结算 {settlement.id} 失败: {e}")
                error_count += 1
        
        db.session.commit()
        
        return {
            "status": "success",
            "processed_count": processed_count,
            "error_count": error_count,
            "total_pending": len(pending_settlements),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
    except Exception as e:
        logger.error(f"处理结算任务失败: {e}")
        db.session.rollback()
        return {
            "status": "error",
            "error": str(e)
        }


@shared_task
def cleanup_old_sync_logs() -> Dict[str, Any]:
    """
    清理旧的同步日志
    """
    try:
        # 删除30天前的同步日志
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=30)
        
        deleted_count = TransactionSyncLog.query.filter(
            TransactionSyncLog.sync_time < cutoff_date
        ).delete()
        
        db.session.commit()
        
        logger.info(f"清理了 {deleted_count} 条旧的同步日志")
        
        return {
            "status": "success",
            "deleted_count": deleted_count,
            "cutoff_date": cutoff_date.isoformat()
        }
        
    except Exception as e:
        logger.error(f"清理同步日志失败: {e}")
        db.session.rollback()
        return {
            "status": "error",
            "error": str(e)
        }


@shared_task
def update_address_statistics() -> Dict[str, Any]:
    """
    更新地址统计信息
    """
    try:
        # 获取所有活跃的地址映射
        active_addresses = AddressMapping.query.filter_by(is_active=True).all()
        
        updated_count = 0
        
        for address_mapping in active_addresses:
            try:
                # 统计该地址的交易数量
                deposit_count = TransactionSyncLog.query.filter_by(
                    to_address=address_mapping.address,
                    sync_status='PROCESSED'
                ).count()
                
                withdrawal_count = TransactionSyncLog.query.filter_by(
                    from_address=address_mapping.address,
                    sync_status='PROCESSED'
                ).count()
                
                # 更新统计信息
                if deposit_count != address_mapping.total_deposits or \
                   withdrawal_count != address_mapping.total_withdrawals:
                    
                    address_mapping.total_deposits = deposit_count
                    address_mapping.total_withdrawals = withdrawal_count
                    db.session.add(address_mapping)
                    updated_count += 1
                
            except Exception as e:
                logger.error(f"更新地址 {address_mapping.address} 统计失败: {e}")
                continue
        
        db.session.commit()
        
        return {
            "status": "success",
            "total_addresses": len(active_addresses),
            "updated_count": updated_count,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
    except Exception as e:
        logger.error(f"更新地址统计失败: {e}")
        db.session.rollback()
        return {
            "status": "error",
            "error": str(e)
        }
