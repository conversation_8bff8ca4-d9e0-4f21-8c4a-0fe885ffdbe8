from decimal import Decimal
from datetime import datetime, timezone, timedelta

from celery import shared_task
from celery.utils.log import get_task_logger

from app.enums.biz_enums import FixtureProjectName
from app.models.base import db
from app.models.device import Device
from app.models.project import Project
from app.models.blockchain import AssetType, AssetTypeEnum
from app.services.asset_service import AssetService

logger = get_task_logger(__name__)

# 定义lighter项目名称常量


@shared_task
def award_lighter_device_points():
    """
    为lighter项目的设备分配积分
    
    规则：
    - 每个机器/24小时（累计且不需连续累计）/增加0.01积分
    - 通过记录上次获得积分的时间来判断是否已经过了24小时
    """
    logger.info(f"开始为{FixtureProjectName.LIGHTER_PROJECT_NAME}项目的设备分配积分")
    
    try:
        # 1. 获取lighter项目
        lighter_project = Project.query.filter_by(name=FixtureProjectName.LIGHTER_PROJECT_NAME).first()
        if not lighter_project:
            logger.error(f"{FixtureProjectName.LIGHTER_PROJECT_NAME}项目不存在，请先创建")
            return {
                "status": "error",
                "message": f"{FixtureProjectName.LIGHTER_PROJECT_NAME}项目不存在"
            }
        
        # 2. 获取所有设备
        devices = Device.query.filter(Device.owner_id.isnot(None)).all()
        logger.info(f"找到{len(devices)}个有所有者的设备")
        
        # 3. 遍历设备，为符合条件的设备分配积分
        awarded_count = 0
        now = datetime.now(timezone.utc)
        
        for device in devices:
            # 获取设备上次获得lighter积分的时间
            # 使用设备的system_app_configs字段存储上次获得积分的时间
            last_award_time = None
            if device.system_app_configs and 'lighter_last_award_time' in device.system_app_configs:
                try:
                    last_award_time_str = device.system_app_configs['lighter_last_award_time']
                    last_award_time = datetime.fromisoformat(last_award_time_str)
                    # 确保时区信息一致
                    if last_award_time.tzinfo is None:
                        last_award_time = last_award_time.replace(tzinfo=timezone.utc)
                except (ValueError, TypeError) as e:
                    logger.warning(f"设备{device.id}的上次积分时间格式错误: {e}")
                    last_award_time = None
            
            # 如果没有上次获得积分的时间记录，或者距离上次获得积分已经过了24小时
            if last_award_time is None or (now - last_award_time) >= timedelta(hours=24):
                # 为设备所有者分配积分
                if device.owner_id:
                    try:
                        # 获取lighter项目的积分资产类型
                        asset_type = AssetType.query.filter_by(
                            project_id=lighter_project.id,
                            type=AssetTypeEnum.POINTS
                        ).first()
                        
                        if not asset_type:
                            logger.error(f"{FixtureProjectName.LIGHTER_PROJECT_NAME}项目的积分资产类型不存在")
                            continue
                        
                        # 直接使用AssetService添加积分
                        AssetService.add_or_create_user_asset(
                            user_id=device.owner_id,
                            asset_type_id=asset_type.id,
                            amount=Decimal('0.01'),  # 每24小时增加0.01积分
                            remark=f"lighter项目设备积分",
                            reference_id=str(device.id)
                        )
                        
                        # 更新设备的上次获得积分时间
                        if not device.system_app_configs:
                            device.system_app_configs = {}
                        device.system_app_configs['lighter_last_award_time'] = now.isoformat()
                        db.session.add(device)
                        awarded_count += 1
                        logger.info(f"成功为设备{device.id}的所有者(用户ID:{device.owner_id})分配0.01积分")
                    except Exception as e:
                        logger.error(f"为设备{device.id}的所有者分配积分失败: {str(e)}")
        
        # 提交所有更改
        db.session.commit()
        
        logger.info(f"成功为{awarded_count}个设备分配积分")
        return {
            "status": "success",
            "awarded_count": awarded_count,
            "total_devices": len(devices)
        }
    
    except Exception as e:
        logger.exception(f"分配{FixtureProjectName.LIGHTER_PROJECT_NAME}项目积分时发生错误: {e}")
        db.session.rollback()
        return {
            "status": "error",
            "message": str(e)
        }