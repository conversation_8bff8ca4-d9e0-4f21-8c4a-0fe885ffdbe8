"""Metrics related tasks."""

from datetime import datetime, timedelta, timezone
from typing import Any, Dict

from app.celery_app import celery
from app.models.base import db
from app.services.metrics_service import metrics_service
from scripts.generate_snapshot import generate_all_yesterday_snapshot


@celery.task(name="update_yesterday_metrics")
def update_yesterday_metrics() -> Dict[str, Any]:
    """
    Update yesterday's metrics for all services.

    This task runs once per day to find and update any snapshots that 
    might have missing or incorrect yesterday metrics.
    
    This is a backup task to ensure data integrity - the generate_snapshot function 
    should correctly set yesterday metrics in most cases.

    Returns:
        Dict[str, Any]: A dictionary containing the result of the update operation.
    """
    try:
        generate_all_yesterday_snapshot()

        return {
            "status": "success"
        }

    except Exception as e:
        db.session.rollback()
        return {
            "status": "error",
            "error": str(e)
        }

@celery.task(name="aggregate_service_metrics")
def aggregate_service_metrics() -> Dict[str, Any]:
    """
    从 ServiceMetricsDetail 聚合数据到 ServiceMetrics 表

    此任务通过 Celery 定时执行，对应 CLI 命令 flask aggregate
    同时生成服务指标快照

    Returns:
        Dict[str, Any]: 聚合结果信息
    """
    try:
        # 聚合服务指标
        count = metrics_service.aggregate_metrics()

        # 生成服务指标快照
        # from scripts.generate_snapshot import generate_snapshot
        # generate_snapshot()

        return {
            "status": "success",
            "aggregated_count": count,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }
