from typing import Dict, List, Tuple

from celery import shared_task

from app.models.base import db
from app.models.device import Device
from app.models.device_project import DeviceProject
from app.models.project import Project
from app.services.project_service import ProjectService


@shared_task
def validate_device_projects(user_id: int) -> Dict:
    """
    Validate and create default device project records for a user's devices.
    
    Args:
        user_id: The ID of the user to validate
        
    Returns:
        Dict: Result of the validation
    """
    try:
        # Get all devices owned by the user
        devices = Device.query.filter_by(owner_id=user_id).all()
        if not devices:
            return {"success": True, "message": "No devices found for this user"}
        
        # Get all enabled projects
        enabled_projects = Project.query.filter_by(status=1).all()  # 1 = enabled
        if not enabled_projects:
            return {"success": True, "message": "No enabled projects found"}
        
        # Track created records
        created_count = 0
        
        # For each device and project combination
        for device in devices:
            for project in enabled_projects:
                # Check if device_project record exists
                existing = DeviceProject.query.filter_by(
                    device_id=device.id,
                    project_id=project.id
                ).first()
                
                # If not, create it with default values
                if not existing:
                    new_record = DeviceProject(
                        device_id=device.id,
                        project_id=project.id
                    )
                    db.session.add(new_record)
                    created_count += 1
        
        # Commit changes if any records were created
        if created_count > 0:
            db.session.commit()
            return {
                "success": True,
                "message": f"Created {created_count} device project records"
            }
        else:
            return {
                "success": True,
                "message": "All device project records already exist"
            }
            
    except Exception as e:
        # Rollback in case of error
        db.session.rollback()
        return {
            "success": False,
            "message": f"Error validating device projects: {str(e)}"
        } 