from celery import shared_task
from datetime import datetime, timezone
from app.models.points import PointRecord
from app.services.points_service import PointsService
from app.models.base import db
from app.models.device import Device


@shared_task
def process_invite_records():
    """
    定时任务说明：
    1. 查找所有points为0且record_type为'invite'且invitee_id不为空的PointRecord记录。
    2. 对于每条记录，使用invitee_id作为owner_id检索Device数据模型。
    3. 遍历这些设备的device_projects关联，检查是否存在state=='running'的项目。
    4. 如果至少找到一个，则调用PointRecord.update_invite_points(inviter_id, invitee_id)更新邀请积分。
    """
    # 筛选符合条件的邀请记录
    invite_records = PointRecord.query.filter(
        PointRecord.points == 0,
        PointRecord.record_type == 'invite',
        PointRecord.invitee_id.isnot(None)
    ).all()

    for record in invite_records:
        owner_id = record.invitee_id
        # 根据invitee_id作为owner_id查找设备
        devices = Device.query.filter_by(owner_id=owner_id).all()

        # 检查这些设备中是否有运行中的设备项目
        has_running_project = any(
            dp for device in devices for dp in device.device_projects if dp.state == 'running'
        )

        if has_running_project:
            # 调用静态方法更新邀请积分：传入邀请者ID(record.user_id)和被邀请者ID(record.invitee_id)
            result = PointsService.update_invite_points(record.user_id, record.invitee_id)
            print(f"更新邀请积分结果: {result}")

    try:
        db.session.commit()
    except Exception as e:
        print(f"Error committing changes: {e}")
        db.session.rollback()
        raise
