from celery.utils.log import get_task_logger

from app.celery_app import celery

from app.models import db
from app.models.notify import Notify, UserNotify
from app.models.user import User

logger = get_task_logger(__name__)


@celery.task
def process_notification_queue(notify_id: int):
    """Process notification queue and create user notifications
    
    Args:
        notify_id: The ID of the notification to process
    """

    # Get the notification
    notify = Notify.query.get(notify_id)
    if not notify:
        logger.error(f"Notification {notify_id} not found")
        return
    user_ids = notify.extend_field.get('user_ids')
    if user_ids:
        batch_process_user_notify.delay(notify_id, user_ids)
    else:
        # Create user notifications in batches
        page = 0
        per_page = 100
        while True:
            # If no specific users provided, get all active users
            users = User.query.filter_by(is_active=True).offset(page * per_page).limit(per_page).all()
            if not users:
                break
            user_ids = [user.id for user in users]
            batch_process_user_notify.delay(notify_id, user_ids)
            page += 1


@celery.task
def batch_process_user_notify(notify_id, user_ids):
    user_notifies = [
        UserNotify(
            user_id=user_id,
            notify_id=notify_id,
            is_read=False
        )
        for user_id in user_ids
    ]
    db.session.bulk_save_objects(user_notifies)
    db.session.commit()
