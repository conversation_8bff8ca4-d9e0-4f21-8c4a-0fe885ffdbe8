"""Flask 应用工厂模块"""
import os

from flask import Flask
from flask_cors import CORS
from flask_jwt_extended import JWTManager
from flask_babel import Babel

from app.models.base import init_db
from app.utils.jwt_handlers import register_jwt_error_handlers
from .config import config
from .celery_app import create_celery_app

# 导入任务模块
import app.tasks.monitoring
from app.utils.i18n_helper import get_locale, translate_response_message
from .middlewares.request_metrics_hooks import metrics_request_hook

# 创建扩展实例
jwt = JWTManager()

celery = None

def create_app(config_name="default"):
    """应用工厂函数

    Args:
        config_name: 配置名称，默认为 'default'

    Returns:
        Flask 应用实例
    """
    app = Flask(__name__)

    # 加载配置
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)

    # 初始化扩展
    CORS(app,
         resources={r"/api/*": {"origins": "*"}},
         supports_credentials=True,
         allow_headers=["Content-Type", "Authorization"],
         methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"])

    init_db(app)
    jwt.init_app(app)

    # 注册 JWT 错误处理器
    register_jwt_error_handlers(jwt)

    # 初始化 babel
    babel = Babel()
    babel.init_app(app, locale_selector=get_locale)

    # 注册主API蓝图
    from .api import api_bp
    app.register_blueprint(api_bp, url_prefix="/api")

    app.before_request(metrics_request_hook)

    # 添加响应拦截器，对message进行国际化
    @app.after_request
    def translate_response(response):
        return translate_response_message(response)

    # 注册命令
    from .commands import register_commands
    register_commands(app)

    # 启动区块链监听服务（在应用启动时）
    # if os.environ.get('WERKZEUG_RUN_MAIN') != 'true':
    #     from app.services.blockchain_listener_manager import start_blockchain_listeners_background
    #     start_blockchain_listeners_background(app)  # 传递 app 实例

    # Initialize Celery
    global celery
    celery = create_celery_app(app)

    return app
