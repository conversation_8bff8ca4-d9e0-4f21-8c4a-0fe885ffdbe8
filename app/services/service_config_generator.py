"""服务配置生成器"""
from typing import Dict, Optional

import jinja2


class ServiceConfigGenerator:
    """服务配置生成器"""

    @staticmethod
    def generate_service_config(
        base_config: Dict,
        project_compose: Dict,
        project_files: list,
        render_data: Dict,
        project_name: str
    ) -> Dict:
        """生成服务配置

        Args:
            base_config: 基础服务配置
            project_compose: 项目的服务配置
            project_files: 项目文件列表
            render_data: 用于渲染的数据
            project_name: 项目名称

        Returns:
            Dict: 生成的服务配置
        """
        try:
            # 准备基础配置
            config = {
                "name": base_config.get("name", ""),
                "description": base_config.get("description", ""),
                "docker_compose": {},
                "env": base_config.get("env", {}),
                "files": {}
            }

            # 渲染项目文件
            for file in project_files:
                rendered_content = file.render_content(render_data)
                if rendered_content is not None:
                    config["files"][file.name] = rendered_content

            # 合并 docker-compose 配置
            config["docker_compose"] = ServiceConfigGenerator._merge_compose_configs(
                base_config.get("docker_compose", {}),
                project_compose,
                render_data
            )

            return config
        except Exception:
            return None

    @staticmethod
    def _merge_compose_configs(base_compose: Dict, project_compose: Dict, render_data: Optional[Dict] = None) -> Dict:
        """合并 docker-compose 配置

        Args:
            base_compose: 基础配置的 docker-compose 内容（JSON格式）
            project_compose: 项目配置的 docker-compose 内容（JSON格式）
            render_data: 用于渲染模板的数据

        Returns:
            Dict: 合并后的 docker-compose 配置
        """
        try:
            # 创建一个基础配置的副本
            merged_config = {
                "version": "3",
                "services": {}
            }

            # 从基础配置中复制 services
            if base_compose and "services" in base_compose:
                merged_config["services"].update(base_compose["services"])

            # 从项目配置中添加或覆盖 services
            if project_compose and "services" in project_compose:
                merged_config["services"].update(project_compose["services"])

            # 如果有渲染数据，处理模板变量
            if render_data:
                merged_config = ServiceConfigGenerator._render_config_values(merged_config, render_data)

            return merged_config

        except Exception as e:
            print(f"Error merging compose configs: {str(e)}")
            return {
                "version": "3",
                "services": {}
            }

    @staticmethod
    def _render_config_values(config: Dict, render_data: Dict) -> Dict:
        """渲染配置中的模板变量

        Args:
            config: 要渲染的配置字典
            render_data: 渲染数据

        Returns:
            Dict: 渲染后的配置
        """
        env = jinja2.Environment(
            loader=jinja2.BaseLoader(),
            undefined=jinja2.StrictUndefined
        )

        def render_value(value):
            if isinstance(value, str):
                template = env.from_string(value)
                return template.render(**render_data)
            elif isinstance(value, dict):
                return {k: render_value(v) for k, v in value.items()}
            elif isinstance(value, list):
                return [render_value(item) for item in value]
            return value

        return render_value(config)
