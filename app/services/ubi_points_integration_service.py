from decimal import Decimal
from typing import Optional, Tuple

from app.enums.biz_enums import FixtureProjectName
from app.models.base import db
from app.models.project import Project
from app.models.blockchain import AssetType, AssetTypeEnum
from app.models.points import PointRecord
from app.services.asset_service import AssetService
from app.services.asset_type_service import AssetTypeService
from app.utils.errors import BusinessException


class UbiPointsIntegrationService:
    """UBI积分接入用户资产系统的集成服务"""
    
    @staticmethod
    def get_ubi_project():
        """获取UBI积分对应的项目
        
        Returns:
            Project: UBI积分对应的项目对象
            
        Raises:
            BusinessException: 当UBI项目不存在时
        """
        project = Project.query.filter_by(name=FixtureProjectName.UBI_PROJECT_NAME).first()
        if not project:
            raise BusinessException(f"UBI项目不存在，请先创建名为{FixtureProjectName.UBI_PROJECT_NAME}的项目", 404)
        return project
    
    @staticmethod
    def get_ubi_asset_type():
        """获取UBI积分对应的资产类型
        
        Returns:
            AssetType: UBI积分对应的资产类型对象
            
        Raises:
            BusinessException: 当UBI资产类型不存在时
        """
        project = UbiPointsIntegrationService.get_ubi_project()
        asset_type = AssetType.query.filter_by(
            project_id=project.id,
            type=AssetTypeEnum.POINTS
        ).first()
        
        if not asset_type:
            raise BusinessException(f"UBI积分资产类型不存在，请先创建", 404)
        
        return asset_type
    
    @staticmethod
    def ensure_ubi_asset_type_exists():
        """确保UBI积分资产类型存在，如果不存在则创建
        
        Returns:
            AssetType: UBI积分对应的资产类型对象
        """
        try:
            return UbiPointsIntegrationService.get_ubi_asset_type()
        except BusinessException:
            # 获取UBI项目
            project = UbiPointsIntegrationService.get_ubi_project()
            
            # 创建UBI积分资产类型
            asset_type = AssetTypeService.create_asset_type(
                name="UBI积分",
                asset_type=AssetTypeEnum.POINTS,
                decimals=2,  # 积分保留2位小数
                project_id=project.id
            )
            
            return asset_type
    
    @staticmethod
    def add_ubi_points(user_id: int, points: float, record_type: str = 'project', related_id: Optional[int] = None, fee: Decimal = Decimal(0)) -> Tuple[bool, Optional[str]]:
        """添加UBI积分并同步到用户资产
        
        Args:
            user_id: 用户ID
            points: 积分数量
            record_type: 积分记录类型
            related_id: 关联ID
            
        Returns:
            Tuple[bool, Optional[str]]: 成功状态和错误信息
        """
        try:
            # 1. 添加积分记录
            record = PointRecord(
                user_id=user_id,
                points=points,
                record_type=record_type,
                task_type_id=related_id
            )
            
            db.session.add(record)
            db.session.flush()  # 先flush以获取record.id
            
            # 2. 同步到用户资产
            asset_type = UbiPointsIntegrationService.ensure_ubi_asset_type_exists()
            
            # 将积分转换为Decimal类型，并保留资产类型定义的小数位数
            decimal_points = Decimal(str(points))
            
            # 添加到用户资产
            AssetService.add_or_create_user_asset(
                user_id=user_id,
                asset_type_id=asset_type.id,
                amount=decimal_points,
                fee=fee,
                reference_id=related_id,
                remark=f"UBI积分增加: {record_type}" + (f", 关联ID: {related_id}" if related_id else "")
            )
            
            db.session.commit()
            return True, None
        except Exception as e:
            db.session.rollback()
            return False, str(e)
    
    @staticmethod
    def get_user_ubi_points(user_id: int) -> Decimal:
        """获取用户的UBI积分余额
        
        Args:
            user_id: 用户ID
            
        Returns:
            Decimal: 用户的UBI积分余额
        """
        try:
            asset_type = UbiPointsIntegrationService.get_ubi_asset_type()
            return AssetService.get_balance(user_id, asset_type.id)
        except BusinessException:
            # 如果资产类型不存在，返回0
            return Decimal('0')
    
    @staticmethod
    def get_user_ubi_points_from_records(user_id: int) -> float:
        """从积分记录中获取用户的UBI积分总和（兼容旧方法）
        
        Args:
            user_id: 用户ID
            
        Returns:
            float: 用户的UBI积分总和
        """
        total_points = db.session.query(
            db.func.sum(PointRecord.points)
        ).filter(
            PointRecord.user_id == user_id
        ).scalar() or 0
        
        return float(total_points)