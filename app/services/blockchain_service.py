from datetime import datetime, timezone
from typing import Optional, Dict, Any

from sqlalchemy.orm.attributes import flag_modified

from app.models import db
from app.models.blockchain import Blockchain
from app.models.blockchain import Token, TokenType
from app.utils.errors import BusinessException


class BlockchainService:

    @staticmethod
    def get_by_code(chain_code: str):
        """根据链代码获取区块链信息"""
        return Blockchain.query.filter(
            Blockchain.chain_code == chain_code,
            Blockchain.deleted_at.is_(None)
        ).first()

    @staticmethod
    def get_all():
        """获取所有支持的区块链"""
        return Blockchain.query.filter_by(deleted_at=None).all()

    @staticmethod
    def create(chain_name: str, chain_code: str, coin_type: int, extend_field: Optional[dict] = None):
        """创建新的区块链记录"""
        blockchain = Blockchain()
        blockchain.chain_name = chain_name
        blockchain.chain_code = chain_code
        blockchain.coin_type = coin_type
        blockchain.extend_field = extend_field

        db.session.add(blockchain)
        db.session.commit()
        return blockchain

    @staticmethod
    def update(chain_code: str, data: dict):
        """更新区块链信息"""
        blockchain = Blockchain.query.filter(
            Blockchain.chain_code == chain_code,
            Blockchain.deleted_at.is_(None)
        ).first()

        if not blockchain:
            return None

        # 更新允许修改的字段
        allowed_fields = ['chain_name', 'coin_type', 'extend_field']
        for field in allowed_fields:
            if field in data:
                setattr(blockchain, field, data[field])
                # 如果是 JSON 字段，需要标记为已修改
                if field == 'extend_field':
                    flag_modified(blockchain, 'extend_field')

        db.session.commit()
        return blockchain

    @staticmethod
    def delete(chain_code: str):
        """软删除区块链记录"""
        blockchain = Blockchain.query.filter_by(chain_code=chain_code).first()
        if blockchain:
            blockchain.deleted_at = datetime.now(timezone.utc)
            db.session.commit()
            return True
        return False

    @staticmethod
    def create_token(token_code: str, token_symbol: str, token_name: str,
                     chain_id: int, decimals: int, token_type: TokenType,
                     contract_address: Optional[str] = None):
        """创建代币"""
        with db.session.begin_nested():
            # 合约地址校验
            if token_type != TokenType.NATIVE and not contract_address:
                raise BusinessException("非原生代币必须指定合约地址", 400)

            # 唯一性校验
            if Token.query.filter(Token.token_code == token_code).first():
                raise BusinessException("代币代码已存在", 409)

            new_token = Token()
            new_token.token_code = token_code
            new_token.token_symbol = token_symbol
            new_token.token_name = token_name
            new_token.chain_id = chain_id
            new_token.decimals = decimals
            new_token.token_type = token_type
            new_token.contract_address = contract_address
            db.session.add(new_token)
        db.session.commit()
        return new_token

    @staticmethod
    def get_token_by_code(token_code: str):
        """通过代码获取代币"""
        return Token.query.filter(Token.token_code == token_code).first()

    @staticmethod
    def get_token(token_id: int):
        """获取代币详情"""
        token =db.session.get(Token, token_id)
        if not token:
            raise BusinessException("代币不存在", 404)
        return token

    @staticmethod
    def list_tokens(page: int = 1, per_page: int = 10):
        """分页查询代币"""
        query = db.session.query(Token)
        total = query.count()
        items = query.order_by(Token.id.desc()).offset(
            (page - 1) * per_page
        ).limit(per_page).all()

        return {
            "total": total,
            "items": [b.to_dict() for b in items],
            "page": page,
            "per_page": per_page
        }

    @staticmethod
    def update_token(token_id: int, data: dict):
        """更新代币信息"""
        with db.session.begin_nested():
            token =db.session.get(Token, token_id)
            if not token:
                raise BusinessException("代币不存在", 404)

            # 不可修改字段
            immutable_fields = ['token_code', 'chain_id', 'token_type']
            for field in immutable_fields:
                if field in data:
                    raise BusinessException(f"不可修改字段: {field}", 400)

            # 合约地址校验
            if token.token_type != TokenType.NATIVE and 'contract_address' in data:
                if not data['contract_address']:
                    raise BusinessException("非原生代币必须指定合约地址", 400)
                token.contract_address = data['contract_address']

            # 更新允许修改的字段
            allowed_fields = ['token_symbol', 'token_name', 'decimals']
            for field in allowed_fields:
                if field in data:
                    setattr(token, field, data[field])

            db.session.commit()
            return token

    @staticmethod
    def delete_token(token_id: int):
        """删除代币"""
        token =db.session.get(Token, token_id)
        if not token:
            raise BusinessException("代币不存在", 404)

        db.session.delete(token)
        db.session.commit()
