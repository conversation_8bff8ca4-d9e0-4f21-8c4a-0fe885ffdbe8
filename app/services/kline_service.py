"""
完善的K线数据服务
支持多时间间隔、实时更新、数据验证等功能
"""

from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Union
import time
from dataclasses import dataclass

from flask import current_app

from app.models import db
from app.models.kline import KlineData
from app.models.order import OrderMatch
from app.utils.errors import BusinessException


@dataclass
class TradeData:
    """交易数据结构"""
    pair_id: int
    price: Decimal
    amount: Decimal
    timestamp: int
    maker_order_id: int = None
    taker_order_id: int = None


class KlineService:
    """K线数据服务"""

    # 支持的时间间隔配置
    INTERVALS = {
        'M1': 60,           # 1分钟
        'M5': 300,          # 5分钟
        'M15': 900,         # 15分钟
        'M30': 1800,        # 30分钟
        'H1': 3600,         # 1小时
        'H4': 14400,        # 4小时
        'H6': 21600,        # 6小时
        'H12': 43200,       # 12小时
        'D1': 86400,        # 1天
        'W1': 604800,       # 1周
    }

    @classmethod
    def process_trade(cls, trade_data: Union[TradeData, Dict]) -> bool:
        """处理交易数据，更新K线"""
        try:
            with db.session.begin_nested():
                # 数据格式化
                if isinstance(trade_data, dict):
                    trade = TradeData(
                        pair_id=trade_data['pair_id'],
                        price=Decimal(str(trade_data['price'])),
                        amount=Decimal(str(trade_data['amount'])),
                        timestamp=trade_data.get('timestamp', int(time.time() * 1000)),
                        maker_order_id=trade_data.get('maker_order_id'),
                        taker_order_id=trade_data.get('taker_order_id')
                    )
                else:
                    trade = trade_data

                # 验证数据
                cls._validate_trade_data(trade)

                # 更新所有时间间隔的K线
                for interval in cls.INTERVALS.keys():
                    cls._update_kline(trade, interval)
            return True

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"处理交易数据失败: {e}")
            return False

    @classmethod
    def _validate_trade_data(cls, trade: TradeData):
        """验证交易数据"""
        if not trade.pair_id or trade.pair_id <= 0:
            raise BusinessException("无效的交易对ID", 400)

        if not trade.price or trade.price <= 0:
            raise BusinessException("无效的交易价格", 400)

        if not trade.amount or trade.amount <= 0:
            raise BusinessException("无效的交易数量", 400)

        if not trade.timestamp or trade.timestamp <= 0:
            raise BusinessException("无效的时间戳", 400)

    @classmethod
    def _update_kline(cls, trade: TradeData, interval: str):
        """更新指定时间间隔的K线数据"""
        try:
            # 计算时间窗口
            open_time = cls._get_interval_start(trade.timestamp, interval)
            close_time = cls._get_interval_end(open_time, interval)

            # 查询现有K线
            kline = KlineData.query.filter_by(
                pair_id=trade.pair_id,
                interval_type=interval,
                open_time=open_time
            ).first()

            if not kline:
                # 创建新K线
                kline = KlineData(
                    pair_id=trade.pair_id,
                    interval_type=interval,
                    open_time=open_time,
                    close_time=close_time,
                    open_price=trade.price,
                    high_price=trade.price,
                    low_price=trade.price,
                    close_price=trade.price,
                    volume=trade.amount,
                    quote_volume=trade.amount * trade.price,
                    trade_count=1
                )
                db.session.add(kline)
            else:
                # 更新现有K线
                kline.high_price = max(kline.high_price, trade.price)
                kline.low_price = min(kline.low_price, trade.price)
                kline.close_price = trade.price
                kline.close_time = close_time
                kline.volume += trade.amount
                kline.quote_volume += trade.amount * trade.price
                kline.trade_count += 1

        except Exception as e:
            current_app.logger.error(f"更新K线失败 - 交易对:{trade.pair_id}, 间隔:{interval}, 错误:{e}")
            raise

    @classmethod
    def _get_interval_start(cls, timestamp: int, interval: str) -> int:
        """计算时间间隔的开始时间"""
        dt = datetime.fromtimestamp(timestamp / 1000)
        interval_seconds = cls.INTERVALS.get(interval)

        if not interval_seconds:
            raise BusinessException(f"不支持的时间间隔: {interval}", 400)

        if interval == 'M1':
            # 1分钟：对齐到分钟
            aligned = dt.replace(second=0, microsecond=0)
        elif interval in ['M5', 'M15', 'M30']:
            # 分钟级别：对齐到对应分钟
            minutes = int(interval[1:])
            aligned_minute = (dt.minute // minutes) * minutes
            aligned = dt.replace(minute=aligned_minute, second=0, microsecond=0)
        elif interval in ['H1', 'H4', 'H6', 'H12']:
            # 小时级别：对齐到对应小时
            hours = int(interval[1:])
            aligned_hour = (dt.hour // hours) * hours
            aligned = dt.replace(hour=aligned_hour, minute=0, second=0, microsecond=0)
        elif interval == 'D1':
            # 日级别：对齐到当天0点
            aligned = dt.replace(hour=0, minute=0, second=0, microsecond=0)
        elif interval == 'W1':
            # 周级别：对齐到周一0点
            days_since_monday = dt.weekday()
            aligned = dt.replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(days=days_since_monday)
        else:
            raise BusinessException(f"未实现的时间间隔: {interval}", 400)

        return int(aligned.timestamp() * 1000)

    @classmethod
    def _get_interval_end(cls, open_time: int, interval: str) -> int:
        """计算时间间隔的结束时间"""
        interval_seconds = cls.INTERVALS.get(interval)
        if not interval_seconds:
            raise BusinessException(f"不支持的时间间隔: {interval}", 400)

        return open_time + (interval_seconds * 1000)

    @classmethod
    def get_kline_data(cls, pair_id: int, interval: str,
                      start_time: Optional[int] = None,
                      end_time: Optional[int] = None,
                      limit: int = 1000) -> List[Dict]:
        """获取K线数据"""
        try:
            # 验证参数
            if interval not in cls.INTERVALS:
                raise BusinessException(f"不支持的时间间隔: {interval}", 400)

            if limit <= 0 or limit > 2000:
                raise BusinessException("limit 必须在 1-2000 之间", 400)

            # 构建查询
            query = KlineData.query.filter_by(
                pair_id=pair_id,
                interval_type=interval
            )

            if start_time:
                query = query.filter(KlineData.open_time >= start_time)
            if end_time:
                query = query.filter(KlineData.open_time <= end_time)

            # 执行查询
            klines = query.order_by(KlineData.open_time.asc()).limit(limit).all()

            # 格式化返回数据
            return [cls._format_kline_data(kline) for kline in klines]

        except Exception as e:
            current_app.logger.error(f"获取K线数据失败: {e}")
            raise

    @classmethod
    def _format_kline_data(cls, kline: KlineData) -> Dict:
        """格式化K线数据"""
        return {
            'open_time': kline.open_time,
            'close_time': kline.close_time,
            'open': str(kline.open_price),
            'high': str(kline.high_price),
            'low': str(kline.low_price),
            'close': str(kline.close_price),
            'volume': str(kline.volume),
            'quote_volume': str(kline.quote_volume),
            'trade_count': kline.trade_count
        }

    @classmethod
    def get_latest_price(cls, pair_id: int) -> Optional[Decimal]:
        """获取最新价格"""
        try:
            latest_kline = KlineData.query.filter_by(
                pair_id=pair_id,
                interval_type='M1'
            ).order_by(KlineData.open_time.desc()).first()

            return latest_kline.close_price if latest_kline else None

        except Exception as e:
            current_app.logger.error(f"获取最新价格失败: {e}")
            return None

    @classmethod
    def get_24h_stats(cls, pair_id: int) -> Dict:
        """获取24小时统计数据"""
        try:
            now = int(time.time() * 1000)
            start_time = now - (24 * 60 * 60 * 1000)  # 24小时前

            # 获取24小时内的1分钟K线数据
            klines = cls.get_kline_data(
                pair_id=pair_id,
                interval='M1',
                start_time=start_time,
                end_time=now
            )

            if not klines:
                return {
                    'open': '0',
                    'high': '0',
                    'low': '0',
                    'close': '0',
                    'volume': '0',
                    'quote_volume': '0',
                    'change': '0',
                    'change_percent': '0'
                }

            # 计算统计数据
            open_price = Decimal(klines[0]['open'])
            close_price = Decimal(klines[-1]['close'])
            high_price = max(Decimal(k['high']) for k in klines)
            low_price = min(Decimal(k['low']) for k in klines)
            total_volume = sum(Decimal(k['volume']) for k in klines)
            total_quote_volume = sum(Decimal(k['quote_volume']) for k in klines)

            change = close_price - open_price
            change_percent = (change / open_price * 100) if open_price > 0 else Decimal('0')

            return {
                'open': str(open_price),
                'high': str(high_price),
                'low': str(low_price),
                'close': str(close_price),
                'volume': str(total_volume),
                'quote_volume': str(total_quote_volume),
                'change': str(change),
                'change_percent': f"{change_percent:.2f}"
            }

        except Exception as e:
            current_app.logger.error(f"获取24小时统计失败: {e}")
            return {}

    @classmethod
    def rebuild_kline_from_trades(cls, pair_id: int, interval: str,
                                 start_time: Optional[int] = None,
                                 end_time: Optional[int] = None) -> bool:
        """从历史成交记录重建K线数据"""
        try:
            # 查询成交记录
            query = db.session.query(OrderMatch).join(
                'order'
            ).filter_by(pair_id=pair_id)

            if start_time:
                query = query.filter(OrderMatch.created_at >= datetime.fromtimestamp(start_time / 1000))
            if end_time:
                query = query.filter(OrderMatch.created_at <= datetime.fromtimestamp(end_time / 1000))

            matches = query.order_by(OrderMatch.created_at.asc()).all()

            # 删除现有K线数据
            delete_query = KlineData.query.filter_by(
                pair_id=pair_id,
                interval_type=interval
            )
            if start_time:
                delete_query = delete_query.filter(KlineData.open_time >= start_time)
            if end_time:
                delete_query = delete_query.filter(KlineData.open_time <= end_time)

            delete_query.delete()

            # 重建K线数据
            for match in matches:
                trade_data = TradeData(
                    pair_id=pair_id,
                    price=match.price,
                    amount=match.amount,
                    timestamp=int(match.created_at.timestamp() * 1000),
                    maker_order_id=match.maker_order_id,
                    taker_order_id=match.taker_order_id
                )
                cls._update_kline(trade_data, interval)

            db.session.commit()
            return True

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"重建K线数据失败: {e}")
            return False
