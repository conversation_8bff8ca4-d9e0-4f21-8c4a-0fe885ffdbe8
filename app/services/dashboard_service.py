"""仪表盘相关的服务层"""
from typing import Dict, Optional, Tuple

from sqlalchemy import func, or_

from app.models.base import db
from app.models.device import Device
from app.models.device_project import DeviceProject
from app.models.project import Project
from app.models.user import User


class DashboardService:
    """仪表盘服务"""

    @staticmethod
    def get_overview_data(user: User) -> Dict:
        """获取仪表盘概览数据
        Args:
            user: 当前用户
        Returns:
            Dict: 概览数据
        """
        # 设备总数
        if user.is_admin:
            device_count = Device.query.count()
        else:
            device_count = len(user.devices)

        # 项目总数
        if user.is_admin:
            project_count = Project.not_deleted().count()
        else:
            device_ids = [device.id for device in user.devices]
            if device_ids:
                project_ids = (
                    DeviceProject.query
                    .filter(DeviceProject.device_id.in_(device_ids))
                    .with_entities(DeviceProject.project_id)
                    .distinct()
                    .all()
                )
                project_ids = [pid[0] for pid in project_ids]
                project_count = Project.not_deleted().filter(Project.id.in_(project_ids)).count()
            else:
                project_count = 0

        # 在线设备数 - Updated to check for status 2 (running)
        if user.is_admin:
            online_device_count = Device.query.filter_by(status=2).count()
        else:
            online_device_count = len([d for d in user.devices if d.status == 2])

        return {
            "device_count": device_count,
            "project_count": project_count,
            "online_device_count": online_device_count
        }

    @staticmethod
    def get_device_status(user: User) -> Tuple[Optional[Dict], Optional[str]]:
        """获取设备状态统计

        Args:
            user: 当前用户

        Returns:
            Tuple[Optional[Dict], Optional[str]]: 状态统计数据和错误信息
        """
        try:
            # 基础查询
            query = db.session.query(
                Device.status,
                func.count(Device.id).label("count")
            )

            # 如果不是管理员，只统计自己的设备
            if user.role != "admin":
                query = query.filter(Device.owner_id == user.id)

            # 按状态分组
            status_stats = query.group_by(Device.status).all()

            # 转换为字典 - Note: Device.STATUS_CHOICES should be defined in the Device model
            # Status values:
            # 0: initializing
            # 1: wait to configure
            # 2: running
            # 3: failure
            # 4: offline
            result = {0: 0, 1: 0, 2: 0, 3: 0, 4: 0}
            for status, count in status_stats:
                if status in result:
                    result[status] = count

            return result, None
        except Exception as e:
            return None, str(e)

    def get_device_stats(self) -> Dict:
        """获取设备统计信息"""
        # 获取设备总数
        total_devices = Device.query.count()

        # 获取在线设备数 - Updated to check for status 2 (running)
        online_devices = Device.query.filter_by(status=2).count()

        # 获取离线设备数 - Using more efficient single query with or_
        offline_devices = Device.query.filter(
            or_(Device.status == 3, Device.status == 4)
        ).count()

        # 获取未授权设备数
        unauthorized_devices = Device.query.filter_by(owner_id=None).count()

        return {
            "total": total_devices,
            "online": online_devices,
            "offline": offline_devices,
            "unauthorized": unauthorized_devices
        }

    def get_project_stats(self) -> Dict:
        """获取项目统计信息"""
        # 获取项目总数
        total_projects = Project.query.count()

        # 获取启用的项目数
        enabled_projects = Project.query.filter_by(status=1).count()

        # 获取禁用的项目数
        disabled_projects = Project.query.filter_by(status=0).count()

        # 获取设备项目关联数
        device_project_count = DeviceProject.query.count()

        return {
            "total": total_projects,
            "enabled": enabled_projects,
            "disabled": disabled_projects,
            "device_project_count": device_project_count
        }

    def get_device_status_distribution(self) -> Dict:
        """获取设备状态分布"""
        # 按状态分组统计设备数量
        status_counts = db.session.query(
            Device.status,
            func.count(Device.id)
        ).group_by(Device.status).all()

        # 转换为字典格式
        return {status: count for status, count in status_counts}

    def get_device_project_distribution(self) -> Dict:
        """获取设备项目分布"""
        # 统计每个项目关联的设备数量
        project_counts = db.session.query(
            Project.name,
            func.count(DeviceProject.device_id)
        ).join(
            DeviceProject,
            Project.id == DeviceProject.project_id
        ).group_by(
            Project.name
        ).all()

        # 转换为字典格式
        return {name: count for name, count in project_counts}
