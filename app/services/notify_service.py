from datetime import datetime, timezone
from typing import Dict, List, Optional, Tuple

from sqlalchemy import desc, func, select

from app.enums.biz_enums import NotifyPriority, NotifyStatus, NotifyType
from app.models import db
from app.models.notify import Notify, UserNotify
from app.tasks.notify_tasks import process_notification_queue


class NotifyService:
    @staticmethod
    def get_notifications(page: int = 1,
                          per_page: int = 20,
                          notify_type_list: Optional[List[NotifyType]] = None,
                          priority_list: Optional[List[int]] = None,
                          status_list: Optional[List[int]] = None,
                          title: Optional[str] = None,
                          content: Optional[str] = None,
                          start_date: Optional[datetime] = None,
                          end_date: Optional[datetime] = None,
                          ):
        """获取通知列表，支持多种过滤条件

                Args:
                    page: 页码
                    per_page: 每页数量
                    notify_type_list: 通知类型列表
                    title: 标题
                    content: 内容
                    start_date: 开始日期
                    end_date: 结束日期
                    status_list: 状态
                    notify_type_list: 通知类型
                    priority_list: 优先级
                """
        # 添加通知表的过滤条件
        query = Notify.query.filter_by(is_deleted=False)

        if notify_type_list:
            query = query.filter(Notify.notify_type.in_(notify_type_list))
        if priority_list:
            query = query.filter(Notify.notify_priority.in_(priority_list))
        if status_list:
            query = query.filter(Notify.notify_status.in_(status_list))
        if title:
            query = query.filter(func.json_extract(Notify.title, '$').cast(db.String).ilike(f'%{title}%'))
        if content:
            query = query.filter(func.json_extract(Notify.content, '$').cast(db.String).ilike(f'%{content}%'))
        if start_date:
            query = query.filter(Notify.created_at >= start_date)
        if end_date:
            query = query.filter(Notify.created_at <= end_date)
        # 按时间倒序排序
        query = query.order_by(
            desc(Notify.created_at)
        )
        total = query.count()
        if total == 0:
            return [], total

        notifications = query.paginate(page=page, per_page=per_page, error_out=False).items
        return [notification.to_dict() for notification in notifications], total

    @staticmethod
    def get_user_notifications(
            locale: str,
            user_id: int,
            page: int = 1,
            per_page: int = 20,
            notify_type_list: Optional[list[str]] = None,
            notify_priority_list: Optional[list[int]] = None,
            unread_only: bool = False,
    ) -> Tuple[List[Dict], int]:
        """获取用户的通知列表，支持多种过滤条件
        
        Args:
            locale: i18n locale
            user_id: 用户ID
            page: 页码
            per_page: 每页数量
            notify_type_list: 通知类型
            notify_priority_list: 优先级
            unread_only: 是否只获取未读通知
        """
        query = UserNotify.query.filter_by(user_id=user_id)

        if unread_only:
            query = query.filter_by(is_read=False)

        # 添加通知表的过滤条件
        query = query.join(Notify)
        # 仅查询已发布状态的通知
        query = query.filter(Notify.notify_status == NotifyStatus.PUBLISHED).filter(Notify.is_deleted == False)

        if notify_type_list:
            query = query.filter(Notify.notify_type.in_(notify_type_list))
        if notify_priority_list:
            query = query.filter(Notify.notify_priority.in_(notify_priority_list))

        # 按优先级和时间倒序排序
        query = query.order_by(
            desc(Notify.notify_priority),
            desc(Notify.created_at)
        )

        total = query.count()
        if total == 0:
            return [], total

        notifications = query.paginate(page=page, per_page=per_page, error_out=False).items
        return [notification.to_user_response_dict(locale) for notification in notifications], total


    @staticmethod
    def create_notification(
            creator: int,
            notify_type: str,
            title: Dict,
            content: Dict,
            notify_priority: int = NotifyPriority.NORMAL,
            user_ids: Optional[List[int]] = None
    ) -> Dict:
        """创建通知
        
        Args:
            creator:创建人
            notify_type: 通知类型
            title: 通知标题
            content: 通知内容
            notify_priority: 优先级
            user_ids: 指定用户ID列表，为None时发送给所有用户
        """
        extend_field = {'user_ids': user_ids}
        # 创建通知
        notify = Notify(
            notify_type=notify_type,
            title=title,
            content=content,
            notify_priority=notify_priority,
            notify_status=NotifyStatus.DRAFT,
            creator=creator,
            extend_field=extend_field
        )
        db.session.add(notify)
        db.session.commit()

        return notify.to_dict()

    @staticmethod
    def get_and_read_user_notification(locale:str, id: int, user_id: int) -> Dict:
        """标记通知为已读
        
        Args:
            id: 通知ID
            user_id: 用户ID
        """
        user_notify = (UserNotify.query.filter_by(
            id=id,
            user_id=user_id
        ).join(Notify).filter(Notify.notify_status == NotifyStatus.PUBLISHED).filter(Notify.is_deleted == False).first())

        if not user_notify:
            raise ValueError("通知不存在")

        if not user_notify.is_read:
            user_notify.is_read = True
            user_notify.read_timestamp = datetime.now(timezone.utc)
            db.session.commit()

        return user_notify.to_user_response_dict(locale)

    @staticmethod
    def get_unread_count(user_id: int,
                         notify_type_list: Optional[list[str]] = None,
                         notify_priority_list: Optional[list[int]] = None, ) -> int:
        """获取未读通知数量
        
        Args:
            user_id: 用户ID
            notify_type_list: 通知类型
            notify_priority_list: 优先级
        """
        query = UserNotify.query.filter_by(
            user_id=user_id,
            is_read=False
        ).join(Notify).filter(Notify.notify_status == NotifyStatus.PUBLISHED).filter(Notify.is_deleted == False)

        if notify_type_list:
            query = query.filter(Notify.notify_type.in_(notify_type_list))
        if notify_priority_list:
            query = query.filter(Notify.notify_priority.in_(notify_priority_list))
        return query.count()


    @staticmethod
    def delete_notification(notify_id: int, user_id: int) -> Optional[Dict]:
        """删除通知

        Args:
            notify_id: 通知ID
            user_id: 用户ID

        """
        notify = Notify.query.get(notify_id)
        if not notify:
            raise ValueError("通知不存在")

        notify.is_deleted = True
        notify.updator = user_id
        notify.updated_at = datetime.now(timezone.utc)
        db.session.commit()
        return notify.to_dict()

    @staticmethod
    def update_notification(notify_id: int, user_id: int, data: dict) -> Optional[Dict]:
        """更新通知

        Args:
            notify_id: 通知ID
            user_id: 操作人ID
            data: 更新数据

        """

        notify = Notify.query.get(notify_id)
        if not notify:
            raise ValueError("通知不存在")
        if notify.notify_status == NotifyStatus.PUBLISHED:
            raise ValueError("已发布状态不可更新")

        notify.content = data.get('content', notify.content)
        notify.title = data.get('title', notify.title)
        notify.notify_type = data.get('notify_type', notify.notify_type)
        notify.notify_priority = data.get('notify_priority', notify.notify_priority)
        user_ids = data.get('user_ids')
        extend_field = {'user_ids': user_ids}
        notify.extend_field = extend_field
        notify.updated_at = datetime.now(timezone.utc)
        notify.updator = user_id
        db.session.commit()

        return notify.to_dict()

    @staticmethod
    def delete_user_notify(id: int, user_id: int):
        """删除通知

        Args:
            id: 用户通知ID
            user_id: 用户ID

        """

        # 普通用户只能删除自己的通知（标记为删除状态）
        user_notify = UserNotify.query.filter_by(
            id=id,
            user_id=user_id
        ).first()

        if not user_notify:
            raise ValueError("通知不存在")

        db.session.delete(user_notify)
        db.session.commit()

    @staticmethod
    def publish_notify(notify_id: int, user_id: int):
        notify = Notify.query.get(notify_id)
        if not notify:
            raise ValueError("通知不存在")
        if Notify.notify_status == NotifyStatus.PUBLISHED:
            raise ValueError("通知已发布")
        notify.notify_status = NotifyStatus.PUBLISHED
        notify.updated_at = datetime.now(timezone.utc)
        notify.updator = user_id
        db.session.commit()
        process_notification_queue.delay(notify_id)

    @staticmethod
    def retract_notify(notify_id: int, user_id: int):
        with db.session.begin_nested():
            notify = Notify.query.get(notify_id)
            if not notify:
                raise ValueError("通知不存在")
            if notify.notify_status != NotifyStatus.PUBLISHED:
                raise ValueError("通知未发布")
            notify.notify_status = NotifyStatus.RETRACT
            notify.updated_at = datetime.now(timezone.utc)
            notify.updator = user_id
            UserNotify.query.filter_by(notify_id=notify_id).delete()
            db.session.commit()

    @staticmethod
    def batch_read(user_id: int,
                   id_list: Optional[List[int]] = None,
                   notify_type_list: Optional[List[str]] = None,
                   notify_priority_list: Optional[List[int]] = None):
        query = UserNotify.query.filter_by(
            user_id=user_id,
            is_read=False
        ).join(Notify).filter(Notify.notify_status == NotifyStatus.PUBLISHED).filter(Notify.is_deleted == False)

        if id_list:
            query = query.filter(UserNotify.id.in_(id_list))
        if notify_type_list:
            query = query.filter(Notify.notify_type.in_(notify_type_list))
        if notify_priority_list:
            query = query.filter(Notify.notify_priority.in_(notify_priority_list))

        query_result = query.all()
        if query_result:
            # 使用子查询更新数据
            query_result_ids = [user_notify.id for user_notify in query_result]
            update_query = UserNotify.query.filter(UserNotify.id.in_(query_result_ids))
            update_query.update({'is_read': True, 'read_timestamp': datetime.now(timezone.utc)})
            db.session.commit()