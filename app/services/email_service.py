import os
import random
import requests
from flask import render_template_string
from app.models.user import User
from app.utils.response import Response
from app.utils.redis_client import RedisClient


class EmailService:
    @staticmethod
    def send_verification_code(user_id: str, email: str) -> Response:
        try:
            code = str(random.randint(100000, 999999))
            redis_key = f"verifycode{email}"
            RedisClient.get_instance().setex(redis_key, 600, code)
            with open('app/templates/verification_email.html') as f:
                html_content = render_template_string(f.read(), code=code)

            response = requests.post(
                f"https://api.mailgun.net/v3/{os.getenv('MAILGUN_DOMAIN')}/messages",
                auth=("api", os.getenv("MAILGUN_API_KEY")),
                data={
                    "from": f"Box System <{os.getenv('MAILGUN_FROM_EMAIL')}>",
                    "to": [email],
                    "subject": "邮箱验证码",
                    "html": html_content
                }
            )
            response.raise_for_status()
            return Response.success(message="验证码发送成功")
        except Exception as e:
            return Response.error(f"发送失败: {str(e)}")

    @staticmethod
    def send_reset_password_code(email: str):
        redis_key = f"reset:{email}"
        if RedisClient.get_instance().exists(redis_key):
            return Response.error(f"请勿重复提交", 400)
        # 查找该邮箱是否存在于数据库中
        user = User.query.filter_by(email=email).first()
        if not user:
            return Response.error("User not found", 404)
        try:
            code = str(random.randint(100000, 999999))
            RedisClient.get_instance().setex(redis_key, 3600, code)

            # base_url = 'http://127.0.0.1:5501'
            base_url = os.getenv('BASE_UBI_DOMAIN','https://app.ubinetwork.ai')
            reset_url = f"{base_url}/reset-password?token={code}"

            with open('app/templates/reset_pwd_email.html') as f:
                html_content = render_template_string(f.read(), reset_url=reset_url)

            response = requests.post(
                f"https://api.mailgun.net/v3/{os.getenv('MAILGUN_DOMAIN')}/messages",
                auth=("api", os.getenv("MAILGUN_API_KEY")),
                data={
                    "from": f"Box System <{os.getenv('MAILGUN_FROM_EMAIL')}>",
                    "to": [email],
                    "subject": "Reset Password",
                    "html": html_content
                }
            )
            response.raise_for_status()

            return Response.success(message="Send success")
        except Exception as e:
            RedisClient.get_instance().delete(redis_key)
            return Response.error(f"发送失败: {str(e)}", 500)
