"""认证服务模块"""
from typing import Optional, <PERSON><PERSON>

from flask_jwt_extended import create_access_token
from flask import current_app
from app.utils.redis_client import RedisClient
from app.models.base import db
from app.models.user import User
from datetime import timedelta


class AuthService:
    """认证服务类"""

    @staticmethod
    def register(username: str, password: str, email: str, invite_code: str = "", role: str = "user"):
        """注册用户

        Args:
            username: 用户名
            password: 密码
            email: 邮箱
            invite_code: 邀请码
            role: 角色，默认为 'user'

        Returns:
            Tuple[Optional[User], Optional[str]]: (用户对象, 错误信息)
            如果注册成功，错误信息为 None
        """
        # 检查用户名是否已存在
        if User.query.filter_by(username=username).first():
            return None, None, "用户名已存在"

        # 检查邮箱是否已存在
        if User.query.filter_by(email=email).first():
            return None, None, "邮箱已存在"

        invited_by = User.query.filter_by(invite_code=invite_code).first()
        if invited_by:
            user = User(username=username, email=email, role=role, invited_by=invited_by.id)
        else:
            user = User(username=username, email=email, role=role)
        user.set_password(password)
        db.session.add(user)
        db.session.commit()  # 在这里提交后，user 的 id 会被填充

        # 给创建的用户生成token并返回
        token = create_access_token(identity=str(user.id), expires_delta=timedelta(days=30))
        return token, user, None

    @staticmethod
    def login(username: str, password: str, captcha_id: str = "", captcha_text: str = "") -> Tuple[Optional[str], Optional[str]]:
        """用户登录

        Args:
            username: 用户名
            password: 密码
            captcha_id: 验证码ID
            captcha_text: 验证码文本

        Returns:
            Tuple[Optional[str], Optional[str]]: (token, 错误信息)
            如果登录成功，错误信息为 None
        """
        user = User.query.filter_by(username=username).first()

        if not user:
            return None, "用户不存在"

        if not user.check_password(password):
            return None, "密码错误"

        # 在非测试环境下，非管理员用户需要验证码
        if user.role != "admin" and not current_app.config.get('TESTING'):
            origin_captcha_text = RedisClient.get_instance().get(captcha_id)
            if not origin_captcha_text or origin_captcha_text != captcha_text:
                return None, "验证码错误或已过期"
            RedisClient.get_instance().delete(captcha_id)

        token = create_access_token(identity=str(user.id), expires_delta=timedelta(days=30))
        return token, None

    @staticmethod
    def get_users(page: int = 1, per_page: int = 10, **params) -> Tuple[list, int]:
        """获取用户列表

        Args:
            page: 页码，默认为 1
            per_page: 每页数量，默认为 10

        Returns:
            Tuple[list, int]: (用户列表, 总用户数)
        """
        query = User.query
        if(params.get('email')):
            query = query.filter(User.email.like(f"%{params['email']}%"))
        if (params.get('username')):
            query = query.filter(User.username.like(f"%{params['username']}%"))
        pagination = query.paginate(page=page, per_page=per_page)
        return pagination.items, pagination.total

    @staticmethod
    def update_user(user_id: int, data: dict) -> Tuple[Optional[User], Optional[str]]:
        """更新用户信息

        Args:
            user_id: 用户 ID
            data: 更新数据

        Returns:
            Tuple[Optional[User], Optional[str]]: (用户对象, 错误信息)
            如果更新成功，错误信息为 None
        """
        user = db.session.get(User, user_id)
        if not user:
            return None, "用户不存在"

        if "username" in data:
            existing = User.query.filter(
                User.username == data["username"],
                User.id != user_id
            ).first()
            if existing:
                return None, "用户名已存在"
            user.username = data["username"]

        if "email" in data:
            existing = User.query.filter(
                User.email == data["email"],
                User.id != user_id
            ).first()
            if existing:
                return None, "邮箱已存在"
            user.email = data["email"]

        if "role" in data:
            user.role = data["role"]

        if "password" in data:
            user.set_password(data["password"])

        db.session.commit()
        return user, None

    @staticmethod
    def reset_password(token: str, new_password: str):
        email = None
        for key in RedisClient.get_instance().scan_iter("reset:*"):
            stored_token = RedisClient.get_instance().get(key)
            if stored_token == token:
                email = key.split("reset:")[1]  # 获取邮箱
                break

        if not email:
            return "The token is invalid or has expired", 400

        if not new_password:
            return "New password is required", 400

        # 查找用户并更新密码
        user = User.query.filter_by(email=email).first()
        if not user:
            return "User not found", 404

        user.set_password(new_password)  # 假设你有 set_password 方法来加密新密码
        db.session.commit()

        # 删除已使用的 token
        RedisClient.get_instance().delete(f"reset:{email}")

        return None, 200
