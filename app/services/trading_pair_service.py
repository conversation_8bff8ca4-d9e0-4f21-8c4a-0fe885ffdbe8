from app.models import db
from app.models.blockchain import TradingPair, AssetType, AssetTypeEnum
from app.utils.errors import BusinessException
from app.models.order import Order, OrderMatch
from app.services.kline_service import KlineService
from sqlalchemy import desc

class TradingPairService:

    @staticmethod
    def create_trading_pair(data: dict):
        """创建交易对"""
        with db.session.begin_nested():
            # 修复字典键值访问方式
            base_asset_id = data['base_asset_id']
            quote_asset_id = data['quote_asset_id']
            pair_name = data['pair_name']
            
            # 验证资产是否存在
            TradingPairService._validate_asset(base_asset_id)
            TradingPairService._validate_asset(quote_asset_id)
            
            # 检查相同资产组合
            existing = db.session.query(TradingPair).filter(
                TradingPair.base_asset_id == base_asset_id,
                TradingPair.quote_asset_id == quote_asset_id
            ).first()
            if existing:
                raise BusinessException(f"相同资产组合的交易对已存在（ID:{existing.id}）", 400)

            # 名称重复检查
            if db.session.query(TradingPair).filter_by(pair_name=pair_name).first():
                raise BusinessException("交易对名称已存在", 400)
                
            new_pair = TradingPair(
                base_asset_id=base_asset_id,
                quote_asset_id=quote_asset_id,
                pair_name=pair_name,
                min_price=data['min_price'],
                max_price=data['max_price'],
                price_precision=data['price_precision'],
                amount_precision=data['amount_precision']
            )
            db.session.add(new_pair)
            db.session.commit()
            return new_pair

    @staticmethod
    def _validate_asset(asset_id: int):
        if not db.session.get(AssetType, asset_id):
            raise BusinessException(f"资产{asset_id}不存在", 404)

    @staticmethod
    def update_trading_pair(pair_id: int, data: dict):
        """更新交易对"""
        with db.session.begin_nested():
            pair = db.session.get(TradingPair,pair_id)
            if not pair:
                raise BusinessException("交易对不存在", 404)
                
            for key, value in data.items():
                setattr(pair, key, value)
                
            db.session.commit()
            return pair

    @staticmethod
    def delete_trading_pair(pair_id: int):
        """删除交易对"""
        pair = db.session.get(TradingPair, pair_id)
        if not pair:
            raise BusinessException("交易对不存在", 404)
            
        db.session.delete(pair)
        db.session.commit()

    @staticmethod
    def get_trading_pair(pair_id: int):
        """获取交易对详情"""
        pair = db.session.get(TradingPair, pair_id)
        if not pair:
            raise BusinessException("交易对不存在", 404)
        return pair

    @staticmethod
    def list_trading_pairs(page: int = 1, per_page: int = 10):
        """分页查询交易对"""
        query = db.session.query(TradingPair)
        total = query.count()
        items = query.order_by(TradingPair.id.desc()).offset(
            (page - 1) * per_page
        ).limit(per_page).all()
        
        return {
            "total": total,
            "items": items,
            "page": page,
            "per_page": per_page
        }
        
    @staticmethod
    def get_enhanced_trading_pairs(page: int = 1, per_page: int = 10, base_asset_type: str = None):
        """获取增强的交易对列表，包含最后一单价格、24小时涨跌幅和交易量
        
        Args:
            page: 当前页码
            per_page: 每页数量
            base_asset_type: 基础资产类型，可选值：'POINTS'或'TOKEN'
            
        Returns:
            dict: 包含交易对列表和分页信息的字典
        """
        # 构建查询
        query = db.session.query(TradingPair).join(
            AssetType, TradingPair.base_asset_id == AssetType.id
        )
        
        # 如果指定了基础资产类型，进行筛选
        if base_asset_type:
            try:
                asset_type_enum = AssetTypeEnum[base_asset_type]
                query = query.filter(AssetType.type == asset_type_enum)
            except KeyError:
                # 如果提供了无效的资产类型，返回空结果
                return {
                    "total": 0,
                    "items": [],
                    "page": page,
                    "per_page": per_page
                }
        
        # 获取总数和分页数据
        total = query.count()
        pairs = query.order_by(TradingPair.id.desc()).offset(
            (page - 1) * per_page
        ).limit(per_page).all()
        
        # 增强交易对数据
        enhanced_pairs = []
        for pair in pairs:
            pair_dict = pair.to_dict()
            
            # 获取最后一单价格
            last_match = db.session.query(OrderMatch).filter(
                OrderMatch.order_id.in_(
                    db.session.query(Order.id).filter(Order.pair_id == pair.id)
                )
            ).order_by(desc(OrderMatch.created_at)).first()
            
            if last_match:
                pair_dict['last_price'] = str(last_match.price)
            else:
                pair_dict['last_price'] = '0'
            
            # 获取24小时统计数据
            stats_24h = KlineService.get_24h_stats(pair.id)
            pair_dict.update({
                'price_change_24h': stats_24h.get('change', '0'),
                'price_change_percent_24h': stats_24h.get('change_percent', '0'),
                'volume_24h': stats_24h.get('volume', '0'),
                'quote_volume_24h': stats_24h.get('quote_volume', '0')
            })
            
            enhanced_pairs.append(pair_dict)
        
        return {
            "total": total,
            "items": enhanced_pairs,
            "page": page,
            "per_page": per_page
        }