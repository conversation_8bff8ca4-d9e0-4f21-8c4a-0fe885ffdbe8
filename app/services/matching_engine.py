"""
简单的订单撮合引擎
处理订单匹配和成交逻辑
"""

from typing import List

from flask import current_app
from sqlalchemy import and_, or_

from app.models import db
from app.models.order import Order, OrderTypeEnum, OrderSideEnum, OrderStatusEnum
from app.services.order_match_service import OrderMatchService
from app.utils.errors import BusinessException


class MatchingEngine:
    """订单撮合引擎"""
    
    @classmethod
    def process_new_order(cls, new_order: Order) -> List[dict]:
        """处理新订单，尝试撮合"""
        matches = []
        
        try:
            # 只处理限价单的撮合（市价单需要更复杂的逻辑）
            if new_order.order_type != OrderTypeEnum.LIMIT.value:
                return matches
            with db.session.begin_nested():
                # 查找可匹配的订单
                matching_orders = cls._find_matching_orders(new_order)

                # 执行撮合
                remaining_amount = new_order.original_amount

                for matching_order in matching_orders:
                    if remaining_amount <= 0:
                        break

                    # 计算成交数量
                    match_amount = min(
                        remaining_amount,
                        matching_order.original_amount - matching_order.executed_amount
                    )

                    if match_amount <= 0:
                        continue

                    # 确定成交价格（使用挂单价格）
                    match_price = matching_order.price

                    # 确定maker和taker
                    maker_order = matching_order  # 先挂单的是maker
                    taker_order = new_order       # 新订单是taker

                    # 处理订单成交
                    order_match = OrderMatchService.process_order_match(
                        maker_order=maker_order,
                        taker_order=taker_order,
                        match_price=match_price,
                        match_amount=match_amount
                    )

                    matches.append({
                        'match_id': order_match.id,
                        'maker_order_id': maker_order.id,
                        'taker_order_id': taker_order.id,
                        'price': str(match_price),
                        'amount': str(match_amount)
                    })

                    # 更新剩余数量
                    remaining_amount -= match_amount

                    # 刷新订单状态
                    db.session.add(matching_order)
                    db.session.add(new_order)

            return matches
            
        except Exception as e:
            current_app.logger.error(f"订单撮合失败: {str(e)}", stack_info=True)
            raise BusinessException(f"订单撮合失败: {str(e)}", 500)

    @classmethod
    def matching_order(cls, new_order: Order, matching_order: Order):
        remaining_amount = new_order.original_amount
        if remaining_amount <= 0:
            return None

        # 计算成交数量
        match_amount = min(
            remaining_amount,
            matching_order.original_amount - matching_order.executed_amount
        )

        if match_amount <= 0:
            return None

        # 确定成交价格（使用挂单价格）
        match_price = matching_order.price

        # 确定maker和taker
        maker_order = matching_order  # 先挂单的是maker
        taker_order = new_order  # 新订单是taker

        # 处理订单成交
        order_match = OrderMatchService.process_order_match(
            maker_order=maker_order,
            taker_order=taker_order,
            match_price=match_price,
            match_amount=match_amount
        )

        # 更新剩余数量
        remaining_amount -= match_amount

        # 刷新订单状态
        db.session.add(matching_order)
        db.session.add(new_order)
        return ({
            'match_id': order_match.id,
            'maker_order_id': maker_order.id,
            'taker_order_id': taker_order.id,
            'price': str(match_price),
            'amount': str(match_amount)
        })

    @classmethod
    def _find_matching_orders(cls, new_order: Order) -> List[Order]:
        """查找可匹配的订单"""
        try:
            # 构建基础查询
            query = db.session.query(Order).filter(
                and_(
                    Order.pair_id == new_order.pair_id,
                    Order.status.in_([OrderStatusEnum.PENDING, OrderStatusEnum.PARTIAL]),
                    Order.order_type == OrderTypeEnum.LIMIT,
                    Order.id != new_order.id,  # 排除自己
                    Order.user_id != new_order.user_id,  # 排除自己的订单
                    Order.original_amount > Order.executed_amount  # 还有剩余数量
                )
            )
            
            # 根据买卖方向添加价格条件
            if new_order.side == OrderSideEnum.BUY.value:
                # 新订单是买单，匹配卖单，价格 <= 买单价格
                query = query.filter(
                    and_(
                        Order.side == OrderSideEnum.SELL,
                        Order.price <= new_order.price
                    )
                )
                # 按价格升序排列（优先成交价格低的卖单）
                query = query.order_by(Order.price.asc(), Order.created_at.asc())
            else:
                # 新订单是卖单，匹配买单，价格 >= 卖单价格
                query = query.filter(
                    and_(
                        Order.side == OrderSideEnum.BUY,
                        Order.price >= new_order.price
                    )
                )
                # 按价格降序排列（优先成交价格高的买单）
                query = query.order_by(Order.price.desc(), Order.created_at.asc())
            
            # 限制匹配数量，避免一次处理太多
            return query.limit(10).all()
            
        except Exception as e:
            current_app.logger.error(f"查找匹配订单失败: {e}")
            return []
    
    @classmethod
    def get_order_book(cls, pair_id: int, depth: int = 10) -> dict:
        """获取订单簿"""
        try:
            # 获取买单（按价格降序）
            buy_orders = db.session.query(Order).filter(
                and_(
                    Order.pair_id == pair_id,
                    Order.side == OrderSideEnum.BUY,
                    Order.status.in_([OrderStatusEnum.PENDING, OrderStatusEnum.PARTIAL]),
                    Order.order_type == OrderTypeEnum.LIMIT,
                    Order.original_amount > Order.executed_amount
                )
            ).order_by(Order.price.desc()).limit(depth).all()
            
            # 获取卖单（按价格升序）
            sell_orders = db.session.query(Order).filter(
                and_(
                    Order.pair_id == pair_id,
                    Order.side == OrderSideEnum.SELL,
                    Order.status.in_([OrderStatusEnum.PENDING, OrderStatusEnum.PARTIAL]),
                    Order.order_type == OrderTypeEnum.LIMIT,
                    Order.original_amount > Order.executed_amount
                )
            ).order_by(Order.price.asc()).limit(depth).all()
            
            # 格式化订单簿数据
            def format_orders(orders):
                result = []
                for order in orders:
                    remaining_amount = order.original_amount - order.executed_amount
                    result.append({
                        'order_id': str(order.id),
                        'price': str(order.price),
                        'amount': str(remaining_amount),
                        'total': str(order.price * remaining_amount)
                    })
                return result
            
            return {
                'pair_id': pair_id,
                'bids': format_orders(buy_orders),  # 买单
                'asks': format_orders(sell_orders), # 卖单
               # 'timestamp': db.func.unix_timestamp()
            }
            
        except Exception as e:
            current_app.logger.error(f"获取订单簿失败: {e}")
            return {
                'pair_id': pair_id,
                'bids': [],
                'asks': [],
                'timestamp': 0
            }
    
    @classmethod
    def get_recent_trades(cls, pair_id: int, limit: int = 50) -> List[dict]:
        """获取最近成交记录"""
        try:
            from app.models.order import OrderMatch
            
            # 查询最近的成交记录
            matches = db.session.query(OrderMatch).join(
                Order, OrderMatch.order_id == Order.id
            ).filter(
                Order.pair_id == pair_id
            ).order_by(OrderMatch.created_at.desc()).limit(limit).all()
            
            # 格式化成交数据
            trades = []
            for match in matches:
                trades.append({
                    'id': match.id,
                    'price': str(match.price),
                    'amount': str(match.amount),
                    'timestamp': int(match.created_at.timestamp() * 1000),
                    'side': 'buy' if match.taker_order_id else 'sell'  # 简化处理
                })
            
            return trades
            
        except Exception as e:
            current_app.logger.error(f"获取成交记录失败: {e}")
            return []
