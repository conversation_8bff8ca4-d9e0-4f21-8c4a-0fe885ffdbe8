from decimal import Decimal

from app.models import db
from app.models.asset import AssetFreezeLog
from app.models.asset import UserAsset, AssetTransaction, TransactionTypeEnum, TransactionStatusEnum
from app.models.blockchain import AssetType
from app.utils.errors import BusinessException


class AssetService:

    @staticmethod
    def get_balance(user_id: int, asset_type_id: int) -> Decimal:
        """获取可用资产余额"""
        asset = db.session.query(UserAsset).filter_by(
            user_id=user_id,
            asset_type_id=asset_type_id
        ).with_for_update().first()

        return asset.available_balance if asset else Decimal(0)

    @staticmethod
    def freeze_asset(user_id: int,
                     asset_type_id: int,
                     freeze_amount: Decimal,
                     related_order_id: str,
                     remark: str = ""):
        """冻结资产"""
        asset = db.session.query(UserAsset).filter_by(
            user_id=user_id,
            asset_type_id=asset_type_id
        ).with_for_update().first()

        if not asset or asset.available_balance < freeze_amount:
            raise BusinessException("可用余额不足", 500)

        # 更新资产余额
        asset.available_balance -= freeze_amount
        asset.frozen_balance += freeze_amount

        # 记录冻结日志
        freeze_log = AssetFreezeLog(
            user_id=user_id,
            asset_type_id=asset_type_id,
            amount=freeze_amount,
            operation_type="FREEZE",
            related_order_id=related_order_id,
            remark=remark
        )
        db.session.add(freeze_log)

    @staticmethod
    def unfreeze_asset(user_id: int,
                       asset_type_id: int,
                       unfreeze_amount: Decimal,
                       related_order_id: str,
                       remark: str = ""):
        """解冻资产"""
        with db.session.begin_nested():
            asset = db.session.query(UserAsset).filter_by(
                user_id=user_id,
                asset_type_id=asset_type_id
            ).with_for_update().first()

            if not asset or asset.frozen_balance.normalize() < unfreeze_amount.normalize():
                raise BusinessException("冻结余额不足无法解冻", 500)

            # 更新资产余额
            asset.frozen_balance -= unfreeze_amount
            asset.available_balance += unfreeze_amount

            # 记录解冻日志
            unfreeze_log = AssetFreezeLog(
                user_id=user_id,
                asset_type_id=asset_type_id,
                amount=unfreeze_amount,
                related_order_id=related_order_id,
                operation_type="UNFREEZE",
                remark=remark
            )
            db.session.add(unfreeze_log)

    @staticmethod
    def add_or_create_user_asset(user_id: int,
                                 asset_type_id: int,
                                 amount: Decimal = Decimal('0'),
                                 fee: Decimal = Decimal('0'),
                                 remark='',
                                 reference_id='SYSTEM_OPERATE'):
        """创建用户资产记录

        Args:
            user_id: 用户ID
            asset_type_id: 资产类型ID
            amount: 操作余额，默认为0
            fee: 手续费，默认为 0
            remark: 备注
            reference_id: 关联ID
        Returns:
            UserAsset: 创建的用户资产记录

        Raises:
            BusinessException: 当资产类型不存在或用户资产已存在时
        """
        with db.session.begin_nested():
            # 验证资产类型是否存在
            asset_type = db.session.get(AssetType, asset_type_id)
            if not asset_type:
                raise BusinessException("资产类型不存在", 404)

            # 检查用户资产是否已存在
            existing_asset = db.session.query(UserAsset).filter_by(
                user_id=user_id,
                asset_type_id=asset_type_id
            ).first()

            if not existing_asset:
                # 创建用户资产记录
                user_asset = UserAsset(
                    user_id=user_id,
                    asset_type_id=asset_type_id,
                    available_balance=amount - fee,
                    frozen_balance=Decimal('0'),
                    occupied_balance=fee,
                )
                db.session.add(user_asset)
            else:
                existing_asset.available_balance += (amount - fee)
                # 手续费处理，暂时转到占用
                existing_asset.occupied_balance += fee
                user_asset = existing_asset
                db.session.add(existing_asset)

            # 如果有操作余额，记录交易流水
            if amount > 0:
                transaction = AssetTransaction(
                    user_id=user_id,
                    asset_type_id=asset_type_id,
                    amount=amount,
                    balance_before=Decimal('0'),
                    balance_after=amount,
                    transaction_type=TransactionTypeEnum.DEPOSIT,
                    reference_id=reference_id,
                    status=TransactionStatusEnum.SUCCESS,
                    extend_field={"remark": remark, "fee": str(fee)}
                )
                db.session.add(transaction)
        return user_asset

    @staticmethod
    def subtract_balance(user_id: int, asset_type_id: int, amount: Decimal,
                         transaction_type: TransactionTypeEnum = TransactionTypeEnum.WITHDRAW,
                         reference_id: str = "", remark: str = ""):
        """减少用户资产余额

        Args:
            user_id: 用户ID
            asset_type_id: 资产类型ID
            amount: 减少的金额（必须为正数）
            transaction_type: 交易类型
            reference_id: 关联ID
            remark: 备注

        Returns:
            UserAsset: 更新后的用户资产记录

        Raises:
            BusinessException: 当金额无效或余额不足时
        """
        if amount <= 0:
            raise BusinessException("减少金额必须大于0", 400)

        with db.session.begin_nested():
            asset = db.session.query(UserAsset).filter_by(
                user_id=user_id,
                asset_type_id=asset_type_id
            ).with_for_update().first()

            if not asset:
                raise BusinessException("用户资产不存在", 404)

            if asset.available_balance < amount:
                raise BusinessException("not enough", 400)

            # 记录变更前余额
            balance_before = asset.available_balance

            # 更新余额
            asset.available_balance -= amount
            balance_after = asset.available_balance

            # 记录交易流水
            transaction = AssetTransaction(
                user_id=user_id,
                asset_type_id=asset_type_id,
                amount=-amount,  # 负数表示减少
                balance_before=balance_before,
                balance_after=balance_after,
                transaction_type=transaction_type,
                reference_id=reference_id or f"SUB_{transaction_type.value}",
                status=TransactionStatusEnum.SUCCESS,
                extend_field={"remark": remark} if remark else None
            )
            db.session.add(transaction)

        return asset
