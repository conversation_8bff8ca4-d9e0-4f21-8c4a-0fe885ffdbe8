from datetime import datetime, timezone
from typing import Dict
from zoneinfo import ZoneInfo

from app.enums.biz_enums import DeviceStatus
from app.models.device import Device
from app.models.device_project import DeviceProject
from app.models.metrics import ServiceMetricsDetail


class StatusAnalysisService:
    @staticmethod
    def analyze_status(device_id: int) -> Dict:
        # 获取设备基础信息
        device = Device.query.get(device_id)
        if not device:
            return {'error': 'Device not found'}

        # 获取关联项目状态
        projects = DeviceProject.query.filter_by(device_id=device_id).all()

        # 获取最新指标数据（最近10条）
        latest_metrics = ServiceMetricsDetail.query.filter(
            ServiceMetricsDetail.device_id == device_id
        ).order_by(ServiceMetricsDetail.updated_at.desc()).all()

        # 状态归因逻辑
        analysis = {
            'device_status_code': device.status,
            'primary_reason': '',
            'detail': {
                'project_states': [],
                'metrics_status': len(latest_metrics) > 0,
                'last_metrics_time': None,
                'offline_days': None
            }
        }

        global_recent_metrics = next((metric for metric in latest_metrics), None)
        if global_recent_metrics:
            analysis['detail']['last_metrics_time'] = global_recent_metrics.updated_at.strftime("%Y-%m-%dT%H:%M:%SZ")

        # 补充项目状态详情
        for p in projects:
            project_name = p.project.name
            project_info = {
                'project_id': p.id,
                'project_name': project_name,
                'state': p.state,
                'last_state_change': p.updated_at.isoformat(),
            }

            if latest_metrics:
                project_metrics = [m for m in latest_metrics if m.service_name.startswith(project_name)]
                project_info['state_reason'] = StatusAnalysisService._analyze_project_state(p, project_metrics)

                recent_metrics = next((metric for metric in latest_metrics if metric.service_name.startswith(project_name)), None)
                if recent_metrics:
                    project_info['status_code'] = recent_metrics.status_code
                    project_info['status_msg'] = recent_metrics.status_msg
                    project_info['last_metrics_time'] = recent_metrics.updated_at.strftime("%Y-%m-%dT%H:%M:%SZ")

            analysis['detail']['project_states'].append(project_info)

        # 分析设备状态原因
        if device.status == DeviceStatus.FAILURE.code:
            analysis['primary_reason'] = '设备连续15分钟未上报数据'
            analysis['device_status_str'] = DeviceStatus.FAILURE.desc
        elif device.status == DeviceStatus.OFFLINE.code:
            analysis['device_status_str'] = DeviceStatus.OFFLINE.desc
            if latest_metrics:
                last_active = max([m.updated_at for m in latest_metrics])
                last_active = last_active.replace(tzinfo=ZoneInfo('UTC'))
                offline_days = (datetime.now(timezone.utc) - last_active).days
                analysis['detail']['offline_days'] = max(offline_days, 0)
                analysis['primary_reason'] = f'设备已离线超过{offline_days}天'
            else:
                analysis['primary_reason'] = '设备从未上报过运行指标'

        return analysis

    @classmethod
    def _analyze_project_state(cls, project: DeviceProject, metrics: list) -> str:
        if project.state == 'running':
            return '项目服务正常运行中' if metrics else '项目状态与指标数据不一致'
        elif project.state == 'stopped':
            return '服务停止：连续两个检测周期未收到运行指标'
        elif project.state == 'updated':
            return '等待服务启动：配置已完成但尚未收到运行指标'
        return '项目尚未完成初始化配置'
