"""
区块链监听服务管理器
在 Flask 应用启动时启动区块链监听服务，实现一直监听
"""

import asyncio
import threading
import time
from datetime import datetime
from typing import Dict, Optional

from flask import current_app

from app.services.blockchain_listener_service import BlockchainListenerService


class BlockchainListenerManager:
    """区块链监听服务管理器"""

    _instance = None

    @classmethod
    def get_instance(cls):
        if not cls._instance:
            cls._instance = BlockchainListenerManager()
        return cls._instance

    def __init__(self):
        self.is_running = False
        self.listener_thread: Optional[threading.Thread] = None
        self.loop: Optional[asyncio.AbstractEventLoop] = None
        self.app = None  # 添加 app 实例变量

    def start_background(self, app=None):
        """在后台线程中启动监听服务"""
        if self.is_running:
            current_app.logger.warning("⚠️ 区块链监听服务已在运行中")
            return
        if app:
            self.app = app  # 存储 app 实例
        current_app.logger.info("🚀 启动区块链监听服务...")

        # 创建后台线程
        self.listener_thread = threading.Thread(
            target=self._run_listener_loop,
            daemon=True,  # 守护线程，主程序退出时自动退出
            name="BlockchainListener"
        )

        self.is_running = True
        self.listener_thread.start()

        current_app.logger.info("✅ 区块链监听服务已在后台启动")

    def _run_listener_loop(self):
        """在独立线程中运行异步监听循环"""
        try:
            # 创建新的事件循环
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)

            # 运行监听服务
            if self.app:
                with self.app.app_context():
                    self.loop.run_until_complete(self._start_listeners())
            else:
                current_app.logger.error("❌ Flask app instance not available for listener loop.")

        except Exception as e:
            current_app.logger.error(f"❌ 区块链监听服务异常: {e}")
            self.is_running = False
        finally:
            if self.loop:
                self.loop.close()

    async def _start_listeners(self):
        """启动所有区块链监听器"""
        try:
            # 等待一下确保 Flask 应用完全启动
            await asyncio.sleep(2)

            current_app.logger.info("🔗 开始连接区块链节点...")

            # 启动监听服务
            blockchain_listener = BlockchainListenerService.get_instance()
            await blockchain_listener.start_all_listeners()

        except Exception as e:
            current_app.logger.error(f"❌ 启动区块链监听器失败: {e}")
            self.is_running = False

    def stop(self):
        """停止监听服务"""
        if not self.is_running:
            return

        current_app.logger.info("🛑 正在停止区块链监听服务...")

        self.is_running = False
        blockchain_listener = BlockchainListenerService.get_instance()
        blockchain_listener.stop_all_listeners()

        if self.loop and not self.loop.is_closed():
            # 停止事件循环
            self.loop.call_soon_threadsafe(self.loop.stop)

        if self.listener_thread and self.listener_thread.is_alive():
            # 等待线程结束
            self.listener_thread.join(timeout=5)

        current_app.logger.info("✅ 区块链监听服务已停止")

    def get_status(self) -> Dict:
        """获取监听服务状态"""
        blockchain_listener = BlockchainListenerService.get_instance()
        return {
            "is_running": self.is_running,
            "thread_alive": self.listener_thread.is_alive() if self.listener_thread else False,
            "listener_running": blockchain_listener.is_running if hasattr(blockchain_listener, 'is_running') else False,
            "start_time": getattr(self, 'start_time', None)
        }


def start_blockchain_listeners_background(app):
    """启动区块链监听服务（后台模式）"""
    with app.app_context():
        try:
            # 启动后台监听
            BlockchainListenerManager.get_instance().start_background(app)
        except Exception as e:
            current_app.logger.error(f"❌ 启动区块链监听服务失败: {e}")


def stop_blockchain_listeners():
    """停止区块链监听服务"""
    BlockchainListenerManager.get_instance().stop()


def get_listener_status():
    """获取监听服务状态"""
    return BlockchainListenerManager.get_instance().get_status()


# 优雅关闭处理
import atexit
import signal


def cleanup_listeners():
    """清理监听服务"""
    if BlockchainListenerManager.get_instance().is_running:
        current_app.logger.info("🧹 清理区块链监听服务...")
        BlockchainListenerManager.get_instance().stop()


# 注册清理函数
atexit.register(cleanup_listeners)


# 信号处理
def signal_handler(signum, frame):
    current_app.logger.info(f"📡 收到信号 {signum}，正在关闭区块链监听服务...")
    cleanup_listeners()


signal.signal(signal.SIGTERM, signal_handler)
signal.signal(signal.SIGINT, signal_handler)


class HealthChecker:
    """监听服务健康检查"""

    _instance = None

    @classmethod
    def get_instance(cls):
        if not cls._instance:
            cls._instance = HealthChecker()
        return cls._instance

    def __init__(self):
        self.manager = BlockchainListenerManager.get_instance()
        self.last_check = None
        self.check_interval = 60  # 60秒检查一次

    def check_health(self) -> Dict:
        """检查监听服务健康状态"""
        now = datetime.now()

        status = {
            "healthy": False,
            "manager_running": self.manager.is_running,
            "thread_alive": False,
            "listener_active": False,
            "last_check": now.isoformat(),
            "issues": []
        }

        # 检查管理器状态
        if not self.manager.is_running:
            status["issues"].append("监听管理器未运行")
            return status

        # 检查线程状态
        if self.manager.listener_thread and self.manager.listener_thread.is_alive():
            status["thread_alive"] = True
        else:
            status["issues"].append("监听线程未运行")

        # 检查监听器状态
        blockchain_listener = BlockchainListenerService.get_instance()
        if hasattr(blockchain_listener, 'is_running') and blockchain_listener.is_running:
            status["listener_active"] = True
        else:
            status["issues"].append("区块链监听器未激活")

        # 综合判断健康状态
        status["healthy"] = (
                status["manager_running"] and
                status["thread_alive"] and
                status["listener_active"]
        )

        self.last_check = now
        return status

    def auto_restart_if_needed(self):
        """如果需要，自动重启监听服务"""
        health = self.check_health()

        if not health["healthy"]:
            current_app.logger.warning(f"⚠️ 检测到监听服务异常: {health['issues']}")

            # 尝试重启
            try:
                current_app.logger.info("🔄 尝试重启区块链监听服务...")
                self.manager.stop()
                time.sleep(2)
                self.manager.start_background()
                current_app.logger.info("✅ 区块链监听服务重启成功")
            except Exception as e:
                current_app.logger.error(f"❌ 重启失败: {e}")


# 全局健康检查器
health_checker = HealthChecker().get_instance()


def get_health_status():
    """获取健康状态"""
    return health_checker.check_health()


def restart_listeners_if_needed():
    """如果需要，重启监听服务"""
    health_checker.auto_restart_if_needed()
