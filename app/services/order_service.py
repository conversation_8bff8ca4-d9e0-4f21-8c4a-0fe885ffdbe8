from decimal import Decimal

from flask import current_app

from app.models import db
from app.models.blockchain import TradingPair
from app.models.order import Order, OrderTypeEnum, OrderSideEnum, OrderStatusEnum, OrderSourceEnum, OrderLog, OrderMatch
from app.services.asset_service import AssetService
from app.utils.asset import TAKER_FEE_RATE, calculate_order_freeze
from app.utils.errors import BusinessException


class OrderService:

    @staticmethod
    def create_order(create_order_request):
        """Create new order with comprehensive validation

        Args:
            create_order_request : Order creation data

        Returns:
            Order: Created order instance

        Raises:
            BusinessException: On validation failure
        """
        with db.session.begin_nested():
            # 参数校验
            OrderService._validate_request(create_order_request)

            # 创建订单实体
            new_order = Order(
                user_id=create_order_request['user_id'],
                order_type=create_order_request['order_type'],
                side=create_order_request['side'],
                pair_id=create_order_request['pair_id'],
                price=Decimal(str(create_order_request['price'])),
                original_amount=Decimal(str(create_order_request['amount'])),
                status=OrderStatusEnum.PENDING,
                order_source=create_order_request.get('order_source', OrderSourceEnum.CREATE)
            )
            db.session.add(new_order)
            db.session.flush()  # 提前生成ID

            # 冻结时都按 taker 的费用冻结 - 冻结卖出资产数量 或 买入资产数量 * 价格
            asset_type_id = OrderService._get_target_asset_type_id(create_order_request)
            freeze_amount = calculate_order_freeze(new_order.original_amount, new_order.price, TAKER_FEE_RATE, new_order.side)
            AssetService.freeze_asset(
                user_id=create_order_request['user_id'],
                asset_type_id=asset_type_id,
                freeze_amount=freeze_amount,
                related_order_id=str(new_order.id),
                remark=f"ORDER_FREEZE_{create_order_request['order_type']}",
            )

            # 记录操作日志
            OrderLog.create(
                order_id=new_order.id,
                status_before=None,
                status_after=OrderStatusEnum.PENDING,
                operator=create_order_request['user_id'],
                remark="订单创建"
            )

            from app.services.matching_engine import MatchingEngine
            target_order_id = create_order_request.get('target_order_id', '')
            if target_order_id:
                target_order = db.session.get(Order, target_order_id)
                if target_order:
                    matches = MatchingEngine.matching_order(new_order, target_order)
                    if matches:
                        db.session.refresh(new_order)
                    else:
                        # 如果是指定订单交易，则直接回滚
                        raise BusinessException("订单交易失败")
            # todo
            # 如果是创建订单，目前先不匹配订单
            # else:
            #     try:
            #         # 尝试撮合（在独立事务中）
            #         matches = MatchingEngine.process_new_order(new_order)
            #         # 如果有成交，刷新订单状态
            #         if matches:
            #             db.session.refresh(new_order)
            #     except Exception as e:
            #         # 撮合失败不影响订单创建
            #         current_app.logger.error(f"订单撮合失败，但订单已创建: {str(e)}")
        db.session.commit()
        return new_order

    @staticmethod
    def _get_target_asset_type_id(request):
        # 根据买卖方向获取目标资产类型
        trading_pair = db.session.get(TradingPair, request['pair_id'])
        if not trading_pair:
            raise BusinessException("交易对不存在", 404)
        return trading_pair.quote_asset_id if request['side'] == OrderSideEnum.BUY.value else trading_pair.base_asset_id

    @staticmethod
    def _validate_request(request):
        if request['order_type'] == OrderTypeEnum.LIMIT.value and not request.get('price'):
            raise BusinessException("限价单必须指定价格", 400)
        if not request.get('amount') or Decimal(str(request['amount'])) <= 0:
            raise BusinessException("订单数量必须大于0", 400)
        if not request.get('price') or Decimal(str(request['price'])) <= 0:
            raise BusinessException("订单价格必须大于0", 400)
        if not request.get('pair_id'):
            raise BusinessException("交易对ID不能为空", 400)
        # 验证交易对是否存在
        trading_pair = db.session.get(TradingPair, request['pair_id'])
        if not trading_pair or trading_pair.status != 'ACTIVE':
            raise BusinessException("交易对不存在或未启用", 404)

    @staticmethod
    def get_orders(query_params):
        """分页查询订单"""
        query = db.session.query(Order)

        # 基础过滤条件
        query = query.filter(Order.user_id == query_params['user_id'])

        if query_params.get('status'):
            query = query.filter(Order.status == query_params['status'])
        if query_params.get('start_time'):
            query = query.filter(Order.created_at >= query_params['start_time'])
        if query_params.get('end_time'):
            query = query.filter(Order.created_at <= query_params['end_time'])

        # 分页处理
        total = query.count()
        offset = (query_params['page'] - 1) * query_params['size']
        items = query.order_by(Order.created_at.desc()).offset(offset).limit(query_params['size']).all()

        return {
            'records': items,
            'total': total,
            'page': query_params['page'],
            'size': query_params['size']
        }

    @staticmethod
    def get_trading_hall_orders(query_params):
        """查询交易大厅订单（所有用户的PARTIAL/PENDING状态订单）"""
        from app.models.blockchain import AssetType, AssetTypeEnum

        query = db.session.query(Order)

        # 只查询PARTIAL和PENDING状态的订单
        status_list = query_params.get('status_list', ['PARTIAL', 'PENDING'])
        query = query.filter(Order.status.in_(status_list))

        # 按交易对过滤
        if query_params.get('pair_id'):
            query = query.filter(Order.pair_id == query_params['pair_id'])

        # 按资产类型过滤
        if query_params.get('asset_type'):
            asset_type = query_params['asset_type']
            # 通过交易对关联查询资产类型
            query = query.join(TradingPair, Order.pair_id == TradingPair.id)
            if asset_type == 'POINTS':
                query = query.join(AssetType, TradingPair.base_asset_id == AssetType.id)
                query = query.filter(AssetType.type == AssetTypeEnum.POINTS)
            elif asset_type == 'TOKEN':
                query = query.join(AssetType, TradingPair.base_asset_id == AssetType.id)
                query = query.filter(AssetType.type == AssetTypeEnum.TOKEN)

        # 分页处理
        total = query.count()
        offset = (query_params['page'] - 1) * query_params['size']
        items = query.order_by(Order.created_at.desc()).offset(offset).limit(query_params['size']).all()

        return {
            'records': items,
            'total': total,
            'page': query_params['page'],
            'size': query_params['size']
        }

    @staticmethod
    def get_user_history_orders(query_params):
        """查询用户历史订单（通过create创建的订单）"""
        from app.models.blockchain import AssetType, AssetTypeEnum

        query = db.session.query(Order)

        # 基础过滤条件
        query = query.filter(Order.user_id == query_params['user_id'])

        # 按订单来源过滤
        if query_params.get('order_source'):
            query = query.filter(Order.order_source == query_params['order_source'])

        # 按交易对过滤
        if query_params.get('pair_id'):
            query = query.filter(Order.pair_id == query_params['pair_id'])

        # 按资产类型过滤
        if query_params.get('asset_type'):
            asset_type = query_params['asset_type']
            # 通过交易对关联查询资产类型
            query = query.join(TradingPair, Order.pair_id == TradingPair.id)
            if asset_type == 'POINTS':
                query = query.join(AssetType, TradingPair.base_asset_id == AssetType.id)
                query = query.filter(AssetType.type == AssetTypeEnum.POINTS)
            elif asset_type == 'TOKEN':
                query = query.join(AssetType, TradingPair.base_asset_id == AssetType.id)
                query = query.filter(AssetType.type == AssetTypeEnum.TOKEN)

        # 时间过滤
        if query_params.get('start_time'):
            query = query.filter(Order.created_at >= query_params['start_time'])
        if query_params.get('end_time'):
            query = query.filter(Order.created_at <= query_params['end_time'])

        # 分页处理
        total = query.count()
        offset = (query_params['page'] - 1) * query_params['size']
        items = query.order_by(Order.created_at.desc()).offset(offset).limit(query_params['size']).all()

        return {
            'records': items,
            'total': total,
            'page': query_params['page'],
            'size': query_params['size']
        }

    @staticmethod
    def get_user_trade_records(query_params):
        """查询用户买卖记录（通过instant_trade创建的订单）"""
        from app.models.blockchain import AssetType, AssetTypeEnum

        query = db.session.query(Order)

        # 基础过滤条件
        query = query.filter(Order.user_id == query_params['user_id'])

        # 按订单来源过滤
        if query_params.get('order_source'):
            query = query.filter(Order.order_source == query_params['order_source'])

        # 按资产类型过滤
        if query_params.get('asset_type'):
            asset_type = query_params['asset_type']
            # 通过交易对关联查询资产类型
            query = query.join(TradingPair, Order.pair_id == TradingPair.id)
            if asset_type == 'POINTS':
                query = query.join(AssetType, TradingPair.base_asset_id == AssetType.id)
                query = query.filter(AssetType.type == AssetTypeEnum.POINTS)
            elif asset_type == 'TOKEN':
                query = query.join(AssetType, TradingPair.base_asset_id == AssetType.id)
                query = query.filter(AssetType.type == AssetTypeEnum.TOKEN)

        # 时间过滤
        if query_params.get('start_time'):
            query = query.filter(Order.created_at >= query_params['start_time'])
        if query_params.get('end_time'):
            query = query.filter(Order.created_at <= query_params['end_time'])

        # 分页处理
        total = query.count()
        offset = (query_params['page'] - 1) * query_params['size']
        items = query.order_by(Order.created_at.desc()).offset(offset).limit(query_params['size']).all()

        return {
            'records': items,
            'total': total,
            'page': query_params['page'],
            'size': query_params['size']
        }

    @staticmethod
    def cancel_order(order_id, user_id):
        """取消订单核心逻辑"""
        with db.session.begin_nested():
            order = db.session.get(Order, order_id)
            if not order:
                raise BusinessException("订单不存在", 404)

            # 权限校验
            if int(user_id) != order.user_id:
                raise BusinessException("无权取消他人订单", 403)

            # 状态校验
            if order.status in [OrderStatusEnum.FILLED.value, OrderStatusEnum.CANCELED.value]:
                raise BusinessException("订单状态不允许取消", 400)

            # 计算可取消数量
            cancelable_amount = Decimal(str(order.original_amount)) - Decimal(str(order.executed_amount))
            if cancelable_amount <= 0:
                raise BusinessException("订单已全部成交，无法取消", 400)

            # 更新订单状态
            order.status = OrderStatusEnum.CANCELED
            db.session.flush()

            unfreeze_amount = calculate_order_freeze(cancelable_amount, order.price, TAKER_FEE_RATE, order.side)
            # 释放冻结资产
            if cancelable_amount > 0:
                asset_type_id = OrderService._get_release_asset_type(order)
                AssetService.unfreeze_asset(
                    user_id=order.user_id,
                    asset_type_id=asset_type_id,
                    related_order_id=order.id,
                    unfreeze_amount=unfreeze_amount,
                    remark="ORDER_CANCEL"
                )
        db.session.commit()
        return order

    @staticmethod
    def _get_release_asset_type(order):
        trading_pair = db.session.get(TradingPair, order.pair_id)
        if not trading_pair:
            raise BusinessException("交易对不存在", 404)
        return trading_pair.quote_asset_id if order.side == OrderSideEnum.BUY.value else trading_pair.base_asset_id


    @staticmethod
    def get_trading_pair_info(pair_id):
        """获取交易对信息"""
        try:
            trading_pair = db.session.get(TradingPair, pair_id)
            if trading_pair:
                return {
                    'id': trading_pair.id,
                    'pair_name': trading_pair.pair_name,
                    'base_asset_id': trading_pair.base_asset_id,
                    'quote_asset_id': trading_pair.quote_asset_id
                }
            return None
        except Exception:
            return None

    @staticmethod
    def get_status_text(status):
        """获取订单状态的中文描述"""
        status_map = {
            OrderStatusEnum.PENDING: '待成交',
            OrderStatusEnum.PARTIAL: '部分成交',
            OrderStatusEnum.FILLED: '已成交',
            OrderStatusEnum.CANCELED: '已取消',
            OrderStatusEnum.FAILED: '失败'
        }
        return status_map.get(status, '未知状态')

    @staticmethod
    def get_admin_orders(query_params):
        """管理员分页查询所有订单"""
        query = db.session.query(Order)

        # 管理员可以查询所有用户的订单，也可以指定用户
        if query_params.get('user_id'):
            query = query.filter(Order.user_id == query_params['user_id'])

        # 其他过滤条件
        if query_params.get('status'):
            query = query.filter(Order.status == query_params['status'])
        if query_params.get('order_type'):
            query = query.filter(Order.order_type == query_params['order_type'])
        if query_params.get('side'):
            query = query.filter(Order.side == query_params['side'])
        if query_params.get('pair_id'):
            query = query.filter(Order.pair_id == query_params['pair_id'])
        if query_params.get('start_time'):
            query = query.filter(Order.created_at >= query_params['start_time'])
        if query_params.get('end_time'):
            query = query.filter(Order.created_at <= query_params['end_time'])

        # 分页处理
        total = query.count()
        offset = (query_params['page'] - 1) * query_params['size']
        items = query.order_by(Order.created_at.desc()).offset(offset).limit(query_params['size']).all()

        return {
            'records': items,
            'total': total,
            'page': query_params['page'],
            'size': query_params['size']
        }

    @staticmethod
    def get_order_detail(order_id):
        """获取订单详情（包含成交记录和日志）"""
        order = db.session.get(Order, order_id)
        if not order:
            raise BusinessException("订单不存在", 404)

        # 获取交易对信息
        trading_pair = order.trading_pair

        # 获取成交记录
        matches = db.session.query(OrderMatch).filter(OrderMatch.order_id == order_id).all()
        match_data = [match.to_dict() for match in matches]

        # 获取订单日志
        logs = db.session.query(OrderLog).filter(OrderLog.order_id == order_id).order_by(
            OrderLog.created_at.desc()).all()
        log_data = [log.to_dict() for log in logs]

        # 组装详细信息
        order_detail = order.to_dict()
        order_detail.update({
            'pair_name': trading_pair['pair_name'] if trading_pair else f"交易对{order.pair_id}",
            'status_text': OrderService.get_status_text(order.status),
            'remaining_amount': str(Decimal(str(order.original_amount)) - Decimal(str(order.executed_amount))),
            'fill_rate': f"{float(Decimal(str(order.executed_amount)) / Decimal(str(order.original_amount)) * 100):.2f}%" if float(
                order.original_amount) > 0 else "0%",
            'matches': match_data,
            'logs': log_data
        })

        return order_detail

    @staticmethod
    def admin_cancel_order(order_id, reason="管理员操作"):
        """管理员强制取消订单"""
        with db.session.begin_nested():
            order = db.session.get(Order, order_id)
            if not order:
                raise BusinessException("订单不存在", 404)

            # 状态校验（管理员可以取消更多状态的订单）
            if order.status in [OrderStatusEnum.FILLED.value]:
                raise BusinessException("已成交的订单无法取消", 400)

            if order.status == OrderStatusEnum.CANCELED.value:
                raise BusinessException("订单已经被取消", 400)

            # 计算可取消数量
            cancelable_amount = Decimal(str(order.original_amount)) - Decimal(str(order.executed_amount))

            # 更新订单状态
            order.status = OrderStatusEnum.CANCELED.value
            db.session.flush()
            unfreeze_amount = calculate_order_freeze(cancelable_amount, order.price, TAKER_FEE_RATE, order.side)
            # 释放冻结资产
            if cancelable_amount > 0:
                asset_type_id = OrderService._get_release_asset_type(order)
                AssetService.unfreeze_asset(
                    user_id=order.user_id,
                    asset_type_id=asset_type_id,
                    unfreeze_amount=unfreeze_amount,
                    related_order_id=order.id,
                    remark=f"ADMIN_CANCEL: {reason}"
                )

            # 记录管理员操作日志
            OrderLog.create(
                order_id=order.id,
                status_before=OrderStatusEnum.PENDING,  # 简化处理
                status_after=OrderStatusEnum.CANCELED,
                operator="ADMIN",
                remark=f"管理员取消订单: {reason}"
            )

        db.session.commit()
        return order

    @staticmethod
    def build_enhanced_order_info(order: Order, depth=0) -> dict:
        """构建增强的订单信息"""
        # 基础订单信息

        # 计算订单进度
        original_amount = Decimal(str(order.original_amount))
        executed_amount = Decimal(str(order.executed_amount))
        remaining_amount = original_amount - executed_amount
        fill_rate = (executed_amount / original_amount * 100) if original_amount > 0 else Decimal('0')

        maker_orders = []
        taker_orders = []
        maker_order_ids = []
        taker_order_ids = []

        if depth == 0:
            maker_order_ids = [order_match.maker_order.id for order_match in order.taker_order_matches]
            taker_order_ids = [order_match.taker_order.id for order_match in order.maker_order_matches]

            maker_orders = [OrderService.build_enhanced_order_info(order_match.maker_order, 1) for
                            order_match in
                            order.taker_order_matches]
            taker_orders = [OrderService.build_enhanced_order_info(order_match.taker_order, 1) for
                            order_match in
                            order.maker_order_matches]

        order_info = {
            "id": order.id,
            "order_type": order.order_type.value if order.order_type else None,
            "side": order.side.value if order.side else None,
            "price": str(order.price) if order.price else None,
            'amount': str(order.original_amount),
            "original_amount": str(order.original_amount),
            "executed_amount": str(order.executed_amount),
            "executed_value": str(order.executed_value),
            "fee": str(order.fee),
            "status": order.status.value if order.status else None,
            "created_at": order.created_at.isoformat() if order.created_at else None,
            "updated_at": order.updated_at.isoformat() if order.updated_at else None,
            "trading_pair": order.trading_pair.to_dict(),
            "pair_name": order.trading_pair.pair_name,
            'remaining_amount': str(remaining_amount),
            'fill_rate': f"{float(fill_rate):.2f}%",
            'asset_flow': OrderService.build_asset_flow_info(order),
            'status_text': OrderService.get_status_text(order.status),
            'user_id': order.user_id,
            'username': order.user.username,
            'maker_orders': maker_orders,
            'taker_orders': taker_orders,
            'maker_order_ids': maker_order_ids,
            'taker_order_ids': taker_order_ids,
        }

        return order_info

    @staticmethod
    def build_asset_flow_info(order: Order) -> dict:
        """构建资产流向信息"""
        base_asset = order.trading_pair.base_asset
        quote_asset = order.trading_pair.quote_asset

        if order.side == OrderSideEnum.BUY.value:
            # 买单：支付计价资产，获得基础资产
            return {
                "direction": "BUY",
                "pay_asset": {
                    "id": quote_asset.id if quote_asset else None,
                    "name": quote_asset.name if quote_asset else "未知",
                    "type": quote_asset.type.name if quote_asset else None,
                    "amount": str(
                        Decimal(str(order.original_amount)) * Decimal(str(order.price))) if order.price else "市价"
                },
                "receive_asset": {
                    "id": base_asset.id if base_asset else None,
                    "name": base_asset.name if base_asset else "未知",
                    "type": base_asset.type.name if base_asset else None,
                    "amount": str(order.original_amount)
                }
            }
        else:
            # 卖单：支付基础资产，获得计价资产
            return {
                "direction": "SELL",
                "pay_asset": {
                    "id": base_asset.id if base_asset else None,
                    "name": base_asset.name if base_asset else "未知",
                    "type": base_asset.type.name if base_asset else None,
                    "amount": str(order.original_amount)
                },
                "receive_asset": {
                    "id": quote_asset.id if quote_asset else None,
                    "name": quote_asset.name if quote_asset else "未知",
                    "type": quote_asset.type.name if quote_asset else None,
                    "amount": str(
                        Decimal(str(order.original_amount)) * Decimal(str(order.price))) if order.price else "市价"
                }
            }

    @staticmethod
    def get_status_text(status: OrderStatusEnum) -> str:
        """获取状态描述文本"""
        status_map = {
            OrderStatusEnum.PENDING: "待成交",
            OrderStatusEnum.PARTIAL: "部分成交",
            OrderStatusEnum.FILLED: "已完成",
            OrderStatusEnum.CANCELED: "已取消",
            OrderStatusEnum.FAILED: "失败"
        }
        return status_map.get(status, "未知状态")
