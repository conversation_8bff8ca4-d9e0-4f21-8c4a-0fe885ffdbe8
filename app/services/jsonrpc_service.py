from flask import current_app

from app.services.wallet_service import WalletService


class JsonRPCService:

    @staticmethod
    def handle_request(device, request):
        """处理JSON-RPC请求"""
        try:
            # 路由处理方法
            method_handler = JsonRPCService._get_method_handler(request["method"])
            if not method_handler:
                return JsonRPCService._create_error_response(request["id"], request["method"], -32601,
                                                             "Method not found")

            # 执行处理方法
            result = method_handler(device, request)
            return result

        except Exception as e:
            current_app.logger.error(f"JSON-RPC error: {str(e)}")
            return JsonRPCService._create_error_response(request["id"], request["method"], -32603, "Internal error")

    @staticmethod
    def _get_method_handler(method_name):
        """获取方法处理器"""
        handlers = {
            "test_method": JsonRPCService._handle_echo_message,
            "sign_message": JsonRPCService._handle_sign_message,
            # 可以添加更多方法处理器
        }
        return handlers.get(method_name)

    @staticmethod
    def _handle_sign_message(device, req):
        """处理签名消息请求"""
        params = req.get("params", [])

        if not len(params) == 3:
            return JsonRPCService._create_error_response(req["id"], req["method"], -32602, "Invalid params","Missing required fields")
        signed_message, error = WalletService.sign_message(user_id=device.owner_id, chain_code=params[0], wallet_address=params[1], message=params[2])
        if error:
            return JsonRPCService._create_error_response(req["id"], req["method"], -32603, error)

        return JsonRPCService._create_success_response(req["id"], req["method"], {"signed_message": signed_message})

    @staticmethod
    def _handle_echo_message(device, req):
        echo_result = {
            "echo_method": req["method"],
            "echo_params": req.get("params")
        }
        return JsonRPCService._create_success_response(req["id"], req["method"], echo_result)

    @staticmethod
    def _create_error_response(id, method, code, message, data=None):
        """创建错误响应"""
        error = {
            "code": code,
            "message": message
        }
        if data:
            error["data"] = data
        return {
            "id": id,
            "jsonrpc": "2.0",
            "method": method,
            "error": error
        }

    @staticmethod
    def _create_success_response(id, method, result):
        return {
            "id": id,
            "jsonrpc": "2.0",
            "method": method,
            "result": result
        }
