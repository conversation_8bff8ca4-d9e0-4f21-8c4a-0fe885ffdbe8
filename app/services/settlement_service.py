"""
结算服务
实现延迟结算逻辑
"""
from decimal import Decimal
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from sqlalchemy import and_, func

from app.models import db
from app.models.asset import UserAsset
from app.models.settlement import (
    TradeFlow, UserSettlement, SettlementFlowMapping,
    TradeFlowTypeEnum, SettlementStatusEnum
)
from app.models.order import Order, OrderMatch
from app.utils.errors import BusinessException


class SettlementService:
    """结算服务"""

    @staticmethod
    def record_trade_flow(user_id: int, asset_type_id: int, flow_type: TradeFlowTypeEnum,
                         amount: Decimal, price: Decimal = None, order_id: int = None,
                         trading_pair_id: int = None, fee_amount: Decimal = None,
                         reference_id: str = None, remark: str = None) -> TradeFlow:
        """记录交易流水"""
        
        total_value = None
        if price and amount:
            total_value = price * amount
        
        flow = TradeFlow(
            user_id=user_id,
            asset_type_id=asset_type_id,
            flow_type=flow_type,
            order_id=order_id,
            trading_pair_id=trading_pair_id,
            amount=amount,
            price=price,
            total_value=total_value,
            fee_amount=fee_amount or Decimal('0'),
            reference_id=reference_id,
            remark=remark
        )
        
        db.session.add(flow)
        db.session.flush()
        
        return flow

    @staticmethod
    def calculate_withdrawable_amount(user_id: int, asset_type_id: int) -> Dict:
        """计算用户可提现金额"""
        
        # 获取用户持仓信息
        asset = db.session.query(UserAsset).filter_by(
            user_id=user_id,
            asset_type_id=asset_type_id
        ).first()
        
        if not asset:
            return {
                "available_amount": Decimal('0'),
                "total_amount": Decimal('0'),
                "frozen_amount": Decimal('0'),
                "average_cost": Decimal('0'),
                "unrealized_pnl": Decimal('0')
            }
        
        # 获取未结算的流水
        unsettled_flows = db.session.query(TradeFlow).filter(
            and_(
                TradeFlow.user_id == user_id,
                TradeFlow.asset_type_id == asset_type_id,
                TradeFlow.settlement_status == SettlementStatusEnum.PENDING
            )
        ).all()
        
        # 计算实时盈亏（需要当前市价）
        # 这里可以集成价格服务
        current_price = SettlementService._get_current_price(asset_type_id)
        unrealized_pnl = Decimal('0')
        total_balance = asset.available_balance + asset.frozen_balance
        if total_balance  > 0 and current_price > 0:
            current_value = total_balance * current_price
        
        return {
            "available_amount": asset.available_amount,
            "total_amount": asset.total_amount,
            "frozen_amount": asset.frozen_amount,
            "average_cost": asset.average_cost,
            "total_cost": asset.total_cost,
            "current_price": current_price,
            "unsettled_flows_count": len(unsettled_flows)
        }

    @staticmethod
    def create_withdrawal_settlement(user_id: int, asset_type_id: int, 
                                   withdraw_amount: Decimal, withdraw_address: str = None) -> UserSettlement:
        """创建提现结算"""
        
        # 检查可提现金额
        withdrawable_info = SettlementService.calculate_withdrawable_amount(user_id, asset_type_id)
        
        if withdraw_amount > withdrawable_info["available_amount"]:
            raise BusinessException(
                f"提现金额超过可用余额。可用: {withdrawable_info['available_amount']}, 申请: {withdraw_amount}",
                400
            )
        
        # 获取需要结算的流水
        settlement_to = datetime.now()
        unsettled_flows = db.session.query(TradeFlow).filter(
            and_(
                TradeFlow.user_id == user_id,
                TradeFlow.asset_type_id == asset_type_id,
                TradeFlow.settlement_status == SettlementStatusEnum.PENDING,
                TradeFlow.created_at <= settlement_to
            )
        ).order_by(TradeFlow.created_at).all()
        
        if not unsettled_flows:
            raise BusinessException("没有可结算的流水", 400)
        
        settlement_from = unsettled_flows[0].created_at
        
        # 计算结算结果
        settlement_result = SettlementService._calculate_settlement(unsettled_flows)
        
        # 创建结算记录
        settlement = UserSettlement(
            user_id=user_id,
            asset_type_id=asset_type_id,
            settlement_from=settlement_from,
            settlement_to=settlement_to,
            total_buy_amount=settlement_result["total_buy_amount"],
            total_buy_value=settlement_result["total_buy_value"],
            total_sell_amount=settlement_result["total_sell_amount"],
            total_sell_value=settlement_result["total_sell_value"],
            total_fee=settlement_result["total_fee"],
            net_amount=settlement_result["net_amount"],
            net_value=settlement_result["net_value"],
            realized_pnl=settlement_result["realized_pnl"],
            withdraw_amount=withdraw_amount,
            withdraw_address=withdraw_address,
            calculation_details=settlement_result["details"]
        )
        
        db.session.add(settlement)
        db.session.flush()
        
        # 创建流水映射关系
        for flow in unsettled_flows:
            mapping = SettlementFlowMapping(
                settlement_id=settlement.id,
                flow_id=flow.id
            )
            db.session.add(mapping)
            
            # 更新流水状态
            flow.settlement_status = SettlementStatusEnum.PROCESSING
            flow.settlement_id = settlement.id
        
        # 更新用户持仓
        asset = db.session.query(UserAsset).filter_by(
            user_id=user_id,
            asset_type_id=asset_type_id
        ).first()
        
        if asset:
            asset.available_balance -= withdraw_amount
            asset.frozen_balance += withdraw_amount
        
        return settlement

    @staticmethod
    def _calculate_settlement(flows: List[TradeFlow]) -> Dict:
        """计算结算结果"""
        total_buy_amount = Decimal('0')
        total_buy_value = Decimal('0')
        total_sell_amount = Decimal('0')
        total_sell_value = Decimal('0')
        total_fee = Decimal('0')
        
        details = []
        
        for flow in flows:
            flow_detail = {
                "flow_id": flow.id,
                "type": flow.flow_type.value,
                "amount": str(flow.amount),
                "price": str(flow.price) if flow.price else None,
                "value": str(flow.total_value) if flow.total_value else None,
                "fee": str(flow.fee_amount),
                "created_at": flow.created_at.isoformat()
            }
            details.append(flow_detail)
            
            if flow.flow_type == TradeFlowTypeEnum.BUY:
                total_buy_amount += flow.amount
                if flow.total_value:
                    total_buy_value += flow.total_value
            
            elif flow.flow_type == TradeFlowTypeEnum.SELL:
                total_sell_amount += flow.amount
                if flow.total_value:
                    total_sell_value += flow.total_value
            
            total_fee += flow.fee_amount
        
        # 计算净持仓和盈亏
        net_amount = total_buy_amount - total_sell_amount
        net_value = total_buy_value - total_sell_value
        realized_pnl = total_sell_value - total_buy_value - total_fee
        
        return {
            "total_buy_amount": total_buy_amount,
            "total_buy_value": total_buy_value,
            "total_sell_amount": total_sell_amount,
            "total_sell_value": total_sell_value,
            "total_fee": total_fee,
            "net_amount": net_amount,
            "net_value": net_value,
            "realized_pnl": realized_pnl,
            "details": details
        }

    @staticmethod
    def _get_current_price(asset_type_id: int) -> Decimal:
        """获取当前市价（集成价格服务）"""
        # TODO: 集成 PriceService
        # 这里需要根据 asset_type_id 获取对应的代币价格
        return Decimal('100.0')  # 临时返回固定价格

    @staticmethod
    def complete_settlement(settlement_id: int) -> UserSettlement:
        """完成结算（提现成功后调用）"""
        settlement = db.session.get(UserSettlement, settlement_id)
        if not settlement:
            raise BusinessException("结算记录不存在", 404)
        
        if settlement.status != SettlementStatusEnum.PROCESSING:
            raise BusinessException("结算状态错误", 400)
        
        # 更新结算状态
        settlement.status = SettlementStatusEnum.COMPLETED
        
        # 更新相关流水状态
        flows = db.session.query(TradeFlow).filter_by(settlement_id=settlement_id).all()
        for flow in flows:
            flow.settlement_status = SettlementStatusEnum.COMPLETED
        
        # 更新用户持仓（移除冻结金额）
        position = db.session.query(UserAsset).filter_by(
            user_id=settlement.user_id,
            asset_type_id=settlement.asset_type_id
        ).first()
        
        if position:
            position.frozen_amount -= settlement.withdraw_amount

        return settlement

    @staticmethod
    def get_user_settlement_history(user_id: int, asset_type_id: int = None, 
                                  page: int = 1, size: int = 20) -> Dict:
        """获取用户结算历史"""
        query = db.session.query(UserSettlement).filter_by(user_id=user_id)
        
        if asset_type_id:
            query = query.filter_by(asset_type_id=asset_type_id)
        
        total = query.count()
        offset = (page - 1) * size
        settlements = query.order_by(UserSettlement.created_at.desc()).offset(offset).limit(size).all()
        
        return {
            "records": [settlement.to_dict() for settlement in settlements],
            "total": total,
            "page": page,
            "size": size
        }
