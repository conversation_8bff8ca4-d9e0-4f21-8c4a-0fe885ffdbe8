# Flask配置
FLASK_ENV=development
SECRET_KEY=dev-secret-key
JWT_SECRET_KEY=dev-jwt-secret-key
ENCRYPTION_KEY=12345678901234567890123456789012

# 数据库配置 - MySQL
DATABASE_URL=mysql+pymysql://root:123456@localhost/box?charset=utf8mb4

# 测试数据库配置 - SQLite
TEST_DATABASE_URL=sqlite:///:memory:

# Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

ENC_PORT_SEED=UQ8yv92zcRxbVOuOnm7k58bJ9hndm94g

#local
#SOLANA_ENDPOINT=http://localhost:8899
#ETH_ENDPOINT=http://127.0.0.1:7545
# BSC_ENDPOINT=http://127.0.0.1:7545
# BASE_ENDPOINT=http://127.0.0.1:7545
# ARB_ENDPOINT=http://127.0.0.1:7545

# test
SOLANA_ENDPOINT=https://api.testnet.solana.com
ETH_ENDPOINT=https://sepolia.infura.io/v3/********************************
BSC_ENDPOINT=https://bsc-testnet.infura.io/v3/********************************
BASE_ENDPOINT=https://base-sepolia.infura.io/v3/********************************
ARB_ENDPOINT=https://arbitrum-sepolia.infura.io/v3/********************************

# prod
# SOLANA_ENDPOINT=https://https://api.mainnet-beta.solana.com
# ETH_ENDPOINT=https://mainnet.infura.io/v3/********************************
# BSC_ENDPOINT=https://bsc-mainnet.infura.io/v3/********************************
# BASE_ENDPOINT=https://base-mainnet.infura.io/v3/********************************
# ARB_ENDPOINT=https://arbitrum-mainnet.infura.io/v3/********************************

BASE_UBI_DOMAIN=https://pinpool.net/